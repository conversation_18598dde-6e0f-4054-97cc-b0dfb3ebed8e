{"semi": false, "singleQuote": true, "trailingComma": "es5", "tabWidth": 2, "printWidth": 120, "bracketSpacing": true, "arrowParens": "always", "endOfLine": "lf", "importOrder": ["^(react/(.*)$)|^(react$)", "^(@tanstack/react-router/(.*)$)|^(@tanstack/react-router$)", "^(@tanstack/(.*)$)|^(@tanstack$)", "", "<THIRD_PARTY_MODULES>", "", "^types$", "^@/lib/(.*)$", "^@/hooks/(.*)$", "^@/components/ui/(.*)$", "^@/components/(.*)$", "", "^[./]"], "importOrderParserPlugins": ["typescript", "jsx", "decorators-legacy"], "plugins": ["@ianvs/prettier-plugin-sort-imports"]}