import createNextIntlPlugin from 'next-intl/plugin'

/** @type {import('next').NextConfig} */
const nextConfig = {
  output: 'standalone',

  async rewrites() {
    return [
      {
        source: '/pdf-proxy/:path*',
        destination: 'https://mobilversichert.de/wp-content/uploads/:path*',
      },
    ]
  },

  experimental: {
    turbo: {
      resolveAlias: {
        canvas: './empty-module.ts',
      },
    },
    staleTimes: {
      dynamic: 120,
      static: 180,
    },
  },
  logging: {
    incomingRequests: true,
    fetches: {
      fullUrl: true,
    },
  },
  images: {
    remotePatterns: [
      {
        hostname: 'b2c-middleware.mobilversichert-dev.de',
      },
      {
        hostname: 'b2c-api.mobilversichert-dev.de',
      },
      {
        hostname: 'smartinsurtech.innosystems.net',
      },
    ],
  },
}

const withNextIntl = createNextIntlPlugin()

export default withNextIntl(nextConfig)
