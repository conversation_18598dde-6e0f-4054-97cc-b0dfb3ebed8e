# Whitelabel client

## Environment recommendations

OS: Any recent and major Linux distribution should do, X11 is required for testing.
IDE: Visual Studio Code
Runtime: NodeJS v18.15.0
Java: OpenJDK v11.0.19
Packages: OpenJFX, Google Chrome (for testing)

### VSC Plugins and configuration

- aaron-bond.better-comments - for better comments
- kamikillerto.vscode-colorize - nicely shows colors inside S/CSS file
- BrennonDenny.vsc-jetbrains-icons-enhanced - good iconset
- ahgood.shift-shift - use Shift+Shift to show command pallete like JetBrains does
- DavidAnson.vscode-markdownlint - for Markdown documents like this one.
- fabiospampinato.vscode-diff - better Diffs
- waderyan.gitblame - git blame in gutter
- GitHub.copilot - very good code assist. Works great.
- GitHub.copilot-labs - extension to the previous one, allows to generate Unit tests, finds bugs, explains and fixes code.
- hbenl.vscode-test-explorer - see unit test results directly in IDE
- lucono.karma-test-explorer - Karma support for previous plugin
- esbenp.prettier-vscode - automatically format and lint code. Has built-in eslint support
- rangav.vscode-thunder-client - great REST client with Postman collections support

I also strongly recommend using `FiraCode Nerd Font Mono` font for code ligatures.

#### Recommended VSC settings

```
{
	"editor.defaultFormatter": "esbenp.prettier-vscode",
	"editor.insertSpaces": false,
	"editor.formatOnType": false,
	"editor.formatOnSave": true,
	"editor.formatOnSaveMode": "modifications",
	"editor.cursorSurroundingLines": 10,
	"editor.renderWhitespace": "all",
	"editor.fontLigatures": true,
	"editor.accessibilitySupport": "off",
	"editor.acceptSuggestionOnCommitCharacter": false,
	"editor.cursorBlinking": "phase",
	"editor.cursorSmoothCaretAnimation": "on",
	"editor.inlineSuggest.enabled": true,
	"explorer.confirmDragAndDrop": false,
	"explorer.fileNesting.enabled": true,
	"explorer.fileNesting.patterns": {
		"*.js": "${capture}.spec.js"
	},
	"git.autofetch": true,
	"git.confirmSync": false,
	"window.commandCenter": true,
	"prettier.semi": false,
	"redhat.telemetry.enabled": false,

	"[javascript]": {
		"editor.defaultFormatter": "esbenp.prettier-vscode"
	},
	//** Rest of your config here */
}
```

## Installation

- Install required packages with `npm install`
- Put `127.0.0.1 mvp-local.mobilversichert-dev.de` in your /etc/hosts file
- Create `.environment.js` file in project root with this sample configuration for QA:

```
	Ext.define('Wl.override.Env', {
		override: 'Wl.Env',

		environment: 'qa',

		apiBaseUrl: 'https://b2c-middleware.mobilversichert-dev.de/b2c',
		ssoUrl: 'https://sso.mobilversichert-dev.de',
		ssoClientId: 'api-b2c-mv',
		homeUrl: 'http://mvp-local.mobilversichert-dev.de:1842/#welcome',

		customerBenefitsUrl: 'https://mobilversichert-dev.kundenvorteile.online',
		termsOfUseFileUrl: 'https://mobilversichert.de/wp-content/uploads/Nutzungsbedingungen_App.pdf',
		impressumFileUrl: 'https://mobilversichert.de/wp-content/uploads/Impressum_App.pdf',
		dataProtectionFileUrl: 'https://mobilversichert.de/wp-content/uploads/Datenschutzerkla%CC%88rung_App.pdf',
	})
```

## Building

Development mode: `npm run watch`
Production build: `npm run build:production VERSION`
Wordpress plugin build: `npm run build:wp-plugin VERSION` (artifact can be found in project root under name `mgis-client-whitelabel-$VERSION.zip`)

## Code of conduct

- Only <= ES6 code is allowed because nothing above is supported by internal Sencha CMD transpilers.
  It is not a big deal, few polyfills/workaround are build in to provide some newer functionality.
- Documentation needs to be done in JSDoc
- Keep original namespace when extending ExtJS class:
  - ie. Ext.form.Panel can be extended as Wl.form.Panel but not Wl.FormPanel
- Non-UI classes (like utils, api etc.) should have unit tests.
- Unit tests should be saved next to tested file
- `app` directory contains only shared business logic and UI. All additional branding should be done by creating new package in `packages/local`

### Translations

Translation keys are done uppercase, dot separated with following syntax:
`<MODULE_NAME>.<VIEW_NAME>.<COMPONENT_NAME (can be more than one)>.<STATE/VARIANT (optional)>.<PROPERTY>`

    example valid keys:

    * CONTRACTS.LIST.ITEM.TITLEA
    * CONTRACTS.LIST.ITEM.ACCEPT_BUTTON.TOOLTIP
    * CONTRACTS.LIST.ITEM.ACCEPT_BUTTON.DISABLED.TOOLTIP
    * CONTRACTS.LIST.ITEM.REVOKE_BUTTON.TEXT
    * NAVIGATION.PROFILE_MENU.PROFILE_BUTTON.TEXT
    * NAVIGATION.SIDE_MENU.LANGUAGE_MENU.ENGLISH.TEXT

    "GLOBAL" can be used as module name for shared components. Do not use shortcuts, write BUTTON, not BTN.

## Testing

### End to end

Sencha Testing Studio turned out to be very unstable so Cypress is used instead. `npm run test:e2e` runs hands-free testing flow that can be used on CI/CD. For test development use `npm run test:e2e:watch`

### Unit testing

Tests are ran by Karma with Jasmine. If you use recommended plugins for VSC there is no need to run tests from CLI, else you can run tests manually with `npm run test:tdd`

- Code coverage by Istambul.js is not working because of how ExtJS loads classes. I so not find it very usable whatsoever

## Caveats

App needs to be ran as Wordpress plugin, and few problems come with it. Environment configuration must be done through global variables, and production build
does not connect to API directly but through Wordpress REST API. It had problems with vanishing `Authorization` token so additional `Wl-Authorization` header is used
just for Wordpress build.

There is a help modal window with @@COMMIT_HASH string that is replaced during build with actual commit hash.

Contracts store is shared among few screens, so it is loaded separately on isLoggedIn state change

When you logout we first change hash to homeUrl but that does not reload page, so additional reload is required.

In Application.js there is method with internal routes hardcoded. I have honestly no idea how to get them dynamically. We need it to
redirect user to contracts/list if route is wrong.

## Troubleshooting

- Sencha CMD is VERY picky when it comes to java version. For now, JDK v11 works best.
- `Warning: Nashorn engine is planned to be removed from a future JDK release` - this can be ignored
- Sencha CMD fails because of `DSO support routines:DLFCN_LOAD:could not load the shared library:dso_dlfcn.c:185:filename(libproviders.so)` - overwrite Sencha's OpenSSL config by running `OPENSSL_CONF=/dev/null sencha <arguments>` instead
- Backend has it's own CORS validation, so just disabling it in browser won't work. You need to add custom domain to /etc/hosts (See Installation)
- production `index.html` is empty - check if you did not reformat its code. The microloader `<script>` tag must be a single line, else SenchaCMD won't recognize it
