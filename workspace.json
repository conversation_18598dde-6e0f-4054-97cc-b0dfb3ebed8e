{"apps": ["", "packages/local/Default/.sencha/temp/Themer", "packages/local/Test/.sencha/temp/Themer"], "frameworks": {"ext": {"path": "ext", "version": "*******"}}, "build": {"dir": "${workspace.dir}/build"}, "tests": {"dir": "${workspace.dir}/tests/project.json", "path": "tests/project.json", "browser": {"farms": [{"name": "Generic WebDriver", "type": "generic", "port": 443, "username": "oauth-celber-112c6", "accessKey": "e6357b53-c94c-4a8a-97c0-d3de7fc39b6d", "host": "ondemand.saucelabs.com", "sessionLimit": 2, "enableInRecorder": false, "pools": [{"name": "<PERSON><PERSON><PERSON>", "path": "Default.json"}]}]}, "archiveServers": []}, "packages": {"dir": "${workspace.dir}/packages/local,${workspace.dir}/packages", "extract": "${workspace.dir}/packages/remote"}, "name": "Client-whitelabel"}