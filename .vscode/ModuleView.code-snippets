{"Wl.view.module.View": {"scope": "javascript", "prefix": "Ext.define", "isFileTemplate": true, "body": ["Ext.define('Wl.view.module.${TM_FILENAME_BASE/(\\w*)View$/${1:/downcase}/}.$TM_FILENAME_BASE', {", "\textend: 'Wl.view.module.Module',", "\trequires: ['Wl.view.module.${TM_FILENAME_BASE/(\\w*)View$/${1:/downcase}/}.${TM_FILENAME_BASE/(\\w*)View$/${1:/capitalize}/}Controller', 'Wl.view.module.${TM_FILENAME_BASE/(\\w*)View$/${1:/downcase}/}.${TM_FILENAME_BASE/(\\w*)View$/${1:/capitalize}/}Model'],", "\tcontroller: '${TM_FILENAME_BASE/(\\w*)View$/${1:/downcase}/}-${TM_FILENAME_BASE/(\\w*)View$/${1:/downcase}/}',", "\txtype: 'wl-${TM_FILENAME_BASE/(\\w*)View$/${1:/downcase}/}',", "\tui: 'module',", "\tviewModel: {", "\t\ttype: '${TM_FILENAME_BASE/(\\w*)View$/${1:/downcase}/}-${TM_FILENAME_BASE/(\\w*)View$/${1:/downcase}/}'", "\t},", "\tbodyCls: 'wl-module-${TM_FILENAME_BASE/(\\w*)View$/${1:/downcase}/}',", "\titems: [{", "\t\txtype: 'container',", "\t\t$2", "\t}]", "})"], "description": "Empty module view"}, "Wl.view.module.Controller": {"scope": "javascript", "prefix": "Ext.define", "isFileTemplate": true, "body": ["Ext.define('Wl.view.module.${TM_FILENAME_BASE/(\\w*)Controller$/${1:/downcase}/}.$TM_FILENAME_BASE', {", "\textend: 'Ext.app.ViewController',", "\talias: 'controller.${TM_FILENAME_BASE/(\\w*)Controller$/${1:/downcase}/}-${TM_FILENAME_BASE/(\\w*)Controller$/${1:/downcase}/}',", "\troutes: {", "\t\t${TM_FILENAME_BASE/(\\w*)Controller$/${1:/downcase}/}: 'on${TM_FILENAME_BASE/(\\w*)Controller$/${1:/capitalize}/}Route',", "\t},", "", "\ton${TM_FILENAME_BASE/(\\w*)Controller$/${1:/capitalize}/}Route: function() {", "\t\t$1", "\t}", "})"], "description": "Empty module controller"}, "Wl.view.module.ViewModel": {"scope": "javascript", "prefix": "Ext.define", "isFileTemplate": true, "body": ["Ext.define('Wl.view.module.${TM_FILENAME_BASE/(\\w*)Model$/${1:/downcase}/}.$TM_FILENAME_BASE', {", "\textend: 'Ext.app.ViewModel',", "\talias: 'viewmodel.${TM_FILENAME_BASE/(\\w*)Model$/${1:/downcase}/}-${TM_FILENAME_BASE/(\\w*)Model$/${1:/downcase}/}',", "\tdata: {", "\t},", "", "})"], "description": "Empty module viewmodel"}}