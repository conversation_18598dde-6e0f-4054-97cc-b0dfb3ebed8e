Ext.define('Wl.components.panel.MainPanelController', {
  extend: 'Ext.app.ViewController',
  alias: 'controller.mainPanel',

  checkFormsIfValid: function (mainForms) {
    if (mainForms && mainForms.length > 0) {
      const mainTabPanelForForm = mainForms[0].up('vMainTabPanel')
      let foundInValidField = false

      for (let index = 0; index < mainForms.length; index++) {
        const formPanel = mainForms[index]
        if (!formPanel.skipValidation) {
          const form = formPanel.getForm()
          if (!form.isValid()) {
            const firstInvalid = form.getFields().items.find(function (f) {
              return !f.isValid()
            })

            if (firstInvalid) {
              if (mainTabPanelForForm) {
                Globals.setActiveTab(mainTabPanelForForm, formPanel.up('subPanel'))
              }

              foundInValidField = true
              firstInvalid.focus()
              formPanel.getScrollable() && formPanel.getScrollable().scrollIntoView(firstInvalid.getEl())
            }
            break
          }
        }
      }

      if (!foundInValidField) {
        return true
      } else {
        return false
      }
    } else {
      return true
    }
  },
})
