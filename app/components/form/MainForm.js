Ext.define('Wl.components.form.MainForm', {
  extend: 'Ext.form.Panel',
  alias: 'widget.mainForm',

  defaultType: 'textfield',
  cls: 'main-form',

  // flex: 1,
  layout: 'anchor',
  bodyCls: 'form-flex-grid responsive-fields',
  bodyPadding: 32,
  scrollable: true,
  trackResetOnLoad: true,

  fieldDefaults: {
    labelAlign: 'top',
    labelStyle: 'text-align: left;',
    labelSeparator: '',
    bodyStyle: '',
    msgTarget: 'under',
    cls: 'form-field-item',
    anchor: '100%',
  },

  responsiveConfig: {
    'width < 1150': {
      width: '100%',
    },
    'width >= 1150': {
      width: 740,
    },
  },

  initComponent: function () {
    var me = this
    me.callParent(arguments)
  },
})
