Ext.define('Wl.model.Provider', {
  extend: 'Ext.data.Model',

  idProperty: 'NONEXISTENT',

  fields: [
    {
      name: 'remote_id',
      type: 'string',
    },
    {
      name: 'name',
      type: 'string',
    },
    {
      name: 'name2',
      type: 'string',
    },
    {
      name: 'agencyId',
      type: 'int',
    },
    {
      name: 'categories',
      type: 'array',
      itemType: {
        type: 'string',
      },
    },
    {
      name: 'code',
      type: 'string',
    },
    {
      name: 'imgId',
      type: 'string',
    },
    {
      name: 'portals',
      type: 'string',
    },
    {
      name: 'prio',
      type: 'string',
    },
    {
      name: 'status',
      type: 'string',
    },
    {
      name: 'type',
      type: 'string',
    },
  ],
})
