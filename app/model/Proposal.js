Ext.define('Wl.model.Proposal', {
  extend: 'Wl.model.MainModel',
  alias: 'model.proposals',

  fields: [
    {
      name: 'id',
      type: 'int',
    },
    {
      name: 'recommended',
      type: 'boolean',
    },
    {
      name: 'accepted',
      type: 'boolean',
    },
    {
      name: 'acceptedAt',
      type: 'date',
    },
    {
      name: 'createdAt',
      type: 'date',
    },
    {
      name: 'proposalPackageId',
      type: 'int',
    },
    {
      name: 'updatedAt',
      type: 'date',
    },
    {
      name: 'productInfo',
    },
    {
      name: 'tariff',
      calculate: function (value) {
        const tarif = Wl.optional('productInfo.tarif', value)
        const tariflink = Wl.optional('productInfo.tariflink', value)
        const tarifbez = Wl.optional('productInfo.tarifbez', value)

        return tarif || tariflink || tarifbez
      },
      depends: ['productInfo'],
    },
    {
      name: 'insuredAmount',
      calculate: function (value) {
        return Wl.optional('productInfo.deckungssumme', value)
      },
      depends: ['productInfo'],
    },
    {
      name: 'contribution',
      calculate: function (value) {
        return Wl.optional('productInfo.beitrag', value)
      },
      depends: ['productInfo'],
    },
    {
      name: 'paymentMode',
      calculate: function (value) {
        return Wl.optional('productInfo.zahlweise', value)
      },
      depends: ['productInfo'],
    },
    {
      name: 'insurerLogo',
      calculate: function (value) {
        return Wl.optional('productInfo.logos.versicherer', value)
      },
      depends: ['productInfo'],
    },
    {
      name: 'innorataLogo',
      calculate: function (value) {
        return Wl.optional('productInfo.logos.innorata', value)
      },
      depends: ['productInfo'],
    },
    {
      name: 'startingAt',
      calculate: function (value) {
        return Wl.optional('productInfo.versicherungsbeginn', value)
      },
      depends: ['productInfo'],
    },
    {
      name: 'company',
      calculate: function (value) {
        return Wl.optional('productInfo.gesellschaft', value)
      },
      depends: ['productInfo'],
    },
  ],

  getParametersTable: function () {
    const result = []
    const productInfo = this.get('productInfo')

    let category = 1
    let property = 1
    let areCategoriesFinished = false
    let arePropertiesFinished = false

    do {
      const parameterGroupName = productInfo[`rubrik${category}_head`]
      if (parameterGroupName) {
        const parameters = []
        property = 1
        arePropertiesFinished = false
        do {
          const label = productInfo[`rubrik${category}_bezeichner${property}_head`]
          const description = productInfo[`rubrik${category}_bezeichner${property}_info`]
          const tariffText = productInfo[`rubrik${category}_bezeichner${property}_tariftext`]
          if (label) {
            parameters.push({
              description: Ext.String.escape(description || ''),
              label: Ext.String.escape(label),
              icons: tariffText ? this.getIcons(productInfo, category, property) : [],
              value: Ext.String.escape(tariffText),
            })
            property++
          } else {
            arePropertiesFinished = true
          }
        } while (!arePropertiesFinished)

        result.push({
          name: Ext.String.escape(parameterGroupName),
          parameters,
        })
        category++
      } else {
        areCategoriesFinished = true
      }
    } while (!areCategoriesFinished)

    return result
  },

  getIcons: function (productInfo, category, property) {
    const evalValue = productInfo[`rubrik${category}_bezeichner${property}_eval`]
    const sb = productInfo[`rubrik${category}_bezeichner${property}_sb`]
    const demand = productInfo[`rubrik${category}_bezeichner${property}_demand`]
    const ak = productInfo[`rubrik${category}_bezeichner${property}_ak`]
    const max = productInfo[`rubrik${category}_bezeichner${property}_max`]

    const result = []

    if (typeof demand === 'string' && !!demand) {
      result.push({
        icon: 'star',
        tooltip: Ext.String.escape(demand),
      })
    }
    if (typeof ak === 'string' && !!ak) {
      if (ak.includes('nicht die Richtlinie')) {
        result.push({
          icon: 'europe_alt_red', //akn
          tooltip: Ext.String.escape(ak),
        })
      } else {
        result.push({
          icon: 'europe_alt', // ak
          tooltip: Ext.String.escape(ak),
        })
      }
    }
    if (typeof max === 'string' && !!max) {
      result.push({
        icon: 'smile', // brightness_medium
        tooltip: Ext.String.escape(max),
      })
    }
    if (typeof sb === 'string' && !!sb) {
      result.push({
        icon: 'euro', // euro_symbol
        tooltip: Ext.String.escape(sb),
      })
    }
    if (typeof evalValue === 'string' && !!evalValue) {
      if (
        evalValue.includes('Leistung besser') ||
        evalValue.includes('Leistungswunsch erf&uuml;llt') ||
        evalValue.includes('Leistung eingeschlossen')
      ) {
        result.push({
          icon: 'plus_alt', // add_circle
          tooltip: Ext.String.escape(evalValue),
        })
      } else {
        result.push({
          icon: 'minus_alt', //checkbox_indeterminate
          tooltip: Ext.String.escape(evalValue),
        })
      }
    }

    return result
  },
})
