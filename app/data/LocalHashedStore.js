Ext.define('Wl.data.LocalHashedStore', {
  extend: 'Ext.data.Store',

  remoteHashToken: null,
  localHashToken: null,
  apiFn: null,
  remoteFilter: false,

  pageSize: 0,

  constructor: function () {
    const me = this
    me.callParent(arguments)

    if (this.proxy.id) {
      const data = Wl.util.LocalStorage.get(`store-${this.proxy.id}`)
      if (data) {
        this.setData(JSON.parse(LZString.decompressFromUTF16(data)))
      }
    }

    Wl.state.when('dbHashesLoaded').then(function (hashes) {
      const remoteHash = hashes[me.remoteHashToken]
      if (remoteHash != Wl.util.LocalStorage.get(me.localHashToken)) {
        me.apiFn().then(function (data) {
          me.removeAll()
          // there is an issue with persisitng the id field in localstorage
          // so we need to rename it to remote_id
          me.add(
            data.map((item) => {
              item.remote_id = item.id
              delete item.id
              return item
            })
          )
          Wl.util.LocalStorage.set(`store-${me.proxy.id}`, LZString.compressToUTF16(JSON.stringify(data)))

          Wl.util.LocalStorage.set(me.localHashToken, remoteHash)
        })
      }
    })
  },
})
