@include extjs-menu-ui(
  'default',
  $ui-border-style: none,
	$ui-item-separator-margin: 12px 0,
	$ui-separator-size: 2px,
	$ui-glyph-color: $base-color,
	$ui-item-icon-size: $lead-font-size,
	$ui-item-active-background-color: transparent,
	$ui-item-active-text-color: $turquoise-color,
	$ui-item-active-glyph-color: $turquoise-color,
);

// Menu mixin does not give full controll over styling so we need to override some styles

.x-menu-item-text-default {
	font-weight: 700;
	line-height: 26px;
}

.x-menu-item-icon-default:before {
	font-weight: 700;
	font-size: $lead-font-size;
}

.x-menu-item-text.x-menu-item-text-default.x-menu-item-indent-no-separator {
	margin-left: 36px;
}

.x-menu-body-default {
	padding: 20px;
	border-radius: $border-radius;
}