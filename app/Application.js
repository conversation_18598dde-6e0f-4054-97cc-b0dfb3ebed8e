Ext.Date.patterns = {
  ISO8601Long: 'Y-m-d H:i:s',
  ISO8601Short: 'Y-m-d',
  ShortDate: 'n/j/Y',
  LongDate: 'l, F d, Y',
  FullDateTime: 'l, F d, Y g:i:s A',
  MonthDay: 'F d',
  ShortTime: 'g:i A',
  LongTime: 'g:i:s A',
  SortableDateTime: 'Y-m-d\\TH:i:s',
  UniversalSortableDateTime: 'Y-m-d H:i:sO',
  YearMonth: 'F, Y',
}

// Native JSON does not support encoding of dates
Ext.USE_NATIVE_JSON = false

Ext.setEnableKeyboardMode(true)

Ext.Ajax.timeout = 3 * 60 * 1000

// @require ./initTranslations.js

Ext.define('Wl.Application', {
  extend: 'Ext.app.Application',

  name: 'Wl',

  requires: [
    'Ext.Responsive',
    'Wl.Env',
    'Wl.util.State',
    'Wl.util.OptionalChain',
    'Wl.util.Pick',
    'Wl.util.Omit',
    'Wl.util.Request',
    'Wl.util.I18n',
    'Wl.util.Globals',
    'Wl.api.StoreAjaxProxy',
  ],

  routes: {
    login: 'onLoginRoute',
    sandbox: 'onSandboxRoute',
    forgotpassword: 'onForgotPasswordRoute',
    'resetpassword|resetpassword/*': 'onResetPasswordRoute',
    'signup|signup/*': 'onSignupRoute',
    welcome: 'onWelcomeRoute',
    // 'privacypolicy|privacypolicy/*': 'onPrivacyPolicyRoute',
    '*': 'onInternalRoute',
  },

  views: [
    'Wl.form.field.PasswordField',
    'Wl.form.field.UploadField',
    'Wl.view.login.LoginView',
    'Wl.view.loading.LoadingView',
    'Wl.view.forgotpassword.ForgotPasswordView',
    'Wl.view.resetpassword.ResetPasswordView',
    'Wl.view.signup.SignupView',
    'Wl.view.privacypolicy.PrivacyPolicyView',
    'Wl.view.primarycontainer.PrimaryContainerView',
  ],

  models: ['Wl.model.Proposal'],

  stores: ['Wl.store.Categories', 'Wl.store.Providers', 'Wl.store.Products', 'Wl.store.Contracts'],

  mainView: {
    xtype: 'viewport',
    layout: 'card',
    deferredRender: true,
    activeItem: 0,
    items: [
      {
        itemId: 'loadingview',
        xtype: 'wl-loading',
      },
      {
        itemId: 'sandboxview',
        xtype: 'wl-sandbox',
      },
      {
        itemId: 'welcomeview',
        xtype: 'wl-welcome',
      },
      {
        itemId: 'loginview',
        xtype: 'wl-login',
      },
      {
        itemId: 'forgotpassword',
        xtype: 'wl-forgotpassword',
      },
      {
        itemId: 'resetpassword',
        xtype: 'wl-resetpassword',
      },
      {
        itemId: 'signupview',
        xtype: 'wl-signup',
      },
      {
        itemId: 'privacypolicyview',
        xtype: 'wl-privacypolicy',
      },
      {
        itemId: 'primaryview',
        xtype: 'wl-primarycontainer',
      },
    ],
  },

  quickTips: false,

  platformConfig: {
    desktop: {
      quickTips: true,
    },
  },

  onSandboxRoute: function () {
    this.getMainView().setActiveItem('sandboxview')
  },

  onLoginRoute: function () {
    this.getMainView().setActiveItem('loginview')
  },

  onSignupRoute: function () {
    this.getMainView().setActiveItem('signupview')
  },

  onWelcomeRoute: function () {
    this.getMainView().setActiveItem('welcomeview')
  },

  onForgotPasswordRoute: function () {
    this.getMainView().setActiveItem('forgotpassword')
  },

  onResetPasswordRoute: function () {
    this.getMainView().setActiveItem('resetpassword')
  },

  onPrivacyPolicyRoute: function () {
    const me = this
    const privacyPolicyView = me.getMainView().down('wl-privacypolicy')
    privacyPolicyView.onAccept = function () {
      me.redirectTo('contracts/list', { force: true })
    }
    this.getMainView().setActiveItem(privacyPolicyView)
  },

  onInternalRoute: function () {
    if (Wl.api.SSO.isLoggedIn()) {
      this.getMainView().setActiveItem('primaryview')
    } else if (!this.isPathAllowedAfterAgreement(Ext.util.History.getHash())) {
      if (!this.isLegalProtectionSigned()) {
        this.onPrivacyPolicyRoute()
      } else {
        this.getMainView().setActiveItem('primaryview')
      }
    } else if (!this.isPathPublic(Ext.util.History.getHash())) {
      this.redirectTo('welcome')
    }
  },

  launch: function () {
    const me = this
    const currentHash = Ext.util.History.getHash()
    Wl.state.when('isLoggedIn').then(async () => {
      Globals.addMainSpinner()
      const clientProfile = await Wl.api.Profile.getUserInfo()
      const agentProfile = await Wl.api.Agent.getById(clientProfile.agentId)

      me.getMainView().down('#primaryview').getViewModel().set({
        clientProfile: clientProfile,
        agentProfile: agentProfile,
      })

      Wl.api.DBHashes.getAll().then((response) => {
        Wl.util.LocalStorage.set('dbhashes', response)
        Wl.state.resolve('dbHashesLoaded', response)
        // check if user has agreed to the user agreement
        if (!me.isLegalProtectionSigned()) {
          me.onPrivacyPolicyRoute()
        } else if (!me.isPathAllowedAfterAgreement(currentHash)) {
          me.redirectTo('contracts/list')
        }

        // many screens depend on contracts store, so we load it here, autoLoad wont work because user can be not logged in from the beginning
        Ext.StoreMgr.lookup('contracts').load()
      })
      Globals.destroyMainSpinner()
    })

    if (!Wl.api.SSO.isLoggedIn() && !this.isPathPublic(currentHash)) {
      me.redirectTo('welcome')
    }

    Ext.GlobalEvents.on('logout', () => {
      Wl.util.LocalStorage.clear()
    })
  },

  isLegalProtectionSigned: function () {
    const clientProfile = Wl.api.SSO.getUserInfo()
    return clientProfile && clientProfile.signedLegalProtection
  },

  isPathPublic: function (path) {
    // TODO: this checks also for partial paths matching whole routes. Does not seem to be a problem for now.
    return !!Object.keys(this.getRoutes())
      .join('|')
      .match(new RegExp((path || 'EMPTY').split('/')[0]))
  },

  isPathAllowedAfterAgreement: function (path) {
    // this is a list of paths that are allowed to be accessed before user has agreed to the user agreement
    return (
      ~'contracts|offers|damages|expert|profession|settings|risk|profile'.indexOf((path || 'EMPTY').split('/')[0]) ||
      this.isPathPublic(path)
    )
  },
})
