@include extjs-button-small-ui(
  'link',
  $color: $blue-color,
  $color-over: $blue-color,
  $color-focus: $blue-color,
  $color-pressed: $blue-color,
  $color-focus-over: $blue-color,
  $color-focus-pressed: $blue-color,
  $color-disabled: $gray100-color,

  $background-color: transparent,
  $background-color-over: transparent,
  $background-color-focus: transparent,
  $background-color-pressed: transparent,
  $background-color-focus-over: transparent,
  $background-color-focus-pressed: transparent,
  $background-color-disabled: transparent
);

@include extjs-button-medium-ui(
  'secondary',
  $border-radius: $button-medium-border-radius,
  $border-width: $button-medium-border-width,

  $glyph-color: $dark-color,
  $color: $dark-color,
  $color-over: $white-color,
  $color-focus: $dark-color,
  $color-pressed: $dark-color,
  $color-focus-over: $white-color,
  $color-focus-pressed: $dark-color,
  $color-disabled: $white-color,

  $background-color: $white-color,
  $background-color-over: $dark-color,
  $background-color-focus: $dark-color,
  $background-color-pressed: $dark-color,
  $background-color-focus-over: $dark-color,
  $background-color-focus-pressed: $dark-color,
  $background-color-disabled: $gray100-color,

  $line-height: $button-medium-line-height,

  $padding: $button-medium-padding,
  $text-padding: $button-medium-text-padding
);

@include extjs-button-medium-ui(
  'trinary',
  $border-radius: $button-medium-border-radius,
  $border-width: $button-medium-border-width,

  $glyph-color: $white-color,
  $color: $white-color,
  $color-over: $white-color,
  $color-focus: $white-color,
  $color-pressed: $white-color,
  $color-focus-over: $white-color,
  $color-focus-pressed: $white-color,
  $color-disabled: $white-color,

  $background-color: $turquoise-color,
  $background-color-over: #2da198,
  $background-color-focus: #28948b,
  $background-color-pressed: #28948b,
  $background-color-focus-over: #28948b,
  $background-color-focus-pressed: #28948b,
  $background-color-disabled: $gray100-color,

  $line-height: $button-medium-line-height,

  $padding: $button-medium-padding,
  $text-padding: $button-medium-text-padding
);

@include extjs-button-large-ui(
  'danger',
  $border-radius: $button-medium-border-radius,
  $border-width: $button-medium-border-width,

  $glyph-color: $white-color,
  $color: $white-color,
  $color-over: $white-color,
  $color-focus: $white-color,
  $color-pressed: $white-color,
  $color-focus-over: $white-color,
  $color-focus-pressed: $white-color,
  $color-disabled: $white-color,

  $background-color: $danger-color,
  $background-color-over: $danger-color,
  $background-color-focus: $danger-color,
  $background-color-pressed: $danger-color,
  $background-color-focus-over: $danger-color,
  $background-color-focus-pressed: $danger-color,
  $background-color-disabled: $gray100-color,

  $line-height: $button-medium-line-height,

  $padding: $button-large-padding,
  $text-padding: $button-large-text-padding
);

@include extjs-button-small-ui('primary');

@include extjs-button-medium-ui('primary');

@include extjs-button-large-ui('primary');

.x-btn-primary-small,
.x-btn-primary-medium,
.x-btn-primary-large,
.x-btn-secondary-small,
.x-btn-secondary-medium,
.x-btn-secondary-large,
.x-btn-trinary-small,
.x-btn-trinary-medium,
.x-btn-trinary-large {
  border-radius: 40px;
}

.x-btn-over .x-btn-icon-el-secondary-medium {
  color: $white-color;
}
