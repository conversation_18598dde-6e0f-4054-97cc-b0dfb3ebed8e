@import '../sass/etc/icomoon/variables';

// Base
$enable-font-awesome: dynamic(true);
$enable-font-icons: dynamic(true);
$font-family: 'Mulish';
$font-size: dynamic(14px);
$font-icon-font-family: dynamic('icomoon', 'Font Awesome 5 Free');
$base-color: $dark-color;
$base-gradient: 'none';
$border-radius: dynamic(10px);

// Typography
$regular-font-size: dynamic($font-size);
$small-font-size: dynamic(12px);
$lead-font-size: dynamic(20px);
$tool-glyph-font-size: $lead-font-size;

// Colors
$dark-color: dynamic(#142a3a);
$blue-color: dynamic(#3966b2);
$carolina-blue-color: dynamic(#4896da);
$turquoise-color: #3bcbbf;
$white-color: #ffffff;

$light-background-color: dynamic(#f7f8f9);
$dark-background-color: dynamic(#0b1a25);

$gray50-color: dynamic(#e0e0e0);
$gray100-color: dynamic(#c3c9d3);
$gray200-color: dynamic(#8a94a5);
$gray300-color: dynamic(#4e5d79);

$danger-color: dynamic(#9a2921);
$warning-color: dynamic(#ffad0d);
$success-color: dynamic(#76b729);

$shimmer-background-color: dynamic(#f5f6f9);
$shimmer-highlight-color: dynamic(#e2e6ed);
$shimmer-gradient: dynamic(
  linear-gradient(
    97.57deg,
    $shimmer-highlight-color 37.97%,
    $shimmer-background-color 56.97%,
    $shimmer-highlight-color 79.3%
  )
);
$lightblue-gradient: dynamic(linear-gradient(180deg, #4896da 0%, #3966b2 100%));
$turquoise-gradient: dynamic(linear-gradient(180deg, #3bcbbf 0%, #28a398 100%));
$box-shadow: dynamic(0px 12px 38px rgba(0, 66, 117, 0.1));

// Panel
$body-background-color: $white-color;
$panel-body-background-color: $white-color;
$panel-header-font-size: $lead-font-size;
$panel-header-line-height: $lead-font-size;
$tool-size: $lead-font-size;
$tool-left-glyph: $icon-arrow-left $tool-glyph-font-size $font-icon-font-family;
$panel-header-text-padding: 10px;
$panel-header-padding: 20px;
$tool-opacity: 1;

//LoadMask
$loadmask-background-color: $dark-color;
$loadmask-opacity: 0.3;


// Buttons
$button-default-color: #ffffff;
$button-default-background-color: $dark-color;
// Button.scss ignores $ignore-frame-padding setting in 7.5.1 and forces frame anyway
// this is why I set border radius directly in CSS and disable it in SCSS
$button-small-border-radius: 0;
$button-medium-border-radius: 0;
$button-large-border-radius: 0;
$button-small-border-width: 0;
$button-medium-border-width: 0;
$button-large-border-width: 0;
$button-medium-padding: 12px;

$button-large-arrow-glyph: '\ff90' 24px $font-icon-font-family;
$button-medium-arrow-glyph: '\ff90' 20px $font-icon-font-family;
$button-small-arrow-glyph: '\ff90' 16px $font-icon-font-family;

// Forms
$form-field-height: dynamic(54px);
$form-field-background-color: #ffffff;
$form-field-border-color: $form-field-background-color;
$form-field-invalid-background-color: $form-field-background-color;
$form-field-invalid-border-color: $danger-color;
$form-field-focus-border-color: $turquoise-color;
$form-text-field-border-radius: $border-radius;
$form-label-font-size: 10px;
$form-error-msg-font-size: 10px;
$form-error-icon-height: 11px;
$form-error-under-padding: 4px 2px 2px 0;
$form-error-under-icon-spacing: 2px;
$form-trigger-background-color: none;
$form-trigger-width: 40px;
$form-trigger-glyph-color: $gray200-color;
$form-checkbox-label-spacing: 8px;
$form-checkbox-label-line-height: 19px;

$form-trigger-glyph: '\ff90' 16px $font-icon-font-family;

@include extjs-display-field-ui(
  'default',
  $ui-field-height: 10px
)