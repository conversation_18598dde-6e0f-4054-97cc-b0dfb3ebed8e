Ext.define('Wl.view.privacypolicy.PrivacyPolicyView', {
  extend: 'Wl.view.fullscreencontainer.FullscreenContainerView',
  xtype: 'wl-privacypolicy',

  baseCls: 'wl-privacy-policy',
  controller: 'privacypolicy-privacypolicy',

  signaturePad: null,
  signaturePadEl: null,
  submitButton: null,
  cancelButton: null,

  initComponent: function () {
    const me = this

    me.submitButton = Ext.create('Ext.Button', {
      xtype: 'button',
      itemId: 'submitSignatureButton',
      text: translate('PROFILE.SIGN_AGREEMENT_MODAL.ACCEPT_BUTTON.TEXT'),
      disabled: true,
      handler: function () {
        me.uploadSignature()
      },
    })

    me.cancelButton = Ext.create('Ext.Button', {
      xtype: 'button',
      ui: 'secondary',
      text: translate('PROFILE.SIGN_AGREEMENT_MODAL.CANCEL_BUTTON.TEXT'),
      handler: function () {
        me.onCancel()
      },
    })

    me.privacyPolicyContent = Ext.create('Ext.Component', {
      xtype: 'component',
      data: {},
      width: 550,
      tpl: `<div class='wl-privacy-policy-content'>
        <h2>Einwilligungserklärung Datenschutz</h2>
        <ol>
          <li>Präambel</li>
          <p>Der Mandant wünscht die Vermittlung und/oder Verwaltung seiner Vertragsverhältnisse gegenüber Versicherern, Bausparkassen und/oder Anlagegesellschaften und/oder sonstigen Unternehmen, mit welchen der Vermittler zusammenarbeitet, aufgrund der vereinbarten Regelungen (Auftrag/Maklervertrag) mit dem/den Vermittler(n). Zu deren Umsetzung, insbesondere der Vertragsvermittlung und -Verwaltung, soll der Vermittler alle in Betracht kommenden Daten des Mandanten verarbeiten, erhalten, verwenden, speichern, übermitteln und weitergeben dürfen.</p>
          <li>Name und Anschrift des für die Verarbeitung Verantwortlichen</li>
          <p>
            Verantwortlicher im Sinne der datenschutzrechtlichen Bestimmungen ist:<br/>
            <br/>
            {agentProfile.agencyName}<br/>
            {agentProfile.agencyCode}<br/>
            <tpl if="agentProfile.agencyPhone">Tel: {agentProfile.agencyPhone}<br/></tpl>
            <tpl if="agentProfile.agencyFax">Fax: {agentProfile.agencyFax}<br/></tpl>
            <tpl if="agentProfile.agencyEmail">E-mail: {agentProfile.agencyEmail}<br/></tpl>
          </p>
          <br/>
          <li>Name und Anschrift des Datenschutzbeauftragten</li>
          <p>
            Der Datenschutzbeauftragte des für die Verarbeitung Verantwortlichen ist:<br/>
            PROLIANCE GmbH, Leopoldstr. 21 in 80802 München<br/><br/>
            Jeder Kunde als betroffene Person kann sich jederzeit bei allen Fragen und Anregungen zum Datenschutz direkt an unseren Datenschutzbeauftragten wenden.
          </p>
          <li>Mandant</li>
          <p>
            {[translate("GLOBAL.SALUTATION."+(values.clientProfile.salutation || 'UNKNOWN'))]}
            {clientProfile.firstName} {clientProfile.lastName}
          </p>
          <li>Versicherte Person</li>
          <p>{clientProfile.firstName} {clientProfile.lastName}</p>
          <li>Rechtsgrundlage, Einwilligung in die Datenverarbeitung</li>
          <ol>
            <li>Der Kunde willigt ausdrücklich ein, dass alle personenbezogenen Daten, insbesondere die besonderen persönlichen Daten, wie z. B. die Gesundheitsdaten der zu versichernden Personen, im Rahmen der gesetzlichen Regelungen der Datenschutz-Grundverordnung (DSGVO) und des Bundesdatenschutzgesetzes (BDSG) von dem/den Vermittler(-n) gespeichert und zum Zwecke der Vermittlung und Verwaltung an die dem Kunden bekannten, kooperierenden Unternehmen weitergegeben werden dürfen.</li>
            <li>Art. 6 Abs. 1 lit. a) und b) DSGVO stellen die Rechtsgrundlagen für die Verarbeitung der personenbezogenen Daten des Kunden dar.</li>
            <li>Diese Einwilligung gilt unabhängig vom Zustandekommen des beantragten Vertrages und auch für die entsprechende Prüfung bei anderweitig zu beantragenden Versicherungsverträgen oder bei künftigen Antragstellungen des Kunden.</li>
            
            <li>Der Kunde ist zudem damit einverstanden, dass die MUNICH GENERAL INSURANCE SERVICES GmbH (www.mobilversichert.de) in Unterstützung des Versicherungsmaklers die Daten speichert und ausschließlich zum Zwecke der Vertragserfüllung nutzen darf.</li>
            <li>Der/die Vermittler dürfen die Kundendaten, insbesondere auch die Gesundheitsdaten des Kunden, zur Einholung von Stellungnahmen und Gutachten, sowie zur rechtlichen Prüfung von Ansprüchen an von Berufswegen zur Verschwiegenheit verpflichtete Personen (z.B. Anwälte und Steuerberater) weitergeben.</li>
          </ol>
            <li>Befugnis der Versicherer (der Vertragspartner)</li>
            <ol>
              <li>Der Kunde ist damit einverstanden, dass sämtliche Informationen und Daten, welche für den von ihm gewünschten Versicherungsschutz von Bedeutung sein könnten, an den potenziellen Vertragspartner (z.B. Versicherer) weitergegeben werden. Diese potenziellen Vertragspartner sind zur ordnungsgemäßen Prüfung und weiteren Vertragsdurchführung berechtigt, die vertragsrelevanten Daten - insbesondere auch die Gesundheitsdaten - im Rahmen des Vertragszweckes zu speichern und zu verwenden.</li>
              <li>Soweit es für die Eingehung und Vertragsverlängerung erforderlich ist, dürfen diese Daten, einschließlich der Gesundheitsdaten, an Rückversicherer oder Mitversicherer zur Beurteilung des vertraglichen Risikos vertraulich und anonymisiert übermittelt werden.</li>
              
            </ol>
          <li>Mitarbeiter und Vertriebspartner</li>
          <p>Der Kunde erklärt seine Einwilligung, dass alle Mitarbeiter und Erfüllungsgehilfen des Vermittlers seine personenbezogenen Daten, insbesondere auch die Gesundheitsdaten, speichern, einsehen und für die Beratung gegenüber dem Kunden und dem Versicherer verwenden dürfen. Zu den Mitarbeitern des Vermittlers zählen alle Arbeitnehmer, selbständige Handelsvertreter, Empfehlungsgeber und sonstige Erfüllungsgehilfen, ie mit dem Vermittler eine vertragliche Regelung unterhalten und die Bestimmungen des Bundesdatenschutzgesetzes beachten. Der Kunde ist damit einverstanden, dass seine personenbezogenen Daten, sein Finanzstatus und die Gesundheitsdaten an diese und künftige Mitarbeiter des Vermittlers zum Zwecke der Vertragsbetreuung weitergegeben werden und seine Mitarbeiter berechtigt sind, die Kundendaten im Rahmen des Vertragszweckes einzusehen und verarbeiten und verwenden zu dürfen.</p>
          <li>Anweisungsregelung</li>
          <p>Der Kunde weist seine bestehenden Vertragspartner (z.B. Versicherer) an, sämtliche vertragsbezogenen Daten - auch die Gesundheitsdaten - an den/die beauftragten Vermittler unverzüglich herauszugeben. Dies insbesondere zum Zwecke der Vertragsübertragung, damit der Vermittler die Überprüfung des bestehenden Vertrages durchführen kann.</p>
          <li>Dauer, für die die personenbezogenen Daten gespeichert werden</li>
          <p>Die Kundendaten werden nach Kündigung der Zusammenarbeit im Rahmen der gesetzlichen Bestimmungen, insbesondere der gesetzlichen Aufbewahrungsfristen, gelöscht. Zur Abwehr zukünftiger Schadenersatzansprüche können sich die Löschfristen entsprechend verlängern. Der Kunde ist damit einverstanden, dass sich der Löschanspruch nicht auf revisionssichere Backupsysteme bezieht und in Form einer Sperrung durchgeführt wird.</p>
          <li>Rechte des Kunden als betroffene Person</li>
          <p>Dem Kunden stehen sämtliche in Kapitel 3 (Art. 12-23) DSGVO genannten Rechte zu, insbesondere das Recht auf Auskunft, Berichtigung, Löschung, Einschränkung der Verarbeitung, Widerspruchsrecht und Recht auf Datenübertragbarkeit.</p>
          <li>Kooperationspartner</li>
          <p>Dem Kunden ist es bekannt, dass der Vermittler im Rahmen seiner auftragsgemäß übernommenen Aufgaben mit Kooperationspartnern zusammen arbeitet. Aus diesem Grunde wurden die Kooperationspartner bevollmächtigt. Zum Zwecke der auftragsgemäßen Umsetzung ist es neben der Bevollmächtigung ebenfalls erforderlich, dass der Kooperationspartner die Daten des Kunden erhält und ebenfalls im Rahmen dieser datenschutzrechtlichen Einwilligungserklärung zur Datenverwendung, Weitergabe oder</p>
          <p>Speicherung berechtigt ist. Den nachfolgend genannten Kooperationspartnern wird daher die datenschutzrechtliche Einwilligungserklärung im Umfang der hiesigen Datenschutzerklärung erteilt. Dies gilt insbesondere auch für die sensiblen persönlichen Daten, insbesondere auch die Gesundheitsdaten des Kunden. Der Kunde willigt in die Datenverwendung aufgrund dieser Datenschutzvereinbarung hinsichtlich der nachfolgend genannten Unternehmen ein:</p>
        <ol>
            <li>Munich General Insurance Services GmbH, Landsberger Str. 155, 80687 München</li>
            <li>THINKSURANCE GMBH, Rotfeder-Ring 5, 60327 Frankfurt am Main</li>
        </ol>
          <p>Der Kunde erklärt die Einwilligung der Datenweitergabe an die vorgenannt benannten Unternehmen, sofern dies zur auftragsgemäßen Erfüllung des Vermittlers erforderlich ist.</p>
          <li>Rechtsnachfolger</li>
          <ol>
            <li>Der Kunde willigt ein, dass die von dem/den Vermittler(-n) aufgrund der vorliegenden Datenschutzerklärung erhobenen, verarbeiteten und gespeicherten Informationen, Daten und Unterlagen, insbesondere auch die Gesundheitsdaten, an einen etwaigen Rechtsnachfolger des/der Vermittler bzw. einen Erwerber des Versicherungsbestandes weitergegeben werden, damit auch dieser seine vertraglichen und gesetzlichen Verpflichtungen als Rechtsnachfolger des Vermittlers erfüllen kann.</li>
            <li>Die zur Bewertung des Maklerunternehmens erforderlichen Kundendaten können auch an einen potenziellen Erwerber des Maklerunternehmens weitergeleitet werden. Besondere personenbezogene Daten, insbesondere Gesundheitsdaten im Sinne des Art. 4 Nr. 15 DSGVO, zählen nicht zu den erforderlichen Kundendaten nach Satz 1. Diese dürfen daher nicht an einen potenziellen Erwerber übermittelt werden. Eine Überlassung dieser Daten erfolgt nach Absatz 1 erst nach der tatsächlichen Veräußerung oder Rechtsnachfolge.</li>
          </ol>
          <li>Keine Datenübertragung in Drittländer</li>
          <p>Der Vermittler beabsichtigt nicht, personenbezogene Daten des Kunden in Drittländer zu übertragen.</p>
          <li>Bestehen einer automatisierten Entscheidungsfindung</li>
          <p>Der Vermittler verzichtet auf eine automatische Entscheidungsfindung oder ein Profiling.</p>
          <li>Widerruf</li>
          <p class="gray-background">Die Einwilligung zur Verwendung, Speicherung und Weitergabe aller gesammelten und vorhandenen Daten - einschließlich der Gesundheitsdaten - kann durch den Kunden jederzeit und ohne Begründung widerrufen werden. Die an der Vertragsvermittlung und/oder -Verwaltung beteiligten Unternehmen werden sofort überden Widerruf informiert und verpflichtet, unverzüglich die gesetzlichen Regelungen der DSGVO und des BDSG umzusetzen. Führt der Widerruf dazu, dass der in der Präambel geregelte Vertragszweck nicht erfüllt werden kann, endet automatisch die vereinbarte Verpflichtung der/des Vermittler(s) gegenüber der den Widerruf erklärenden Person oder Firma. Der Kunde hat jederzeit die Möglichkeit, sich beim zuständigen Landesamt für Datenschutzaufsicht (LDA) zu beschweren.</p>
          <li>Einwilligungserklärung</li>
          <p>Mit der Verwendung, Speicherung und Nutzung der besonderen persönlichen Daten, einschließlich der Gesundheitsdaten und seines Finanzstatus, im Rahmen dieser Datenschutzvereinbarung, erklärt der Kunde seine Einwilligung, die er jederzeit ohne Angabe von Gründen widerrufen kann.</p>
          <li>E-Mail-Kommunikation</li>
          <p class="gray-background">Hiermit willige ich mit meiner Unterschrift ausdrücklich ein, dass ich mit einem unverschlüsselten E-Mail zur Auftragsabwicklung einverstanden bin. Dieses Einverständnis erteile ich ausdrücklich auch für den Fall, dass in der E-Mail Nachricht besondere persönliche Daten, wie z.B. Gesundheitsdaten oder der Finanzstatus, enthalten sind. Sofern ich bereits die besonderen persönlichen Daten per unverschlüsselter E-Mail an meinen Vermittler gesandt hatte, genehmige ich die nicht verschlüsselte Kommunikation bis auf Widerruf für die Zukunft.</p>
        <br/>
          <ol><li>ja</p></ol>
        </ol>	
      </div>  `,
    })

    me.items = [
      {
        xtype: 'panel',
        layout: {
          type: 'vbox',
          pack: 'start',
          align: 'middle',
        },
        scrollable: {
          y: true,
          x: false,
        },
        defaults: {
          xtype: 'component',
          margin: 10,
        },
        padding: '15 40',

        width: 700,

        dockedItems: [
          {
            xtype: 'toolbar',
            dock: 'bottom',

            items: ['->', me.cancelButton, me.submitButton, '->'],
          },
        ],

        items: [
          me.privacyPolicyContent,
          {
            cls: 'wl-title',
            html: translate('PROFILE.SIGN_AGREEMENT_MODAL.SIGNATURE.TITLE'),
          },
          {
            xtype: 'container',
            layout: { type: 'hbox', align: 'stretch', pack: 'center' },
            items: [
              {
                cls: 'signature',
                height: 160,
                width: 300,
                margin: 10,
                autoEl: {
                  tag: 'canvas',
                },
              },
              {
                xtype: 'button',
                itemId: 'clearSignatureButton',
                cls: 'clear-signature',
                ui: 'danger',
                scale: 'large',
                margin: 10,
                width: 100,
                iconCls: 'icon icon-close',
                handler: this.onClear,
                scope: this,
              },
            ],
          },
        ],
      },
    ]

    this.callParent(arguments)
  },

  uploadSignature: function () {
    const me = this
    me.setLoading(true)
    const clientProfile = Wl.api.SSO.getUserInfo()
    Wl.api.Profile.uploadPrivacyProtectionAgreement(clientProfile.id, me.signaturePad.toDataURL())
      .then(function () {
        return Wl.api.SSO.refreshUserInfo()
      })
      .then(function () {
        me.setLoading(false)
        me.onAccept()
      })
  },

  onAccept: function () {
    this.redirectTo('contracts/list', { force: true })
  },

  onClear: function () {
    const me = this
    me.signaturePad.clear()
    me.submitButton.setDisabled(true)
  },

  onCancel: function () {
    Wl.api.SSO.logout()
  },

  afterLayout: function () {
    const me = this

    me.signaturePadEl = this.getEl().query('canvas', true)[0]

    me.signaturePad = new SignaturePad(me.signaturePadEl, {
      backgroundColor: '#f7f8f9',
    })

    me.signaturePad.addEventListener('endStroke', function () {
      me.submitButton.setDisabled(false)
    })

    me.callParent(arguments)
  },

  beforeShow: function () {
    const me = this
    const clientProfile = Wl.api.SSO.getUserInfo()
    const agentProfile = Wl.api.SSO.getAgentInfo()
    me.updatePrivacyPolicyContent(clientProfile, agentProfile)
    me.callParent(arguments)
  },

  updatePrivacyPolicyContent: function (clientProfile, agentProfile) {
    const me = this
    me.privacyPolicyContent.update({ clientProfile, agentProfile })
  },
})
