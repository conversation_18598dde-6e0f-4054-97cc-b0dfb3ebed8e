Ext.define('Wl.view.primarycontainer.PrimaryContainerView', {
  extend: 'Ext.container.Container',
  xtype: 'wl-primarycontainer',
  config: {
    id: 'app-main',
  },

  requires: [
    'Ext.plugin.Viewport',
    'Ext.window.MessageBox',

    'Wl.view.primarycontainer.PrimaryContainerController',
    'Wl.view.primarycontainer.PrimaryContainerModel',

    'Wl.view.module.contracts.ContractsView',
    'Wl.view.module.damages.DamagesView',
    'Wl.view.module.expert.ExpertView',
    'Wl.view.module.offers.OffersView',
    'Wl.view.module.profession.ProfessionView',
    'Wl.view.module.profile.ProfileView',
    'Wl.view.module.settings.SettingsView',
  ],

  controller: 'primarycontainer',

  viewModel: {
    type: 'primarycontainer',
  },

  layout: 'border',

  cls: 'wl-primarycontainer',

  tabBar: {
    flex: 1,
    layout: {
      align: 'stretch',
      overflowHandler: 'none',
    },
  },

  responsiveConfig: {
    tall: {
      headerPosition: 'top',
    },
    wide: {
      headerPosition: 'left',
    },
  },

  defaults: {
    split: false,
  },

  items: [
    {
      region: 'west',
      width: 217,
      padding: 32,
      xtype: 'container',
      cls: 'wl-primarycontainer-navigation',
      layout: { type: 'vbox', align: 'stretch', pack: 'start' },
      items: [
        {
          xtype: 'container',
          layout: 'center',
          items: [
            {
              xtype: 'image',
              src: Ext.getResourcePath('images/branding/logo.png'),
              width: 134,
              height: 100,
              style: {
                marginBottom: '30px',
              },
            },
          ],
        },
        {
          xtype: 'segmentedbutton',
          vertical: 'true',
          ui: 'navigation',
          cls: 'wl-navigation-menu',
          reference: 'navigationMenu',
          bind: {
            value: '{activeModule}',
          },
          defaults: {
            textAlign: 'left',
          },
          items: [
            {
              text: translate('NAVIGATION.SIDEBAR.CONTRACTS_BUTTON.TEXT'),
              value: 'contracts',
              iconCls: 'icon-document',
              handler: 'gotoContracts',
            },
            {
              text: translate('NAVIGATION.SIDEBAR.OFFERS_BUTTON.TEXT'),
              value: 'offers',
              iconCls: 'icon-gift',
              handler: 'gotoOffers',
            },
            {
              text: translate('NAVIGATION.SIDEBAR.DAMAGES_BUTTON.TEXT'),
              value: 'damages',
              iconCls: 'icon-receipt',
              handler: 'gotoDamages',
            },
            {
              text: translate('NAVIGATION.SIDEBAR.EXPERT_BUTTON.TEXT'),
              value: 'expert',
              iconCls: 'icon-crown',
              handler: 'gotoExpert',
            },
            {
              text: translate('NAVIGATION.SIDEBAR.RISK_BUTTON.TEXT'),
              value: 'risk',
              iconCls: 'icon-health',
              handler: 'gotoRisk',
            },
            {
              text: translate('NAVIGATION.SIDEBAR.PROFESSION_BUTTON.TEXT'),
              value: 'profession',
              iconCls: 'icon-briefcase',
              handler: 'gotoProfession',
            },
            {
              text: translate('NAVIGATION.SIDEBAR.SETTINGS_BUTTON.TEXT'),
              value: 'settings',
              iconCls: 'icon-setting',
              handler: 'gotoSettings',
            },
            {
              text: 'PROFILE',
              value: 'profile',
              hidden: true,
              iconCls: 'icon-information',
              handler: 'gotoProfile',
            },
          ],
        },
        {
          xtype: 'button',
          text: translate('NAVIGATION.SIDEBAR.HELP_BUTTON.TEXT'),
          value: 'help',
          textAlign: 'left',
          iconCls: 'icon-information',
          handler: 'openHelpModal',
        },
        { flex: 1 },
        {
          xtype: 'button',
          cls: 'wl-navigation-language',
          reference: 'navigationLanguage',

          text: translate('NAVIGATION.SIDEBAR.LANGUAGE_BUTTON.TEXT'),
          menu: [
            {
              text: 'English',
              value: 'en',
              icon: Ext.getResourcePath('images/flag_uk.png'),
              handler: 'setLanguageEnglish',
            },
            {
              text: 'Deutsch',
              value: 'de',
              icon: Ext.getResourcePath('images/flag_de.png'),
              handler: 'setLanguageGerman',
            },
            /* {
              text: 'Türkçe',
              value: 'tr',
              icon: Ext.getResourcePath('images/flag_tr.png'),
              handler: 'setLanguageTurkish',
            }, */
          ],
        },
      ],
    },
    {
      region: 'north',
      xtype: 'container',
      weight: -10,
      layout: { type: 'hbox', align: 'stretch', pack: 'end' },
      items: [
        {
          xtype: 'container',
          height: 75,
          margin: 20,
          padding: 12,
          layout: { type: 'hbox', align: 'stretch' },
          cls: 'wl-primarycontainer-main-topbar',
          items: [
            {
              xtype: 'container',
              layout: 'center',
              items: [
                {
                  xtype: 'image',
                  bind: {
                    src: '{agencyLogo}',
                  },
                  width: 50,
                  height: 50,
                  style: {
                    objectFit: 'contain',
                  },
                },
              ],
            },
            {
              xtype: 'button',
              bind: {
                text: '{clientProfile.agencyName}',
              },
            },
            {
              xtype: 'component',
              margin: '0 10',
              cls: 'wl-primarycontainer-main-topbar-separator',
            },
            {
              xtype: 'button',
              text: translate('NAVIGATION.TOPBAR.PROFILE_BUTTON.HELLO_TEXT'),
              bind: {
                text: `${translate(
                  'NAVIGATION.TOPBAR.PROFILE_BUTTON.HELLO_TEXT'
                )}, <span class="wl-text-secondary">{clientProfile.firstName} {clientProfile.lastName}</span>`,
              },
              menu: {
                hidden: false,
                allowOtherMenus: true,

                items: [
                  {
                    text: translate('NAVIGATION.TOPBAR.PROFILE_MENU.PROFILE_BUTTON.TEXT'),
                    handler: 'gotoProfile',
                    iconCls: 'icon icon-profile-circle',
                  },

                  '-',
                  {
                    text: translate('NAVIGATION.TOPBAR.PROFILE_MENU.IMPRINT_BUTTON.TEXT'),
                    iconCls: 'icon icon-information',
                    handler: function () {
                      window.open(Wl.Env.getImpressumFileUrl(), '_blank')
                    },
                  },
                  {
                    text: translate('NAVIGATION.TOPBAR.PROFILE_MENU.DATA_PROTECTION_BUTTON.TEXT'),
                    iconCls: 'icon icon-security-safe',
                    handler: function () {
                      window.open(Wl.Env.getDataProtectionFileUrl(), '_blank')
                    },
                  },
                  {
                    text: translate('NAVIGATION.TOPBAR.PROFILE_MENU.TERMS_OF_USE_BUTTON.TEXT'),
                    iconCls: 'icon icon-security',
                    handler: function () {
                      window.open(Wl.Env.getTermsOfUseFileUrl(), '_blank')
                    },
                  },
                  '-',
                  {
                    text: translate('NAVIGATION.TOPBAR.PROFILE_MENU.LOGOUT_BUTTON.TEXT'),
                    handler: 'onLogout',
                    iconCls: 'icon icon-logout',
                  },
                ],
              },
            },
          ],
        },
      ],
    },
    {
      region: 'center',
      xtype: 'container',
      reference: 'mainContainer',
      cls: 'wl-primarycontainer-main',
      margin: '0 20 20 20',
      defaults: {
        cls: 'wl-primarycontainer-main-item',
        padding: 10,
      },
      bind: {
        activeItem: '{activeModule}',
      },
      layout: 'card',
      items: [
        {
          xtype: 'home',
          itemId: 'home',
        },
        {
          xtype: 'wl-contracts',
          itemId: 'contracts',
        },
        {
          xtype: 'wl-offers',
          itemId: 'offers',
        },
        {
          xtype: 'wl-damages',
          itemId: 'damages',
        },
        {
          xtype: 'wl-expert',
          itemId: 'expert',
        },
        {
          xtype: 'wl-risk',
          itemId: 'risk',
        },
        {
          xtype: 'wl-profession',
          itemId: 'profession',
        },
        {
          xtype: 'wl-profile',
          itemId: 'profile',
        },
        {
          xtype: 'wl-settings',
          itemId: 'settings',
        },
      ],
    },
  ],
})
