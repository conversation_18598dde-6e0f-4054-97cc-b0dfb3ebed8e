.wl-primarycontainer {
  background-image: url('images/branding/background.png');
  background-repeat: no-repeat;
  background-size: cover;
  background-color: transparent !important;
}

.wl-primarycontainer-main {
  border-radius: $border-radius;
}

.wl-primarycontainer-main-item {
  box-shadow: $box-shadow;
  border-radius: $border-radius;
  background-color: $panel_body_background_color;
}

.wl-primarycontainer-navigation,
.wl-primarycontainer-main-topbar {
  .x-btn {
    border-radius: 0;
    background-color: transparent;
    padding-left: 0;
    padding-right: 0;

    .x-btn-icon-el {
      margin-right: 1px;
    }

    .x-btn-icon-el,
    .x-btn-inner {
      color: $dark-color !important;
    }

    .x-btn-arrow-right::after {
      color: $dark-color !important;
    }

    &:hover {
      background-color: transparent;
    }
  }
}

.wl-primarycontainer-navigation {
  background-color: $white-color;

  .x-btn {
    padding-bottom: 10px;
  }

  .x-btn-menu-active,
  .x-btn-pressed {
    background-color: transparent !important;
    .x-btn-icon-el,
    .x-btn-inner {
      color: $turquoise-color !important;
    }
  }

  .wl-navigation-language {
    // border-left: 1px solid $gray200-color;
  }
}

.wl-primarycontainer-main-topbar {
  background-color: $white-color;
  border-radius: $border-radius;

  box-shadow: $box-shadow;

  .x-btn-menu-active,
  .x-btn-pressed {
    background-color: transparent !important;
    .x-btn-icon-el,
    .x-btn-inner {
      color: $dark-color !important;
    }
  }

  .wl-primarycontainer-main-topbar-separator {
    border-left: 1px solid $gray200-color;
  }
}
