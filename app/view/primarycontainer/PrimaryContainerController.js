/**
 * This class is the controller for the main view for the application. It is specified as
 * the "controller" of the Main view class.
 */
Ext.define('Wl.view.primarycontainer.PrimaryContainerController', {
  extend: 'Wl.components.panel.MainPanelController',

  alias: 'controller.primarycontainer',

  routes: {
    '*': 'onShow',
    'contracts/*|contracts': 'onContractsRoute',
    profile: 'onProfileRoute',
    expert: 'onExpertRoute',
    settings: 'onSettingsRoute',
    profession: 'onProfessionRoute',
    'risk/*|risk': 'onRiskRoute',
    'offers/*|offers': 'onOffersRoute',
    'damages/*|damages': 'onDamagesRoute',
  },

  gotoContracts: function () {
    this.redirectTo('contracts/list')
  },

  gotoOffers: function () {
    this.redirectTo('offers/list')
  },

  gotoDamages: function () {
    this.redirectTo('damages/list')
  },

  gotoExpert: function () {
    this.redirectTo('expert')
  },

  gotoProfile: function () {
    this.redirectTo('profile')
  },

  gotoRisk: function () {
    this.redirectTo('risk/overview')
  },

  gotoProfession: function () {
    this.redirectTo('profession')
  },

  gotoCustomerBenefits: function () {
    this.redirectTo('customer-benefits')
  },

  gotoSettings: function () {
    this.redirectTo('settings')
  },

  openHelpModal: function () {
    Ext.create('Wl.view.ModalWindow', {
      items: [
        {
          xtype: 'container',
          layout: {
            type: 'vbox',
            align: 'stretch',
          },
          defaults: {
            xtype: 'component',
            margin: 10,
            style: 'text-align: center;',
          },
          items: [
            {
              cls: 'wl-title',
              html: `<div>${translate('HELP.HELP_MODAL.TITLE')}</div>`,
            },
            {
              html: `<div>${translate('HELP.HELP_MODAL.CONTENT')}</div>`,
            },
            {
              html: `${translate('HELP.HELP_MODAL.COMMIT_FIELD.LABEL')}: @@COMMIT_HASH`,
            },
          ],
        },
      ],
    }).show()
  },

  onHome: function () {
    this.setActiveModule('home')
  },

  onContractsRoute: function () {
    this.setActiveModule('contracts')
  },

  onExpertRoute: function () {
    this.setActiveModule('expert')
  },

  onDamagesRoute: function () {
    this.setActiveModule('damages')
  },

  onRiskRoute: function () {
    this.setActiveModule('risk')
  },

  onProfessionRoute: function () {
    this.setActiveModule('profession')
  },

  onOffersRoute: function () {
    this.setActiveModule('offers')
  },

  onProfileRoute: function () {
    this.setActiveModule('profile')
  },

  onSettingsRoute: function () {
    this.setActiveModule('settings')
  },

  onLogout: function () {
    Wl.api.SSO.logout()
  },

  onShow: function () {
    const languageMenu = this.lookupReference('navigationLanguage')

    const activeItem = languageMenu.menu.items.findBy((item) => {
      return item.value === Wl.util.I18n.getLocale()
    })

    if (activeItem) {
      languageMenu.setIcon(activeItem.icon).setText(activeItem.getDisplayText())
    }
  },

  setActiveModule: function (module) {
    this.getViewModel().set('activeModule', module)
  },

  setLanguageGerman: function (btn) {
    this.lookupReference('navigationLanguage').setIcon(btn.icon).setText(btn.getDisplayText())
    this.setLanguage('de')
  },

  setLanguageEnglish: function (btn) {
    this.lookupReference('navigationLanguage').setIcon(btn.icon).setText(btn.getDisplayText())
    this.setLanguage('en')
  },

  setLanguageTurkish: function (btn) {
    this.lookupReference('navigationLanguage').setIcon(btn.icon).setText(btn.getDisplayText())
    this.setLanguage('tr')
  },

  setLanguage: function (language) {
    Wl.util.I18n.setLocale(language)
    document.location.reload()
  },
})
