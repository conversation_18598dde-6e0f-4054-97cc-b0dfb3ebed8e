Ext.define('Wl.view.primarycontainer.PrimaryContainerModel', {
  extend: 'Ext.app.ViewModel',
  alias: 'viewmodel.primarycontainer',

  data: {
    activeModule: 'contracts',
    language: null,
  },

  formulas: {
    agencyLogo: {
      bind: '{agentProfile}',
      get: function (agentProfile) {
        const image = agentProfile && agentProfile.agencyImgId
        if (!image) return null
        return `${Wl.Env.getApiBaseUrl()}/file?document_id=${image}&size=100x100&access_token=${Wl.api.SSO.getAccessToken()}`
      },
    },
  },
})
