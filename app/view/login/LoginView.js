Ext.define('Wl.view.login.LoginView', {
  extend: 'Wl.view.fullscreencontainer.FullscreenContainerView',
  xtype: 'wl-login',

  baseCls: 'wl-login',

  controller: 'login',
  viewModel: 'login',

  items: [
    {
      xtype: 'mainForm',
      bodyPadding: '0 120 30 120',
      titlePosition: 10,
      tools: [
        {
          type: 'left',
          tooltip: translate('LOGIN.LOGIN.BACK_BUTTON.TOOLTIP'),
          handler: 'onBack',
        },
      ],

      items: [
        {
          xtype: 'container',
          layout: 'center',
          items: [
            {
              xtype: 'image',
              src: Ext.getResourcePath('images/branding/logo.png'),
              width: 169,
              height: 130,
              style: {
                marginBottom: '30px',
              },
            },
          ],
        },
        {
          xtype: 'component',
          style: {
            margin: '10px 0',
          },
          cls: 'wl-title',
          html: translate('LOGIN.LOGIN.TITLE.TEXT'),
        },
        {
          fieldLabel: translate('LOGIN.LOGIN.USERNAME_FIELD.LABEL'),
          name: 'username',
          testId: 'username',
          allowBlank: false,
          bind: {
            value: '{username}',
          },

          listeners: {
            specialkey: 'onSpecialKey',
          },
          tabIndex: 1,
        },
        {
          xtype: 'wl-passwordfield',
          fieldLabel: translate('LOGIN.LOGIN.PASSWORD_FIELD.LABEL'),
          name: 'password',
          testId: 'password',
          allowBlank: false,
          bind: {
            value: '{password}',
          },

          listeners: {
            specialkey: 'onSpecialKey',
          },
          tabIndex: 2,
        },
        {
          xtype: 'button',
          style: {
            marginTop: '20px',
          },
          testId: 'loginBtn',
          anchor: '100%',
          text: translate('LOGIN.LOGIN.SUBMIT_BUTTON.TEXT'),
          handler: 'doLogin',
        },
        {
          xtype: 'button',
          style: {
            marginTop: '10px',
          },
          ui: 'link',
          scale: 'small',
          anchor: '100%',
          text: translate('LOGIN.LOGIN.FORGOT_PASSWORD_BUTTON.TEXT'),
          handler: 'doForgotPassword',
        },
      ],
    },
  ],
})
