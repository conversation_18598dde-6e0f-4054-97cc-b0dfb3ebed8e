Ext.define('Wl.view.login.LoginController', {
  extend: 'Wl.components.panel.MainPanelController',

  alias: 'controller.login',

  doLogin: function () {
    const me = this
    const view = me.getView()
    const form = view.down('form')
    const values = form.getValues()
    const username = values.username
    const password = values.password

    if (form.isValid()) {
      view.setLoading(true)
      Wl.api.SSO.login(username, password)
        .then(function (response) {
          view.setLoading(false)
          me.redirectTo('contracts/list')
        })
        .catch(function (error) {
          view.setLoading(false)
          Wl.toast({
            msg: translate('LOGIN.LOGIN.TOAST.FAILED.MESSAGE'),
            title: translate('LOGIN.LOGIN.TOAST.FAILED.TITLE'),
            ui: 'error',
          })
        })
    }
  },
  doForgotPassword: function () {
    this.redirectTo('forgotpassword')
  },

  onBack: function () {
    this.redirectTo('welcome')
  },

  onSpecialKey: function (field, e) {
    if (e.getKey() == e.ENTER) {
      e.preventDefault()
      this.doLogin()
    }
  },
})
