Ext.define('Wl.view.resetpassword.ResetPasswordView', {
  extend: 'Wl.view.fullscreencontainer.FullscreenContainerView',

  xtype: 'wl-resetpassword',
  ui: 'module',

  controller: 'resetpassword',

  viewModel: {
    type: 'resetpassword-resetpassword',
  },

  bodyCls: 'wl-module-resetpassword',
  items: [
    {
      xtype: 'container',
      layout: 'card',
      items: [
        {
          xtype: 'mainForm',
          padding: '40px',
          itemId: 'resetpasswordform',
          reference: 'resetpasswordform',
          layout: {
            type: 'vbox',
            pack: 'start',
            align: 'middle',
          },
          defaultType: 'wl-passwordfield',
          defaults: {
            labelAlign: 'top',
            labelStyle: 'text-align: left;',
            labelSeparator: '',
            width: '400px',
            msgTarget: 'under',
          },
          items: [
            {
              xtype: 'component',
              style: {
                margin: '10px 0',
              },
              cls: 'wl-title',
              html: translate('RESET_PASSWORD.RESET_PASSWORD.TITLE.TEXT'),
            },
            {
              fieldLabel: translate('RESET_PASSWORD.RESET_PASSWORD.NEW_PASSWORD_FIELD.LABEL'),
              name: 'newPassword',
              testId: 'newPassword',
              allowBlank: false,
              bind: {
                value: '{newPassword}',
              },
            },
            {
              fieldLabel: translate('RESET_PASSWORD.RESET_PASSWORD.CONFIRM_PASSWORD_FIELD.LABEL'),
              name: 'confirmPassword',
              testId: 'confirmPassword',
              allowBlank: false,
              bind: {
                value: '{confirmPassword}',
              },
              validator: function (value) {
                const password = this.up('form').down('[name=newPassword]').getValue()
                return password === value
                  ? true
                  : translate('RESET_PASSWORD.RESET_PASSWORD.CONFIRM_PASSWORD_FIELD.INVALID.MESSAGE')
              },
            },
            {
              xtype: 'button',
              style: {
                marginTop: '20px',
              },
              testId: 'resetBtn',
              text: translate('RESET_PASSWORD.RESET_PASSWORD.SUBMIT_BUTTON.TEXT'),
              handler: 'doResetPassword',
            },
          ],
        },
        {
          xtype: 'container',
          itemId: 'resetpasswordsuccess',
          padding: '40',
          layout: {
            type: 'vbox',
            pack: 'start',
            align: 'middle',
          },
          items: [
            {
              xtype: 'container',
              layout: {
                type: 'vbox',
                pack: 'start',
                align: 'middle',
              },
              items: [
                {
                  xtype: 'image',
                  width: 200,
                  height: 170,
                  src: Ext.getResourcePath('images/success.png'),
                },
              ],
            },
            {
              xtype: 'component',
              style: {
                margin: '20px 0',
                textAlign: 'center',
              },
              cls: 'wl-title',
              html: translate('RESET_PASSWORD.RESET_SUCCESS.TITLE'),
            },
            {
              xtype: 'component',
              style: {
                margin: '20px 0',
                textAlign: 'center',
              },
              html: translate('RESET_PASSWORD.RESET_SUCCESS.DESCRIPTION'),
            },
            {
              xtype: 'button',
              style: {
                marginTop: '40px',
              },
              handler: 'goToLogin',
              text: translate('RESET_PASSWORD.RESET_SUCCESS.CONFIRM_BUTTON.TEXT'),
            },
          ],
        },
        {
          xtype: 'container',
          itemId: 'resetpassworderror',
          reference: 'resetpassworderror',
          padding: '40',
          layout: {
            type: 'vbox',
            pack: 'start',
            align: 'middle',
          },
          items: [
            {
              xtype: 'container',
              layout: 'center',
              items: [
                {
                  xtype: 'image',
                  width: 200,
                  height: 170,
                  src: Ext.getResourcePath('images/error.png'),
                },
              ],
            },
            {
              xtype: 'component',
              style: {
                margin: '20px 0',
                textAlign: 'center',
              },
              cls: 'wl-title',
              html: translate('RESET_PASSWORD.RESET_FAILED.TITLE'),
            },
            {
              xtype: 'component',
              style: {
                margin: '20px 0',
                textAlign: 'center',
              },
              html: translate('RESET_PASSWORD.RESET_FAILED.DESCRIPTION'),
            },
          ],
        },
      ],
    },
  ],
})
