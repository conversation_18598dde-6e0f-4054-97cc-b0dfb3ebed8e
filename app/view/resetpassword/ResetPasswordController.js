Ext.define('Wl.view.resetpassword.ResetPasswordController', {
  extend: 'Wl.components.panel.MainPanelController',

  alias: 'controller.resetpassword',

  routes: {
    'resetpassword/:token': 'onResetPasswordRoute',
  },

  onResetPasswordRoute: function (token) {
    this.getViewModel().set('token', token)
    this.getView().down().setActiveItem('resetpasswordform')
  },

  doResetPassword: function () {
    if (!this.lookupReference('resetpasswordform').getForm().getValid()) {
      return
    }

    Wl.api.SSO.changePasswordWithToken(this.getViewModel().get('token'), this.getViewModel().get('password')).then(
      () => {
        this.getView().down().setActiveItem('resetpasswordsuccess')
      },
      (error) => {
        this.getView().down().setActiveItem('resetpassworderror')
      }
    )
  },

  goToLogin: function () {
    this.redirectTo('login')
  },
})
