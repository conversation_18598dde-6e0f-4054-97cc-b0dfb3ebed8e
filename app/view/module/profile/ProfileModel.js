Ext.define('Wl.view.module.profile.ProfileModel', {
  extend: 'Wl.view.primarycontainer.PrimaryContainerModel',
  alias: 'viewmodel.profile-profile',
  data: {},

  formulas: {
    profileImage: {
      bind: '{clientProfile}',
      get: function (clientProfile) {
        return clientProfile.imgId
          ? `${Wl.Env.getApiBaseUrl()}/file?document_id=${clientProfile.imgId}&access_token=${Wl.api.SSO.getAccessToken()}`
          : null
      },
    },
  },
})
