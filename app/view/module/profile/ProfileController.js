Ext.define('Wl.view.module.profile.ProfileController', {
  extend: 'Wl.components.panel.MainPanelController',
  alias: 'controller.profile-profile',

  onSaveProfileClick: function () {
    const me = this
    const vm = me.getViewModel()
    const view = me.getView()
    const mainForm = view.down('mainForm')
    const mainFormValues = mainForm.getValues()
    const clientProfile = vm.get('clientProfile')

    if (me.checkFormsIfValid([mainForm])) {
      view.setLoading(true)
      const newObj = Wl.omit(mainFormValues, [
        'mobile',
        'phone',
        'email',
        'birthdate',
        'street',
        'streetNum',
        'zip',
        'city',
        'country',
      ])
      const birthdateValue = mainFormValues.birthdate
      const data = {
        ...newObj,
        ...(birthdateValue && { birthdate: birthdateValue }),
        address: {
          street: mainFormValues.street,
          buildingNumber: mainFormValues.streetNum,
          zip: mainFormValues.zip,
          city: mainFormValues.city,
          country: mainFormValues.country,
        },
        communicationChannels: {
          EMAIL: {
            value: mainFormValues.email,
            status: 'ACTIVE',
          },
          PHONE: {
            value: mainFormValues.phone,
            status: 'ACTIVE',
          },
          MOBILE: {
            value: mainFormValues.mobile,
            status: 'ACTIVE',
          },
        },
      }
      Wl.api.Profile.updateUserInfo(data).then(
        (response) => {
          const updatedClientProfile = Wl.optional('_embedded.profile[0]', Ext.decode(response.responseText)) || null
          Wl.api.SSO.setUserInfo(updatedClientProfile)
          Wl.toast({
            ui: 'success',
            msg: translate('TOAST_NOTIFICATION.BODY.PROFILE_HAS_BEEN_UPDATED'),
          })
          view.setLoading(false)
        },
        () => {
          Wl.toast({
            ui: 'error',
            msg: translate('TOAST_NOTIFICATION.BODY.FAILED_TO_UPDATE_PROFILE'),
          })
          view.setLoading(false)
        }
      )
    } else {
      Globals.invalidFormToast()
    }
  },

  onProfileImageChange: function (fileField, value, eOpts) {
    const view = this.getView()

    view.setLoading(true)
    const clientProfile = Wl.api.SSO.getUserInfo()
    Wl.api.Profile.updateUserProfileImage(clientProfile.id, fileField.fileInputEl.dom.files[0])
      .then(function () {
        return Wl.api.SSO.refreshUserInfo()
      })
      .finally(() => {
        view.setLoading(false)
      })
      .catch((err) => {
        Wl.toast({
          ui: 'error',
          msg: translate('TOAST_NOTIFICATION.BODY.FAILED_TO_UPDATE_PROFILE_AVATAR'),
        })
      })
  },
})
