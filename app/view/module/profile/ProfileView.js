Ext.define('Wl.view.module.profile.ProfileView', {
  extend: 'Wl.view.module.Module',

  requires: ['Wl.components.form.MainForm'],

  xtype: 'wl-profile',
  ui: 'module',
  viewModel: 'profile-profile',
  controller: 'profile-profile',
  bodyCls: 'wl-profile',

  layout: {
    type: 'hbox',
  },

  scrollable: {
    x: false,
    y: true,
  },

  title: translate('PROFILE.EDIT_FORM.TITLE'),

  responsiveConfig: {
    'width < 950': {
      layout: {
        vertical: true,
        align: 'stretch',
      },
      width: '100%',
    },
    'width >= 950': {
      layout: {
        vertical: false,
        align: 'begin',
      },
      userCls: 'wl-shadow wl-border-radius',
      width: 820,
    },
  },

  tools: [
    {
      xtype: 'button',
      ui: 'module-tool',
      text: translate('PROFILE.EDIT_FORM.SUBMIT_BUTTON.TEXT'),
      iconCls: 'icon icon-tick-circle',
      handler: 'onSaveProfileClick',
    },
  ],

  items: [
    {
      xtype: 'container',

      flex: 1,
      layout: 'center',
      items: [
        {
          layout: {
            type: 'absolute',
          },
          height: 262,
          width: 262,
          xtype: 'container',
          items: [
            {
              xtype: 'image',
              userCls: 'profile-image',
              bind: {
                src: '{profileImage}',
              },
              height: 262,
              width: 262,
            },
            {
              xtype: 'filefield',
              userCls: 'profile-image-upload',
              iconCls: '',
              ui: 'secondary',
              scale: 'medium',
              submitValue: true,
              buttonText: '<i class="icon icon-camera"></i>',
              x: 200,
              y: 200,
              listeners: {
                change: 'onProfileImageChange',
              },
            },
          ],
        },
      ],
    },
    {
      xtype: 'mainForm',

      items: [
        {
          xtype: 'displayfield',
          fieldCls: 'wl-bold',
          value: translate('PROFILE.EDIT_FORM.MY_DATA_FIELDSET.TITLE'),
        },
        {
          xtype: 'wl-selectfield',
          fieldLabel: translate('PROFILE.EDIT_FORM.MY_DATA_FIELDSET.SALUTATION_FIELD.LABEL'),
          name: 'salutation',
          allowBlank: false,
          bind: {
            value: '{clientProfile.salutation}',
          },
          options: [
            {
              value: 'MISTER',
              name: translate('PROFILE.EDIT_FORM.MY_DATA_FIELDSET.SALUTATION_FIELD.OPTION.MISTER'),
            },
            {
              value: 'MISSIS',
              name: translate('PROFILE.EDIT_FORM.MY_DATA_FIELDSET.SALUTATION_FIELD.OPTION.MISSIS'),
            },
            {
              value: 'UNKNOWN',
              name: translate('PROFILE.EDIT_FORM.MY_DATA_FIELDSET.SALUTATION_FIELD.OPTION.UNKNOWN'),
            },
            {
              value: 'COMPANY',
              name: translate('PROFILE.EDIT_FORM.MY_DATA_FIELDSET.SALUTATION_FIELD.OPTION.COMPANY'),
            },
          ],
        },
        {
          xtype: 'textfield',
          fieldLabel: translate('PROFILE.EDIT_FORM.MY_DATA_FIELDSET.FIRSTNAME_FIELD.LABEL'),
          name: 'firstName',
          allowBlank: false,
          bind: {
            value: '{clientProfile.firstName}',
          },
        },
        {
          xtype: 'textfield',
          fieldLabel: translate('PROFILE.EDIT_FORM.MY_DATA_FIELDSET.LASTNAME_FIELD.LABEL'),
          name: 'lastName',
          allowBlank: false,
          bind: {
            value: '{clientProfile.lastName}',
          },
        },
        {
          xtype: 'datefield',
          fieldLabel: translate('PROFILE.EDIT_FORM.MY_DATA_FIELDSET.BIRTHDAY_FIELD.LABEL'),
          name: 'birthdate',
          bind: {
            value: '{clientProfile.birthdate}',
          },
        },
        {
          xtype: 'wl-selectfield',
          fieldLabel: translate('PROFILE.EDIT_FORM.MY_DATA_FIELDSET.MARTIAL_STATUS_FIELD.LABEL'),
          name: 'familyStatus',
          allowBlank: false,
          bind: {
            value: '{clientProfile.familyStatus}',
          },
          options: [
            {
              value: 'SINGLE',
              name: translate('PROFILE.EDIT_FORM.MY_DATA_FIELDSET.MARTIAL_STATUS_FIELD.OPTION.SINGLE'),
            },
            {
              value: 'MARRIED',
              name: translate('PROFILE.EDIT_FORM.MY_DATA_FIELDSET.MARTIAL_STATUS_FIELD.OPTION.MARRIED'),
            },
            {
              value: 'DIVORCED',
              name: translate('PROFILE.EDIT_FORM.MY_DATA_FIELDSET.MARTIAL_STATUS_FIELD.OPTION.DIVORCED'),
            },
            {
              value: 'WIDOWED',
              name: translate('PROFILE.EDIT_FORM.MY_DATA_FIELDSET.MARTIAL_STATUS_FIELD.OPTION.WIDOWED'),
            },
            {
              value: 'SEPARATED',
              name: translate('PROFILE.EDIT_FORM.MY_DATA_FIELDSET.MARTIAL_STATUS_FIELD.OPTION.SEPARATED'),
            },
            {
              value: 'PARTNERSHIP',
              name: translate('PROFILE.EDIT_FORM.MY_DATA_FIELDSET.MARTIAL_STATUS_FIELD.OPTION.PARTNERSHIP'),
            },
            {
              value: 'COMMUNITY',
              name: translate('PROFILE.EDIT_FORM.MY_DATA_FIELDSET.MARTIAL_STATUS_FIELD.OPTION.COMMUNITY'),
            },
            {
              value: 'UNKNOWN',
              name: translate('PROFILE.EDIT_FORM.MY_DATA_FIELDSET.MARTIAL_STATUS_FIELD.OPTION.UNKNOWN'),
            },
          ],
        },
        {
          xtype: 'combobox',
          store: 'countries',
          fieldLabel: translate('PROFILE.EDIT_FORM.MY_DATA_FIELDSET.NATIONALITY_FIELD.LABEL'),
          displayField: Wl.util.I18n.getLocale(),
          valueField: 'code',
          name: 'nationality',
          queryMode: 'local',
          bind: {
            value: '{clientProfile.nationality}',
          },
        },
        {
          xtype: 'displayfield',
          style: {
            marginTop: '20px',
          },
          fieldCls: 'wl-bold',
          value: translate('PROFILE.EDIT_FORM.CONTACT_FIELDSET.TITLE'),
        },
        {
          xtype: 'textfield',
          fieldLabel: translate('PROFILE.EDIT_FORM.CONTACT_FIELDSET.STREET_FIELD.LABEL'),
          name: 'street',
          allowBlank: false,
          bind: {
            value: '{clientProfile.street}',
          },
        },
        {
          xtype: 'textfield',
          fieldLabel: translate('PROFILE.EDIT_FORM.CONTACT_FIELDSET.HOUSE_NUMBER_FIELD.LABEL'),
          name: 'streetNum',
          allowBlank: false,
          bind: {
            value: '{clientProfile.streetNum}',
          },
        },
        {
          xtype: 'textfield',
          fieldLabel: translate('PROFILE.EDIT_FORM.CONTACT_FIELDSET.ZIP_FIELD.LABEL'),
          name: 'zip',
          allowBlank: false,
          bind: {
            value: '{clientProfile.zip}',
          },
        },
        {
          xtype: 'wl-cityfield',
          fieldLabel: translate('PROFILE.EDIT_FORM.CONTACT_FIELDSET.CITY_FIELD.LABEL'),
          name: 'city',
          allowBlank: false,
          bind: {
            value: '{clientProfile.city}',
            zipCode: '{clientProfile.zip}',
          },
        },
        {
          xtype: 'combobox',
          store: 'countries',
          fieldLabel: translate('PROFILE.EDIT_FORM.CONTACT_FIELDSET.COUNTRY_FIELD.LABEL'),
          displayField: Wl.util.I18n.getLocale(),
          valueField: 'code',
          name: 'country',
          queryMode: 'local',
          allowBlank: false,
          bind: {
            value: '{clientProfile.country}',
          },
        },
        {
          xtype: 'displayfield',
          style: {
            marginTop: '20px',
          },
          fieldCls: 'wl-bold',
          value: translate('PROFILE.EDIT_FORM.ADDRESS_FIELDSET.TITLE'),
        },
        {
          xtype: 'textfield',
          fieldLabel: translate('PROFILE.EDIT_FORM.ADDRESS_FIELDSET.PHONE_FIELD.LABEL'),
          name: 'phone',
          bind: {
            value: '{clientProfile.phone}',
          },
        },
        {
          xtype: 'fieldcontainer',
          fieldLabel: translate('PROFILE.EDIT_FORM.ADDRESS_FIELDSET.MOBILE_FIELD.LABEL'),
          layout: { type: 'column' },
          defaults: {
            msgTarget: 'under',
          },
          items: [
            {
              xtype: 'textfield',
              name: 'mobile',
              columnWidth: 1,
              bind: {
                value: '{clientProfile.mobile}',
              },
            },
          ],
        },
        {
          xtype: 'textfield',
          fieldLabel: translate('PROFILE.EDIT_FORM.ADDRESS_FIELDSET.EMAIL_FIELD.LABEL'),
          name: 'email',
          bind: {
            value: '{clientProfile.email}',
          },
        },
      ],
    },
  ],
})
