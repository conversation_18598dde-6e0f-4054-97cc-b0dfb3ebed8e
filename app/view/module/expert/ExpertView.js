Ext.define('Wl.view.module.expert.ExpertView', {
  extend: 'Wl.view.module.Module',
  xtype: 'wl-expert',
  ui: 'module',
  viewModel: {
    type: 'expert-expert',
  },
  controller: 'expert-expert',
  bodyCls: 'wl-expert',

  layout: 'center',

  title: translate('EXPERT.DETAILS.TITLE'),

  tools: [],
  scrollable: {
    x: false,
    y: true,
  },

  items: [
    {
      xtype: 'panel',
      layout: {
        type: 'hbox',
      },
      responsiveConfig: {
        'width < 1100': {
          layout: {
            vertical: true,
            align: 'stretch',
          },
          userCls: '',
          width: '100%',
        },
        'width >= 1100': {
          layout: {
            vertical: false,
            align: 'begin',
          },
          userCls: 'wl-shadow wl-border-radius',
          width: 820,
        },
      },

      items: [
        {
          xtype: 'container',
          layout: 'center',
          margin: 32,
          items: [
            {
              xtype: 'image',
              height: 260,
              userCls: 'avatar wl-shadow wl-border-radius',
              width: 260,
              bind: {
                src: '{avatar}',
              },
            },
          ],
        },
        {
          xtype: 'container',
          layout: 'anchor',
          flex: 1,
          margin: 32,

          defaults: {
            xtype: 'container',

            layout: { type: 'hbox', align: 'middle' },
            defaults: {
              xtype: 'displayfield',
              labelAlign: 'top',
              margin: '24 0 16 0',
              labelStyle: 'text-align: left;',
              labelSeparator: '',
              msgTarget: 'under',
              allowBlank: false,
            },
          },

          items: [
            {
              xtype: 'container',
              items: [
                {
                  xtype: 'component',
                  cls: 'icon-wrapper',
                  html: '<i class="icon icon-buildings"></i>',
                  width: 24,
                  margin: '0 8 0 0',
                },
                {
                  fieldLabel: translate('EXPERT.DETAILS.AGENCY_FIELD.LABEL'),
                  bind: '{agentProfile.agencyName}',
                  flex: 1,
                },
                {
                  xtype: 'component',
                  width: 100,
                  height: 80,
                  bind: {
                    html: '<div class="agency-logo"><img src="{agencyLogo}"/></div>',
                  },
                },
              ],
            },

            {
              xtype: 'component',
              cls: 'wl-title',
              html: translate('EXPERT.DETAILS.AGENT_DATA_SEPARATOR.TITLE'),
            },
            {
              xtype: 'component',
              margin: '16 0 16 0',
              cls: 'horizontal-line',
            },
            {
              xtype: 'container',
              items: [
                {
                  xtype: 'component',
                  cls: 'icon-wrapper',
                  html: '<i class="icon icon-house"></i>',
                  width: 24,
                  margin: '0 8 0 0',
                },
                {
                  fieldLabel: translate('EXPERT.DETAILS.ADDRESS_FIELD.LABEL'),
                  bind: '{agentProfile.street} {agentProfile.streetNum} {agentProfile.zip} {agentProfile.city}',
                  flex: 1,
                },
              ],
            },
            {
              xtype: 'container',
              items: [
                {
                  xtype: 'component',
                  cls: 'icon-wrapper',
                  html: '<i class="icon icon-call-calling"></i>',
                  width: 24,
                  margin: '0 8 0 0',
                },
                {
                  fieldLabel: translate('EXPERT.DETAILS.PHONE_FIELD.LABEL'),
                  bind: '{agentProfile.phone}',
                  flex: 1,
                },
              ],
            },
            {
              xtype: 'container',
              items: [
                {
                  xtype: 'component',
                  cls: 'icon-wrapper',
                  html: '<i class="icon icon-mobile"></i>',
                  width: 24,
                  margin: '0 8 0 0',
                },
                {
                  fieldLabel: translate('EXPERT.DETAILS.MOBILE_PHONE_FIELD.LABEL'),
                  bind: '{agentProfile.mobile}',
                  flex: 1,
                },
              ],
            },
            {
              xtype: 'container',
              items: [
                {
                  xtype: 'component',
                  cls: 'icon-wrapper',
                  html: '<i class="icon icon-directbox-notif"></i>',
                  width: 24,
                  margin: '0 8 0 0',
                },
                {
                  fieldLabel: translate('EXPERT.DETAILS.EMAIL_FIELD.LABEL'),
                  bind: '{agentProfile.email}',
                  flex: 1,
                },
              ],
            },
            {
              xtype: 'container',
              layout: 'center',
              margin: '42 0 0 0',
              bind: {
                hidden: '{!clientProfile.acceptanceId}',
              },
              items: [
                {
                  xtype: 'button',
                  iconCls: 'icon icon-folder',
                  handler: 'onShowSignedAgreement',
                  ui: 'trinary',
                  bind: {
                    text: `<div><strong>${translate('EXPERT.DETAILS.SIGN_AGREEMENT_BUTTON.SIGNED.LABEL')}</strong></div>
                    {clientProfile.acceptanceTime}
                    `,
                    hidden: '{!(clientProfile.acceptanceId && clientProfile.acceptanceId != "0")}',
                  },
                },
              ],
            },
            {
              xtype: 'container',
              layout: 'center',
              margin: '42 0 0 0',
              bind: {
                hidden: '{clientProfile.acceptanceStatus !== "REQUESTED"}',
              },
              items: [
                {
                  xtype: 'button',
                  text: translate('EXPERT.DETAILS.SIGN_AGREEMENT_BUTTON.TEXT'),
                  handler: 'onSignAgreement',
                },
              ],
            },
          ],
        },
      ],
    },
  ],
})
