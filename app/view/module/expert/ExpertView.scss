.wl-expert {

	.avatar {
		border-radius: 200px;
	}

	.wl-title {
		font-size: $regular-font-size;
		font-weight: 700;
	}

	.agency-logo {
		border-radius: $border-radius;
		background-color: $light-background-color;
		height: 100%;
		padding: 2px;
		text-align: center;

		img {
			object-fit: contain;
			height: 100%;
			width: 80px;
		}
	}

	.x-form-item-label {
			padding-top: 0 !important;
		> .x-form-item-label-inner {
			color: $gray200-color !important;
			padding-top: 0 !important;
		}
	}

	.icon-wrapper {
		display: flex;
		align-items: center;
		.icon {
				font-size: 1.5rem;
			}
	}

	.agreement-download-button a {
		display: flex;
		text-decoration: none;
		gap: 10px;
		padding: 10px;
		align-items: center;
		justify-content: flex-start;

		border-radius: $border-radius;
		background-color: $blue-color;

		color: $white-color;

		div {
			flex: 1
		}

		.icon {
			font-size: 1.5rem;
		}
	}
}

.wl-sign-expert-agreement-modal {
	.signature {
		border-radius: $border-radius;
	}

	.clear-signature {
		border-radius: $border-radius;
	}
}