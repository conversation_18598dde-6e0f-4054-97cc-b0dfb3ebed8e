Ext.define('Wl.view.module.expert.ExpertController', {
  extend: 'Wl.components.panel.MainPanelController',
  alias: 'controller.expert-expert',

  onSignAgreement: function () {
    const me = this
    let msgBox = null
    let submitButton = null
    let signaturePad = null

    msgBox = Ext.create('Ext.window.Window', {
      height: 700,
      width: 700,
      modal: true,
      cls: 'wl-sign-expert-agreement-modal',
      resizeable: false,
      draggable: false,
      scrollable: {
        x: false,
        y: true,
      },
      layout: { type: 'vbox', align: 'middle', pack: 'center' },
      titlePosition: 2,
      defaults: {
        xtype: 'component',
        margin: 10,
      },
      items: [
        {
          cls: 'wl-title',
          html: translate('EXPERT.DETAILS.SIGN_AGREEMENT_MODAL.TITLE'),
        },
        {
          html: this.getViewModel().get('agreementText'),
          width: 520,
        },
        {
          cls: 'wl-title',
          html: translate('EXPERT.DETAILS.SIGN_AGREEMENT_MODAL.SIGNATURE.TITLE'),
        },
        {
          xtype: 'container',
          layout: { type: 'hbox', align: 'stretch', pack: 'center' },
          items: [
            {
              cls: 'signature',
              height: 160,
              width: 300,
              margin: 10,
              autoEl: {
                tag: 'canvas',
              },
            },
            {
              xtype: 'button',
              itemId: 'clearSignatureButton',
              cls: 'clear-signature',
              ui: 'danger',
              scale: 'large',
              margin: 10,
              width: 100,
              iconCls: 'icon icon-close',
              handler: function () {
                signaturePad.clear()
                submitButton.setDisabled(true)
              },
            },
          ],
        },
      ],
      dockedItems: [
        {
          xtype: 'toolbar',
          dock: 'bottom',

          items: [
            '->',
            {
              xtype: 'button',
              itemId: 'submitSignatureButton',
              text: translate('EXPERT.DETAILS.SIGN_AGREEMENT_MODAL.ACCEPT_BUTTON.TEXT'),
              disabled: true,
              handler: function () {
                msgBox.setLoading(true)
                Wl.api.Agent.signAgreement(signaturePad.toDataURL()).then(
                  (response) => {
                    msgBox.hide()
                    Ext.create('Ext.window.Window', {
                      resizeable: false,
                      draggable: false,
                      closable: false,
                      bodyPadding: 20,
                      modal: true,
                      layout: { type: 'vbox', align: 'middle' },
                      items: [
                        {
                          xtype: 'container',
                          layout: 'center',
                          items: [
                            {
                              xtype: 'image',
                              width: 200,
                              height: 170,
                              src: Ext.getResourcePath('images/success.png'),
                            },
                          ],
                        },
                        {
                          xtype: 'component',
                          style: {
                            margin: '20px 0',
                          },
                          cls: 'wl-title',
                          html: translate('EXPERT.DETAILS.SIGN_AGREEMENT_MODAL.SUCCESS.TITLE'),
                        },
                        {
                          xtype: 'component',
                          style: {
                            margin: '20px 0',
                            textAlign: 'center',
                          },
                          html: translate('EXPERT.DETAILS.SIGN_AGREEMENT_MODAL.SUCCESS.DESCRIPTION'),
                        },
                      ],
                      dockedItems: [
                        {
                          xtype: 'toolbar',
                          dock: 'bottom',
                          items: [
                            '->',
                            {
                              xtype: 'button',
                              itemId: 'submitSignatureButton',
                              text: translate('EXPERT.DETAILS.SIGN_AGREEMENT_MODAL.SUCCESS.ACCEPT_BUTTON.TEXT'),
                              handler: function () {
                                document.location.reload()
                              },
                            },
                            '->',
                          ],
                        },
                      ],
                    }).show()
                  },
                  () => {
                    msgBox.setLoading(false)
                    Wl.toast({
                      ui: 'error',
                      msg: translate('EXPERT.DETAILS.SIGN_AGREEMENT_MODAL.TOAST.ERROR.MESSAGE'),
                    })
                  }
                )
              },
            },
            '->',
          ],
        },
      ],
    }).show()

    const signatureEl = msgBox.getEl().query('canvas', true)[0]
    submitButton = msgBox.down('#submitSignatureButton')

    signaturePad = new SignaturePad(signatureEl, {
      backgroundColor: '#f7f8f9',
    })

    signaturePad.addEventListener('endStroke', function () {
      submitButton.setDisabled(false)
    })
  },

  onShowSignedAgreement: function (btn) {
    const me = this
    const view = me.getView()
    const vm = this.getViewModel()
    const clientProfile = vm.get('clientProfile')
    Globals.openDocumentIntoNewTab(clientProfile.acceptanceId)
  },
})
