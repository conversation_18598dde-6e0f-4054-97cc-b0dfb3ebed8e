Ext.define('Wl.view.module.expert.ExpertModel', {
  extend: 'Wl.view.primarycontainer.PrimaryContainerModel',
  alias: 'viewmodel.expert-expert',
  data: {},

  formulas: {
    agencyLogo: {
      bind: '{agentProfile}',
      get: function (agentProfile) {
        const image = agentProfile && agentProfile.agencyImgId
        if (!image) return null
        return `${Wl.Env.getApiBaseUrl()}/file?document_id=${image}&size=100x100&access_token=${Wl.api.SSO.getAccessToken()}`
      },
    },
    avatar: {
      bind: '{agentProfile}',
      get: function (agentProfile) {
        const image = agentProfile && agentProfile.imgId
        if (!image) return null
        return `${Wl.Env.getApiBaseUrl()}/file?document_id=${image}&size=100x100&access_token=${Wl.api.SSO.getAccessToken()}`
      },
    },
    accessToken: {
      bind: '{agentProfile}',
      get: function (agentProfile) {
        return Wl.api.SSO.getAccessToken()
      },
    },
    agreementText: {
      bind: {
        clientProfile: '{clientProfile}',
        agentProfile: '{agentProfile}',
      },

      get: function (data) {
        const userProfile = data.clientProfile
        const agent = data.agentProfile
        const virtualModel = {
          agencies: {
            name: agent.agencyName,
            legalForm: agent.agencyLegalForm,
            street: agent.agencyStreet,
            streetNumber: agent.agencyStreetNumber,
            zip: agent.agencyZip,
            city: agent.agencyCity,
          },
          agents: {
            firstName: agent.firstName,
            lastName: agent.lastName,
            street: agent.street,
            streetNum: agent.streetNum,
            zip: agent.zip,
            city: agent.city,
          },
          texts: {
            today: new Date().toLocaleDateString(),
          },
          clients: {
            firstName: userProfile.firstName,
            lastName: userProfile.lastName,
            street: userProfile.streetName,
            streetNum: userProfile.streetNumber,
            zip: userProfile.postalCode,
            city: userProfile.city,
            birthdate: userProfile.birthday ? new Date(userProfile.birthday).toLocaleDateString() : '',
          },
        }

        let content = (agent && agent.acceptanceRequest) || ''

        const regex = /\{[^}]*\}/g
        content = content.replace(regex, (match) => {
          const obj = JSON.parse(match)
          const key = obj.value
          return `${Wl.optional(key, virtualModel) || ''}`
        })

        return `
				${content}
				`
      },
    },
  },
})
