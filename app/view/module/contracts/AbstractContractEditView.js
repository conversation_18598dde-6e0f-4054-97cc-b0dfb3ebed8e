/** @require Ext
 * @abstract
 * @class Wl.view.module.contracts.AbstractContractsEditView
 * @extends Ext.form.Panel
 * @xtype contracts-edit
 *
 * Abstract class for contracts edit view
 * NOTE: Because of how 'Bindable' works, it would disable the field after Tab is handled,
 * so we need to use 'disabled' method instead of 'bind' method
 */
Ext.define('Wl.view.module.contracts.AbstractContractsEditView', {
  extend: 'Wl.components.form.MainForm',

  ui: 'module',
  // bodyPadding: '50 20',
  cls: 'contract-edit',
  titlePosition: 1,

  /* defaults: {
    xtype: 'textfield',
    labelAlign: 'top',
    labelStyle: 'text-align: left;',
    labelSeparator: '',
    anchor: '100%',
    msgTarget: 'under',
    allowBlank: false,
  }, */

  items: [
    {
      xtype: 'combobox',
      fieldLabel: translate('CONTRACTS.EDIT_FORM.CATEGORY_FIELD.LABEL'),
      name: 'category_id',
      itemId: 'categoryField',
      testId: 'categoryField',
      queryMode: 'local',
      valueField: 'remote_id',
      displayField: 'name',
      store: 'categories',
      anyMatch: true,
      autoSelect: false,
      forceSelection: true,
      allowBlank: false,
      bind: {
        value: '{category_id}',
        disabled: '{mode == "edit"}',
      },
      listeners: {
        change: function (combo, record) {
          combo.up().onCategoryChange(record)
        },
      },
    },
    {
      xtype: 'combobox',
      fieldLabel: translate('CONTRACTS.EDIT_FORM.PROVIDER_FIELD.LABEL'),
      itemId: 'providerField',
      testId: 'providerField',
      queryMode: 'local',
      valueField: 'remote_id',
      displayField: 'name',
      store: 'providers',
      anyMatch: true,
      autoSelect: false,
      forceSelection: true,
      allowBlank: false,
      bind: {
        value: '{provider_id}',
        disabled: '{mode == "edit" || !category_id}',
      },
      listeners: {
        change: function (combo, record) {
          combo.up().onProviderChange(record)
        },
      },
    },
    {
      xtype: 'combobox',
      fieldLabel: translate('CONTRACTS.EDIT_FORM.PRODUCT_FIELD.LABEL'),
      queryMode: 'local',
      testId: 'productField',
      itemId: 'productField',
      valueField: 'remote_id',
      displayField: 'name',
      store: 'products',
      anyMatch: true,
      autoSelect: false,
      forceSelection: true,
      allowBlank: false,
      bind: {
        value: '{product_id}',
        disabled: '{mode == "edit" || !provider_id}',
      },
    },
    {
      fieldLabel: translate('CONTRACTS.EDIT_FORM.INSURANCE_NUMBER_FIELD.LABEL'),
      name: 'insurance_number',
      testId: 'insuranceNumberField',
      allowBlank: false,
      bind: {
        value: '{insurance_number}',
      },
    },
    {
      xtype: 'numberfield',
      fieldLabel: translate('CONTRACTS.EDIT_FORM.COST_FIELD.LABEL'),
      hideTrigger: true,
      testId: 'costField',
      mouseWheelEnabled: false,
      name: 'cost',
      bind: {
        value: '{cost}',
      },
    },
    {
      xtype: 'wl-selectfield',
      testId: 'paymentPeriodField',
      fieldLabel: translate('CONTRACTS.EDIT_FORM.PAYMENT_PERIOD_FIELD.LABEL'),
      name: 'period',
      allowBlank: false,
      options: [
        { value: 'ONCE', name: translate('CONTRACTS.EDIT_FORM.PAYMENT_PERIOD_FIELD.OPTION.ONCE') },
        { value: 'WEEKLY', name: translate('CONTRACTS.EDIT_FORM.PAYMENT_PERIOD_FIELD.OPTION.WEEKLY') },
        { value: 'MONTHLY', name: translate('CONTRACTS.EDIT_FORM.PAYMENT_PERIOD_FIELD.OPTION.MONTHLY') },
        { value: 'QUARTERLY', name: translate('CONTRACTS.EDIT_FORM.PAYMENT_PERIOD_FIELD.OPTION.QUARTERLY') },
        { value: 'HALFYEARLY', name: translate('CONTRACTS.EDIT_FORM.PAYMENT_PERIOD_FIELD.OPTION.HALFYEARLY') },
        { value: 'YEARLY', name: translate('CONTRACTS.EDIT_FORM.PAYMENT_PERIOD_FIELD.OPTION.YEARLY') },
        { value: 'FREE', name: translate('CONTRACTS.EDIT_FORM.PAYMENT_PERIOD_FIELD.OPTION.FREE') },
      ],
      bind: {
        value: '{period}',
      },
    },
    {
      xtype: 'datefield',
      fieldLabel: translate('CONTRACTS.EDIT_FORM.STARTDATE_FIELD.LABEL'),
      name: 'start_date',
      testId: 'startDateField',
      allowBlank: false,
      bind: {
        value: '{start_date}',
      },
    },
    {
      xtype: 'datefield',
      fieldLabel: translate('CONTRACTS.EDIT_FORM.ENDDATE_FIELD.LABEL'),
      name: 'end_date',
      testId: 'endDateField',
      allowBlank: false,
      bind: {
        value: '{end_date}',
      },
    },
    {
      xtype: 'textarea',
      fieldLabel: translate('CONTRACTS.EDIT_FORM.COMMENTS_FIELD.LABEL'),
      name: 'info',
      bind: {
        value: '{info}',
      },
    },
    {
      xtype: 'dataview',
      store: {},
      margin: '16 0 16 0',
      padding: 0,
      cls: 'documents-list',
      bind: {
        store: '{files}',
      },
      tpl: new Ext.XTemplate(
        '<tpl for=".">',
        '<div class="document wl-shadow">',
        '<div class="document__icon">',
        '<i class="icon icon-folder"></i>',
        '</div>',
        '<div class="document__details">',
        '<div class="document__name">',
        '{originalName}',
        '</div>',
        '<div class="document__date">',
        '{modified}',
        '</div>',
        '</div>',
        '<div class="document__actions">',
        '<div class="document__actions--uploaded">' +
          translate('CONTRACTS.EDIT_FORM.DOCUMENTS_FIELD.DOCUMENT.STATUS_LABEL.UPLOADED.TEXT') +
          '</div>',
        '</div>',
        '</div>',
        '</div>',
        '</tpl>'
      ),
      itemSelector: 'div.document',
    },
    {
      xtype: 'fieldcontainer',
      items: [
        {
          xtype: 'html5fileupload',
          name: 'files',
          labelAlign: 'top',
          label: translate('CONTRACTS.EDIT_FORM.DOCUMENTS_FIELD.TEXT'),
          requestURL: '',
          bind: {
            value: '{newFiles}',
          },
        },
      ],
    },
  ],

  getCategoryField: function () {
    return this.getComponent('categoryField')
  },

  getProviderField: function () {
    return this.getComponent('providerField')
  },

  getProductField: function () {
    return this.getComponent('productField')
  },

  clearFilters: function () {
    Ext.StoreManager.get('providers').clearFilter()
    Ext.StoreManager.get('products').clearFilter()

    // we leave other fields disabled in edit mode
    if (this.isEditMode()) {
      return
    }

    const categoryField = this.getCategoryField()
    const providerField = this.getProviderField()
    const productField = this.getProductField()

    categoryField.setRawValue(null)
    providerField.setRawValue(null)
    productField.setRawValue(null)

    return this
  },

  isEditMode: function () {
    return this.getViewModel().get('mode') === 'edit'
  },

  onCategoryChange: function (record) {
    // we leave other fields disabled in edit mode
    if (this.isEditMode()) {
      return
    }

    const store = Ext.StoreManager.get('providers')
    const categoryField = this.getCategoryField()
    const providerField = this.getProviderField()
    const productField = this.getProductField()

    store.clearFilter()
    providerField.setRawValue(null)
    productField.setRawValue(null)

    if (record === null) {
      providerField.setDisabled(true)
    } else {
      providerField.setDisabled(false)
      const value = categoryField.getSelection().get('remote_id')
      store.filterByCategory(value)
    }

    productField.setDisabled(true)
  },

  onProviderChange: function (record) {
    const store = Ext.StoreManager.get('products')
    const categoryField = this.getCategoryField()
    const providerField = this.getProviderField()
    const productField = this.getProductField()

    store.clearFilter()

    if (this.isEditMode()) {
      return
    }

    if (record === null) {
      productField.setRawValue(null)
      productField.setDisabled(true)
    } else {
      store.filterByProviderAndCategory(
        providerField.getSelection().get('remote_id'),
        categoryField.getSelection().get('remote_id')
      )

      productField.setDisabled(false)
    }
  },

  disableCategoryFilterFields: function () {
    const categoryField = this.getCategoryField()
    const providerField = this.getProviderField()
    const productField = this.getProductField()

    categoryField.setDisabled(true)
    providerField.setDisabled(true)
    productField.setDisabled(true)
  },
})
