Ext.define('Wl.view.module.contracts.ContractsController', {
  extend: 'Wl.components.panel.MainPanelController',
  alias: 'controller.contracts-contracts',

  routes: {
    'contracts/list': 'onContractsListRoute',
    'contracts/details/:id': 'onContractsDetailsRoute',
    'contracts/edit/:id': 'onContractsEditRoute',
    'contracts/create': 'onContractsCreateRoute',
  },

  gotoDefaultRoute: function () {
    this.redirectTo('contracts/list')
  },

  onContractsListRoute: function () {
    this.getView().setActiveItem('contractsList')
  },

  onContractsDetailsRoute: function (id) {
    const me = this
    const detailsView = me.lookupReference('contractDetails')

    me.getView().setLoading(true)

    Wl.api.Contracts.getById(id)
      .then(function (contract) {
        detailsView.getViewModel().setData({
          contract_id: contract.id,
          provider_img: `${Wl.Env.getApiBaseUrl()}/file?document_id=${
            contract.provider_img_id
          }&size=100x100&access_token=${Wl.api.SSO.getAccessToken()}`,
          category_name: contract.category_name,
          provider_name: contract.provider_name,
          product_name: contract.product_name,
          insurance_number: contract.insurance_number,
          cost: contract.payment,
          period: contract.payment_period,
          start_date: Ext.Date.parse(contract.start, 'Y-m-d'),
          end_date: Ext.Date.parse(contract.end, 'Y-m-d'),
          info: contract.info,
          status: contract.status,
          currency: contract.currency,
        })

        return Wl.api.Contracts.getDocuments(id)
      })
      .then(function (documents) {
        detailsView.getViewModel().getStore('documents').loadRawData(documents)
        detailsView.down('tabpanel').setActiveItem(0)
        me.getView().setActiveItem('contractDetails')
        me.getView().setLoading(false)
      })
      .catch(function (error) {
        Wl.toast({
          ui: 'error',
          msg: translate('CONTRACTS.DETAILS.TOAST.NOT_FOUND.MESSAGE'),
        })

        me.getView().setLoading(false)
      })
  },

  onContractsEditRoute: function (id) {
    const me = this
    const editView = me.lookupReference('contractEditForm')

    me.getView().setLoading(true)
    editView.clearFilters()

    Wl.state.when('dbHashesLoaded').then(function () {
      Wl.api.Contracts.getById(id)
        .then(function (contract) {
          editView.getViewModel().setData({
            mode: 'edit',
            contract_id: contract.id,
            category_id: contract.category,
            provider_id: contract.provider_id,
            product_id: contract.product_id,
            insurance_number: contract.insurance_number,
            cost: contract.payment,
            period: contract.payment_period,
            start_date: Ext.Date.parse(contract.start, 'Y-m-d'),
            end_date: Ext.Date.parse(contract.end, 'Y-m-d'),
            info: contract.info,
          })

          return Wl.api.Contracts.getDocuments(id)
        })
        .then(function (documents) {
          editView.getViewModel().getStore('files').loadRawData(documents)
          me.getView().setActiveItem('contractEditForm')
          me.getView().setLoading(false)
        })
        .catch(function (error) {
          Wl.toast({
            ui: 'error',
            msg: translate('CONTRACTS.EDIT_FORM.TOAST.NOT_FOUND.MESSAGE'),
          })

          me.getView().setLoading(false)
        })
    })
  },

  onContractsCreateRoute: function () {
    const me = this
    const createView = me.lookupReference('contractCreateForm')

    createView.clearFilters().getViewModel().setData(undefined)
    createView.getViewModel().setData({ mode: 'create' })

    this.getView().setActiveItem('contractCreateForm')
  },

  onAddContractClick: function () {
    this.redirectTo('contracts/create')
  },

  onEditContractClick: function () {
    const me = this
    const detailsView = me.lookupReference('contractDetails')
    const payload = detailsView.getViewModel()
    this.redirectTo('contracts/edit/' + payload.get('contract_id'))
  },

  onBackToDetailsClick: function () {
    const me = this
    const detailsView = me.lookupReference('contractEditForm')
    const payload = detailsView.getViewModel()
    this.redirectTo('contracts/details/' + payload.get('contract_id'))
  },

  onContractClick: function (grid, record) {
    this.redirectTo('contracts/details/' + record.get('id'))
  },

  updateProductListFilters: function () {},

  submitContractCreate: function () {
    const me = this
    const mainForm = me.lookupReference('contractCreateForm')
    const payload = mainForm.getViewModel().getData()

    mainForm.setLoading(true)

    if (me.checkFormsIfValid([mainForm])) {
      Wl.api.Contracts.createContract({
        category: payload.category_id,
        provider_id: payload.provider_id,
        product_id: payload.product_id,
        insurance_number: payload.insurance_number,
        payment_period: payload.period,
        payment: payload.cost,
        info: payload.info,
        start: Ext.Date.format(payload.start_date, 'Y-m-d'),
        end: Ext.Date.format(payload.end_date, 'Y-m-d'),
      })
        .then(function (contract) {
          // Upload files in parallel
          return Ext.Promise.all(
            payload.newFiles.map(function (file) {
              // Read file with FileReader
              const fileReader = new FileReader()
              return new Ext.Promise(function (resolve, reject) {
                fileReader.onload = function () {
                  resolve({
                    name: file.name,
                    type: file.type,
                    size: file.size,
                    owner_id: contract.id,
                    base64: fileReader.result,
                  })
                }
                fileReader.onerror = function () {
                  reject()
                }
                fileReader.readAsDataURL(file)
              })
            })
          ).then(function (files) {
            // Upload files one by one
            return Ext.Promise.all(
              files.map(function (file) {
                return Wl.util.Request.post('/documents_base64', {
                  params: {
                    owner_id: file.owner_id,
                    owner_type: 'CONTRACT',
                    name: file.name,
                    type: file.type === 'application/pdf' ? 'DOCUMENT' : 'PICTURE',
                    image: file.base64,
                    'file[name]': file.name,
                    'file[type]': file.type,
                  },
                })
              })
            )
          })
        })
        .then(function (files) {
          // Create a toast with a success message
          Wl.toast({
            msg: translate('CONTRACTS.EDIT_FORM.CREATE_MODE.TOAST.SUCCESS.MESSAGE'),
            ui: 'success',
          })
          mainForm.setLoading(false)
          // redirect to the contracts list
          me.redirectTo('contracts/list')
          const contractsListView = me.getView().down('dataview')
          const contractsListStore = contractsListView && contractsListView.getStore()
          contractsListStore && contractsListStore.load()
        })
        .catch(function () {
          mainForm.setLoading(false)
          // Create a toast with an error message
          Wl.toast({
            msg: translate('CONTRACTS.EDIT_FORM.CREATE_MODE.TOAST.ERROR.MESSAGE'),
            ui: 'error',
          })
        })
    } else {
      Globals.invalidFormToast()
    }
  },

  submitContractEdit: function () {
    const me = this
    const createView = me.lookupReference('contractEditForm')
    const payload = createView.getViewModel().getData()
    const contractId = payload.contract_id

    createView.setLoading(true)

    Wl.api.Contracts.updateContract(payload.contract_id, {
      insurance_number: payload.insurance_number,
      payment_period: payload.period,
      payment: payload.cost,
      info: payload.info,
      start: Ext.Date.format(payload.start_date, 'Y-m-d'),
      end: Ext.Date.format(payload.end_date, 'Y-m-d'),
    })
      .then(function (contract) {
        // Upload files in parallel
        return Ext.Promise.all(
          payload.newFiles.map(function (file) {
            // Read file with FileReader
            const fileReader = new FileReader()
            return new Ext.Promise(function (resolve, reject) {
              fileReader.onload = function () {
                resolve({
                  name: file.name,
                  type: file.type,
                  size: file.size,
                  owner_id: contract.id,
                  base64: fileReader.result,
                })
              }
              fileReader.onerror = function () {
                reject()
              }
              fileReader.readAsDataURL(file)
            })
          })
        ).then(function (files) {
          // Upload files one by one
          return Ext.Promise.all(
            files.map(function (file) {
              return Wl.util.Request.post('/documents_base64', {
                params: {
                  owner_id: file.owner_id,
                  owner_type: 'CONTRACT',
                  name: file.name,
                  type: file.type === 'application/pdf' ? 'DOCUMENT' : 'PICTURE',
                  image: file.base64,
                  'file[name]': file.name,
                  'file[type]': file.type,
                },
              })
            })
          )
        })
      })
      .then(
        function (response) {
          // Create a toast with a success message
          Wl.toast({
            msg: translate('CONTRACTS.EDIT_FORM.EDIT_MODE.TOAST.SUCCESS.MESSAGE'),
            ui: 'success',
          })
          const contractsListView = me.getView().down('dataview')
          const contractsListStore = contractsListView && contractsListView.getStore()
          contractsListStore && contractsListStore.load()
          me.redirectTo('contracts/details/' + contractId)

          createView.setLoading(false)
        },
        function (err) {
          // Create a toast with an error message
          const response = Ext.decode(err.responseText)
          Wl.toast({
            title: translate('CONTRACTS.EDIT_FORM.EDIT_MODE.TOAST.ERROR.TITLE'),
            msg: Wl.optional('message', response) || translate('CONTRACTS.EDIT_FORM.EDIT_MODE.TOAST.ERROR.MESSAGE'),
            ui: 'error',
          })
          createView.setLoading(false)
        }
      )
  },
})
