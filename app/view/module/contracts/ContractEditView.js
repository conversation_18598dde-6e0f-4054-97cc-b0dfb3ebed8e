Ext.define('Wl.view.module.contracts.ContractsEditView', {
  extend: 'Wl.view.module.contracts.AbstractContractsEditView',
  xtype: 'wl-contracts-edit',

  title: translate('CONTRACTS.EDIT_FORM.EDIT_MODE.TITLE'),

  tools: [
    {
      type: 'left',
      tooltip: translate('CONTRACTS.EDIT_FORM.BACK_BUTTON.TEXT'),
      handler: 'onBackToDetailsClick',
    },
    {
      xtype: 'button',
      ui: 'module-tool',
      text: translate('CONTRACTS.EDIT_FORM.SUBMIT_BUTTON.TEXT'),
      iconCls: 'icon-tick-circle',
      handler: 'submitContractEdit',
    },
  ],
})
