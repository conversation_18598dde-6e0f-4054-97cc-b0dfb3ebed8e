Ext.define('Wl.view.module.contracts.ContractsDetailsModel', {
  extend: 'Wl.view.primarycontainer.PrimaryContainerModel',
  alias: 'viewmodel.contracts-details',
  data: {
    category_name: null,
    provider_name: null,
    provider_img: null,
    product_name: null,
    insurance_number: null,
    cost: null,
    period: null,
    start_date: null,
    end_date: null,
    info: null,
    status: null,
  },

  formulas: {
    statusTranslated: {
      get: function (get) {
        const status = get('status') || 'UNKNOWN'
        return translate('CONTRACTS.DETAILS.DETAILS_TAB.STATUS_FIELD.' + status.toUpperCase() + '.TEXT')
      },
    },

    periodTranslated: {
      get: function (get) {
        const period = get('period') || 'UNKNOWN'
        return translate('CONTRACTS.DETAILS.DETAILS_TAB.COST_FIELD.PERIOD.' + period.toUpperCase() + '.TEXT')
      },
    },

    startDateFormatted: {
      get: function (get) {
        const date = get('start_date')
        return Ext.Date.format(date, 'd.m.Y')
      },
    },
    endDateFormatted: {
      get: function (get) {
        const date = get('end_date')
        return Ext.Date.format(date, 'd.m.Y')
      },
    },
  },

  stores: {
    documents: {
      model: 'Wl.model.ContractDocument',
    },
  },
})
