.contracts-list {
  padding: 20px 40px;

  .contract-item {
    display: flex;
    flex-direction: row;
    justify-content: start;
    gap: 24px;

    margin: 24px auto;

    width: Min(800px, 100%);

    border-radius: $border-radius;
    padding: 24px;
    box-shadow: $box-shadow;

    .contract-item__image-wrapper {
      width: 160px;
      height: 160px;

      border-radius: $border-radius;
      background-color: $light-background-color;

      display: flex;

      flex-direction: row;
      flex-wrap: wrap;
      justify-content: center;
      align-items: center;
      align-content: center;

      img {
        height: 150px;
        width: 150px;
        object-fit: contain;
      }
    }

    .contract-item__details-wrapper {
      flex-grow: 1;
      display: flex;
      flex-direction: column;
      justify-content: space-between;

      .contract-item__category-name,
      .contract-item__provider-name {
        font-weight: bold;
      }

      .contract-item__insurance-number {
        color: $gray200-color;
      }

      .contract-item__bottom-row {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
      }

      .contract-item__status {
        color: $carolina-blue-color;
        font-weight: bold;
      }

      .contract-item__product-name {
        color: $gray200-color;
      }
    }
  }
}

.contract-create {
  .x-form-item {
    margin: 24px auto;
    width: Min(800px, 100%) !important;
  }
}

.contract-details {

  .x-form-item-label-text {
    color: $gray200-color;
  }

  .x-form-item {
    width: Min(800px, 100%) !important;
  }

  .payment-wrapper {
    display: flex;
    justify-content: space-between;

    &__price, &__status {
      display: flex;
      flex-direction: column;
      align-content: space-around;
      gap: 10px;
    }

    &__price {
      align-items: flex-start;
    }

    &__status {
      align-items: flex-end;
    } 

    &__price--label, &__price--period, &__status--label {
      color: $gray200-color;
    }

    &__price--value {
      font-weight: bold;
      font-size: $lead-font-size;
    }

    &__price--period {
      color: $gray200-color;
    }

    &__status--value {
      font-weight: bold;
      font-size: $lead-font-size;
      color: $turquoise-color;
    }
  }
}

.wl-contracts {

  .documents-list {
    .document {
      padding: 10px 20px;
      margin: 30px 0;
      border-radius: $border-radius;
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;
      gap: 20px;
      overflow: hidden;

      &__icon {
        font-size: $lead-font-size;
      }

      &__details {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: center;
      }
      &__name {
        font-weight: 400;
      }
      &__date {
        color: $gray200-color;
        font-size: $small-font-size;
      }

      &__actions--download {
        font-size: $lead-font-size;

        a {
          color: $gray200-color;
        }

        &:hover a  {
          text-decoration: none;
        }
      }

      &__actions--uploaded {
        
        color: $gray200-color;
      }
    }
  }
}