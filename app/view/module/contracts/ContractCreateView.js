Ext.define('Wl.view.module.contracts.ContractsCreateView', {
  extend: 'Wl.view.module.contracts.AbstractContractsEditView',
  xtype: 'wl-contracts-create',

  title: translate('CONTRACTS.EDIT_FORM.CREATE_MODE.TITLE'),
  ui: 'module',
  bodyPadding: '50 20',
  cls: 'contract-create',
  titlePosition: 1,

  tools: [
    {
      type: 'left',
      tooltip: translate('CONTRACTS.EDIT_FORM.BACK_BUTTON.TEXT'),
      handler: 'gotoDefaultRoute',
    },
    {
      xtype: 'button',
      ui: 'module-tool',
      text: translate('CONTRACTS.EDIT_FORM.SUBMIT_BUTTON.TEXT'),
      testId: 'saveContractButton',
      iconCls: 'icon-tick-circle',
      handler: 'submitContractCreate',
    },
  ],
})
