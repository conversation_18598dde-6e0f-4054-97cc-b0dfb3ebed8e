Ext.define('Wl.view.module.contracts.ContractsView', {
  extend: 'Wl.view.module.Module',
  xtype: 'wl-contracts',

  requires: ['Wl.view.module.contracts.ContractsController', 'Wl.view.module.contracts.ContractsModel'],

  controller: 'contracts-contracts',
  viewModel: {
    type: 'contracts-contracts',
  },

  defaults: {
    titleAlign: 'center',
    scrollable: true,
  },

  baseCls: 'wl-contracts',

  layout: 'card',

  items: [
    {
      title: translate('CONTRACTS.LIST.TITLE'),
      xtype: 'panel',
      ui: 'module',

      itemId: 'contractsList',

      tools: [
        {
          xtype: 'button',
          ui: 'module-tool',
          text: translate('CONTRACTS.LIST.ADD_BUTTON.TEXT'),
          iconCls: 'icon-card',
          handler: 'onAddContractClick',
        },
      ],

      bbar: {
        xtype: 'pagingtoolbar',
        displayInfo: true,
        store: 'contracts',
      },

      items: [
        {
          xtype: 'dataview',
          cls: 'contracts-list',
          reference: 'contractsList',
          store: 'contracts',
          listeners: {
            itemclick: 'onContractClick',
            added: function (cmp, container) {
              cmp.loadMask = { target: container }
            },
          },
          emptyText: translate('CONTRACTS.LIST.EMPTY_TEXT'),
          itemSelector: '.contract-item',
          tpl: new Ext.XTemplate(
            '<tpl for=".">',
            '<div class="contract-item">',
            '<div class="contract-item__image-wrapper">',
            `<img src='${Wl.Env.getApiBaseUrl()}/file?document_id={provider_img_id}&size=100x100&access_token={[this.getAccessToken()]}'/>`,
            '</div>',
            '<div class="contract-item__details-wrapper">',
            '<div class="contract-item__category-name">{category_name}</div>',
            '<div class="horizontal-line"></div>',
            '<div class="contract-item__provider-name">{provider_name}</div>',
            '<div class="contract-item__insurance-number">{insurance_number}</div>',
            '<div class="horizontal-line"></div>',
            '<div class="contract-item__bottom-row">',
            '<div class="contract-item__status">{[translate("CONTRACTS.DETAILS.DETAILS_TAB.STATUS_FIELD."+ values.status +".TEXT")]}</div>',
            '<div class="contract-item__product-name">{product_name}</div>',
            '</div>',
            '</div>',
            '</div>',
            '</tpl>',
            {
              getAccessToken: Wl.api.SSO.getAccessToken,
            }
          ),
        },
      ],
    },
    {
      xtype: 'wl-contracts-create',
      itemId: 'contractCreateForm',
      reference: 'contractCreateForm',
      viewModel: {
        type: 'contracts-edit',
      },
    },
    {
      xtype: 'wl-contracts-edit',
      itemId: 'contractEditForm',
      reference: 'contractEditForm',
      viewModel: {
        type: 'contracts-edit',
      },
    },
    {
      xtype: 'wl-contracts-details',
      itemId: 'contractDetails',
      reference: 'contractDetails',

      viewModel: {
        type: 'contracts-details',
      },
    },
  ],
})
