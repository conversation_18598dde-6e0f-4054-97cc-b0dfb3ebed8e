Ext.define('Wl.view.module.contracts.ContractsDetailsView', {
  extend: 'Ext.panel.Panel',
  xtype: 'wl-contracts-details',

  title: translate('CONTRACTS.DETAILS.TITLE.TEXT'),
  ui: 'module',
  cls: 'contract-details',
  titlePosition: 1,
  layout: 'fit',

  tools: [
    {
      type: 'left',
      tooltip: translate('CONTRACTS.DETAILS.BACK_BUTTON.TEXT'),
      handler: 'gotoDefaultRoute',
    },
    {
      xtype: 'button',
      ui: 'module-tool',
      text: translate('CONTRACTS.DETAILS.EDIT_BUTTON.TEXT'),
      iconCls: 'icon-edit',
      handler: 'onEditContractClick',
    },
  ],

  items: [
    {
      xtype: 'tabpanel',
      items: [
        {
          xtype: 'panel',
          scrollable: {
            x: false,
            y: true,
          },
          layout: 'center',
          title: translate('CONTRACTS.DETAILS.DETAILS_TAB.TITLE'),
          responsiveConfig: {
            'width >= 1150': {
              bodyPadding: 32,
            },
          },
          items: [
            {
              xtype: 'panel',

              layout: {
                type: 'hbox',
              },
              responsiveConfig: {
                'width < 1150': {
                  layout: {
                    vertical: true,
                    align: 'stretch',
                  },
                  userCls: '',
                  width: '100%',
                },
                'width >= 1150': {
                  layout: {
                    vertical: false,
                    align: 'start',
                  },
                  userCls: 'wl-shadow wl-border-radius',
                  width: 820,
                },
              },

              items: [
                {
                  xtype: 'container',
                  layout: 'center',
                  margin: 32,
                  items: [
                    {
                      xtype: 'image',
                      height: 260,
                      userCls: 'wl-shadow wl-border-radius',
                      width: 260,
                      style: {
                        objectFit: 'contain',
                        padding: '16px',
                      },
                      bind: {
                        src: '{provider_img}',
                      },
                    },
                  ],
                },
                {
                  xtype: 'container',
                  layout: 'anchor',
                  flex: 1,
                  margin: 32,

                  defaults: {
                    xtype: 'displayfield',
                    labelAlign: 'top',
                    labelStyle: 'text-align: left;',
                    labelSeparator: '',
                    msgTarget: 'under',
                    allowBlank: false,
                  },

                  items: [
                    {
                      bind: { value: '{provider_name}' },
                      fieldCls: 'wl-title',
                    },
                    {
                      xtype: 'component',
                      margin: '16 0 16 0',
                      cls: 'horizontal-line',
                    },
                    {
                      fieldLabel: translate('CONTRACTS.DETAILS.DETAILS_TAB.INSURANCE_NUMBER_FIELD.LABEL'),
                      bind: '{insurance_number}',
                    },
                    {
                      fieldLabel: translate('CONTRACTS.DETAILS.DETAILS_TAB.PRODUCT_FIELD.LABEL'),
                      bind: '{product_name}',
                    },
                    {
                      fieldLabel: translate('CONTRACTS.DETAILS.DETAILS_TAB.CATEGORY_FIELD.LABEL'),
                      bind: '{category_name}',
                    },
                    {
                      xtype: 'component',
                      margin: '16 0 16 0',
                      cls: 'horizontal-line',
                    },
                    {
                      xtype: 'component',
                      reference: 'paymentWrapper',
                      bind: {
                        data: {
                          cost: '{cost}',
                          period: '{periodTranslated}',
                          status: '{statusTranslated}',
                          currency: '{currency}',
                        },
                      },
                      tpl: new Ext.XTemplate(
                        '<div class="payment-wrapper">',
                        '<div class="payment-wrapper__price">',
                        `<span class="payment-wrapper__price--label">${translate(
                          'CONTRACTS.DETAILS.DETAILS_TAB.COST_FIELD.LABEL'
                        )}</span>`,
                        `<span class="payment-wrapper__price--value">{cost} {currency}</span>`,
                        `<span class="payment-wrapper__price--period">{period}</span>`,
                        '</div>',
                        '<div class="payment-wrapper__spacer"></div>',
                        '<div class="payment-wrapper__status">',
                        `<span class="payment-wrapper__status--label">${translate(
                          'CONTRACTS.DETAILS.DETAILS_TAB.STATUS_FIELD.LABEL'
                        )}</span>`,
                        `<span class="payment-wrapper__status--value">{status}</span>`,
                        '</div>',
                        '</div>'
                      ),
                    },
                    {
                      xtype: 'component',
                      margin: '16 0 16 0',
                      cls: 'horizontal-line',
                    },
                    {
                      fieldCls: 'wl-muted',
                      bind: {
                        value: `${translate(
                          'CONTRACTS.DETAILS.DETAILS_TAB.VALID_FIELD.VALID_FROM_TEXT'
                        )} {startDateFormatted} ${translate(
                          'CONTRACTS.DETAILS.DETAILS_TAB.VALID_FIELD.VALID_TO_TEXT'
                        )} {endDateFormatted}`,
                      },
                    },
                  ],
                },
              ],
            },
          ],
        },
        {
          title: translate('CONTRACTS.DETAILS.DOCUMENTS_TAB.TITLE'),
          bodyPadding: '50 20',
          cls: 'documents-list',
          items: [
            {
              xtype: 'dataview',
              store: {},
              bind: {
                store: '{documents}',
              },
              emptyText: translate('CONTRACTS.DETAILS.DOCUMENTS_TAB.EMPTY_TEXT'),
              itemSelector: 'div.document',
              itemTpl: new Ext.XTemplate(
                '<tpl for=".">',
                '<div class="document wl-shadow">',
                '<div class="document__icon">',
                '<i class="icon icon-folder"></i>',
                '</div>',
                '<div class="document__details">',
                '<div class="document__name">',
                '{originalName}',
                '</div>',
                '<div class="document__date">',
                '{modified}',
                '</div>',
                '</div>',
                '<div class="document__actions">',
                '<div class="document__actions--download"><a href="{[this.getDocumentUrl(values.id)]}" target="_blank"><i class="icon icon-document-download"></i></a></div>',
                '</div>',
                '</div>',
                '</div>',
                '</tpl>',
                {
                  getDocumentUrl: function (id) {
                    return `${Wl.Env.getApiBaseUrl()}/file?document_id=${id}&download=1&access_token=${Wl.api.SSO.getAccessToken()}`
                  },
                }
              ),
            },
          ],
        },
      ],
    },
  ],
})
