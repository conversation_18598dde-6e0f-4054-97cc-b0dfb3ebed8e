Ext.define('Wl.view.module.profession.ProfessionView', {
  extend: 'Wl.view.module.Module',
  requires: ['Wl.view.module.profession.ProfessionController', 'Wl.view.module.profession.ProfessionModel'],
  controller: 'profession-profession',
  xtype: 'wl-profession',
  ui: 'module',
  viewModel: {
    type: 'profession-profession',
  },
  bodyCls: 'wl-module-profession',

  titleAlign: 'center',
  title: translate('PROFESSION.EDIT.HEADER.TITLE'),

  tools: [
    {
      xtype: 'button',
      ui: 'module-tool',
      text: translate('PROFESSION.EDIT.SAVE_BUTTON.TEXT'),
      iconCls: 'icon-tick-circle',
      handler: 'onProfessionSaveClick',
    },
  ],
  scrollable: true,
  items: [
    {
      xtype: 'panel',
      layout: {
        type: 'hbox',
        align: 'stretch',
        pack: 'middle',
      },
      items: [
        {
          xtype: 'mainForm',
          reference: 'professionForm',

          items: [
            {
              xtype: 'component',
              margin: '32 0 16 0',
              style: {
                fontWeight: 'bold',
              },
              html: translate('PROFESSION.EDIT.JOB_INFO_FIELDSET.TITLE'),
            },
            {
              xtype: 'textfield',
              fieldLabel: translate('PROFESSION.EDIT.PROFESSION_FIELD.LABEL'),
              bind: {
                value: '{clientProfile.profession}',
              },
            },
            {
              xtype: 'wl-selectfield',
              fieldLabel: translate('PROFESSION.EDIT.JOB_TYPE_FIELD.LABEL'),
              bind: {
                value: '{clientProfile.jobType}',
              },
              options: [
                'Arbeiter',
                'Angestellter',
                'Selbständiger',
                'Freiberufler',
                'Beamter auf Lebenszeit',
                'Beamter im höheren Dienst',
                'Beamter auf Widerruf',
                'Beamter auf Probe',
                'Arbeiter öffentl. Dienst',
                'Angestellter öffentl. Dienst',
                'Geschäftsf. Gesellschafter',
                'Vorstand',
                'Schüler',
                'Azubi',
                'Student',
                'Wehr-.Zivildienst.FSJ',
                'Hausfrau.Hausmann',
                'In Elternzeit',
                'Rentner.Ruheständler',
                'Arbeitssuchend',
              ].map((value) => {
                return { value: value, name: value }
              }),
            },
            {
              xtype: 'numberfield',
              fieldLabel: translate('PROFESSION.EDIT.OFFICE_FIELD.LABEL'),
              // disabled: true,
              readOnly: true,
              suffix: '%',
              minValue: 0,
              maxValue: 100,
              name: 'workPercentageOffice',
              bind: {
                value: '{100 - clientProfile.workPercentagePhysical}',
              },
            },
            {
              xtype: 'numberfield',
              fieldLabel: translate('PROFESSION.EDIT.PHYSICAL_FIELD.LABEL'),
              suffix: '%',
              minValue: 0,
              maxValue: 100,
              bind: {
                value: '{clientProfile.workPercentagePhysical}',
              },
            },
            {
              xtype: 'component',
              margin: '32 0 16 0',
              style: {
                fontWeight: 'bold',
              },
              html: translate('PROFESSION.EDIT.INCOME_FIELDSET.TITLE'),
            },
            {
              xtype: 'textfield',
              fieldLabel: translate('PROFESSION.EDIT.YEARLY_BRUTTO_FIELD.LABEL'),
              suffix: '&euro;',
              bind: {
                value: '{clientProfile.incomeYearlyBrutto}',
              },
            },
            {
              xtype: 'textfield',

              suffix: '&euro;',
              fieldLabel: translate('PROFESSION.EDIT.YEARLY_NETTO_FIELD.LABEL'),
              bind: {
                value: '{clientProfile.incomeYearlyNetto}',
              },
            },
            {
              xtype: 'textfield',
              fieldLabel: translate('PROFESSION.EDIT.YEARLY_SALARIES_FIELD.LABEL'),
              bind: {
                value: '{clientProfile.incomeYearlySalaries}',
              },
            },
            {
              xtype: 'datefield',
              fieldLabel: translate('PROFESSION.EDIT.SAVING_SINCE_FIELD.LABEL'),
              bind: {
                value: '{clientProfile.retirementSavingSince}',
              },
            },
            {
              xtype: 'textfield',
              suffix: '&euro;',
              fieldLabel: translate('PROFESSION.EDIT.CAP_FORM_PAYMENTS_FIELD.LABEL'),
              bind: {
                value: '{clientProfile.capitalFormingPayments}',
              },
            },
            {
              xtype: 'component',
              margin: '32 0 16 0',
              style: {
                fontWeight: 'bold',
              },
              html: translate('PROFESSION.EDIT.TAX_INFO_FIELDSET.TITLE'),
            },
            {
              xtype: 'textfield',
              fieldLabel: translate('PROFESSION.EDIT.TAX_OFFICE_FIELD.LABEL'),
              bind: {
                value: '{clientProfile.taxOfficeName}',
              },
            },
            {
              xtype: 'textfield',
              fieldLabel: translate('PROFESSION.EDIT.TAX_NUMBER_FIELD.LABEL'),
              bind: {
                value: '{clientProfile.taxNumber}',
              },
            },
            {
              xtype: 'textfield',
              fieldLabel: translate('PROFESSION.EDIT.TAX_ID_FIELD.LABEL'),
              bind: {
                value: '{clientProfile.taxId}',
              },
            },
            {
              xtype: 'wl-selectfield',
              fieldLabel: translate('PROFESSION.EDIT.TAX_CLASS_FIELD.LABEL'),
              bind: {
                value: '{clientProfile.taxClass}',
              },
              options: ['_', 'I', 'II', 'III', 'IV', 'V', 'VI'].map((value) => {
                return { value: value, name: value }
              }),
            },
            {
              xtype: 'wl-selectfield',
              fieldLabel: translate('PROFESSION.EDIT.CHURCH_TAX_FIELD.LABEL'),
              bind: {
                value: '{clientProfile.churchTaxPercentage}',
              },
              options: ['0', '4', '5', '6', '7', '8', '9'].map((value) => {
                return { value: value, name: value + '%' }
              }),
            },

            {
              xtype: 'component',
              margin: '32 0 16 0',
              style: {
                fontWeight: 'bold',
              },
              html: translate('PROFESSION.EDIT.ADDITIONAL_INFO_FIELDSET.TITLE'),
            },
            {
              xtype: 'textfield',
              fieldLabel: translate('PROFESSION.EDIT.HEALTH_INSURANCE_FIELD.LABEL'),
              bind: {
                value: '{clientProfile.healthInsurance}',
              },
            },
            {
              xtype: 'textfield',
              fieldLabel: translate('PROFESSION.EDIT.SOCIAL_INSURANCE_NUMBER_FIELD.LABEL'),
              bind: {
                value: '{clientProfile.socialInsuranceNumber}',
              },
            },
          ],
        },
      ],
    },
  ],
})
