Ext.define('Wl.view.module.profession.ProfessionController', {
  extend: 'Wl.components.panel.MainPanelController',
  alias: 'controller.profession-profession',

  onProfessionSaveClick: function () {
    const me = this
    const view = this.getView()
    const mainForm = me.lookupReference('professionForm')
    const formValues = mainForm.getValues()

    if (me.checkFormsIfValid([mainForm])) {
      view.setLoading(true)
      const clientProfile = mainForm.lookupViewModel().get('clientProfile')
      const pickedProfessionData = Wl.pick(clientProfile, [
        'profession',
        'jobType',
        'workPercentageOffice',
        'workPercentagePhysical',
        'incomeYearlyBrutto',
        'incomeYearlyNetto',
        'incomeYearlySalaries',
        'retirementSavingSince',
        'capitalFormingPayments',
        'taxOfficeName',
        'taxNumber',
        'taxId',
        'taxClass',
        'churchTaxPercentage',
        'healthInsurance',
        'socialInsuranceNumber',
      ])

      const professionData = {
        ...pickedProfessionData,
        workPercentageOffice: parseFloat(formValues.workPercentageOffice),
      }

      Wl.api.Profile.setProfession(professionData)
        .then(
          () => {
            Wl.toast({
              ui: 'success',
              msg: translate('TOAST_NOTIFICATION.BODY.RECORD_SAVED_SUCCESSFULLY'),
            })
          },
          () => {
            Wl.toast({
              ui: 'error',
              msg: translate('TOAST_NOTIFICATION.BODY.SAVING_RECORD_FAILED'),
            })
          }
        )
        .finally(() => {
          view.setLoading(false)
        })
    }
  },
})
