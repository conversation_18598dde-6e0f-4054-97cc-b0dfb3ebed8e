@include extjs-panel-ui(
  $ui: 'module',

  $ui-header-background-color: transparent,
  $ui-header-color: $dark-color,
  $ui-border-color: transparent,
  $ui-header-padding: 0
);

// Title does not inherit panel UI so static css override is needed
.x-panel-module .x-panel-header-title-default .x-title-item {
  color: $dark-color;
}

@include extjs-button-medium-ui(
  'module-tool',
  $border-radius: $button-medium-border-radius,
  $border-width: $button-medium-border-width,

  $color: $turquoise-color,
  $color-over: $turquoise-color,
  $color-focus: $turquoise-color,
  $color-pressed: $turquoise-color,
  $color-focus-over: $turquoise-color,
  $color-focus-pressed: $turquoise-color,
  $color-disabled: $gray200-color,

  $background-color: transparent,
  $background-color-over: transparent,
  $background-color-focus: transparent,
  $background-color-pressed: transparent,
  $background-color-focus-over: transparent,
  $background-color-focus-pressed: transparent,
  $background-color-disabled: transparent,

  $glyph-color: $turquoise-color,

  $line-height: $button-medium-line-height,

  $padding: $button-medium-padding,
  $text-padding: $button-medium-text-padding
);

.x-component-empty {
  text-align: center;
  padding-top: Min(7%, 100px);
  font-size: $lead-font-size;
  color: $gray200-color;
}

.x-panel-header-module-top .x-tool-before-title .x-tool-tool-el {
  color: $dark-color;
}

.x-panel-header-module-top .x-tool-tool-el {
  color: $turquoise-color;
}
