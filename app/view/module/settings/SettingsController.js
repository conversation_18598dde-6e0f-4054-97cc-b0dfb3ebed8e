Ext.define('Wl.view.module.settings.SettingsController', {
  extend: 'Wl.components.panel.MainPanelController',
  alias: 'controller.settings-settings',
  routes: {
    settings: 'onSettingsRoute',
  },

  onSettingsRoute: function () {},

  onChangePasswordClick: function () {
    const vm = this.getViewModel()
    let modal

    if (!vm.get('confirmPassword') || vm.get('newPassword') !== vm.get('confirmPassword')) {
      this.lookupReference('confirmPassword').markInvalid(
        translate('SETTINGS.CHANGE_PASSWORD_TAB.CONFIRM_PASSWORD_FIELD.INVALID.MESSAGE')
      )
    } else {
      this.lookupReference('confirmPassword').clearInvalid()

      this.getView().setLoading(true)

      modal = Ext.create('Wl.view.ModalWindow', {
        layout: { type: 'vbox', align: 'middle', pack: 'center' },
        header: false,
        closable: false,
        height: 400,
        dockedItems: [
          {
            xtype: 'toolbar',
            dock: 'bottom',
            items: [
              '->',
              {
                xtype: 'button',
                ui: 'secondary',
                text: translate('SETTINGS.CHANGE_PASSWORD_TAB.CONFIRM_MODAL.CANCEL_BUTTON.TEXT'),
                handler: () => {
                  modal.close()
                },
              },
              {
                xtype: 'button',
                text: translate('SETTINGS.CHANGE_PASSWORD_TAB.CONFIRM_MODAL.OK_BUTTON.TEXT'),
                handler: () => {
                  modal.close()
                  Wl.api.Profile.changePassword(vm.get('currentPassword'), vm.get('newPassword')).then(
                    () => {
                      this.getView().setLoading(false)
                      let modal
                      modal = Ext.create('Wl.view.ModalWindow', {
                        layout: { type: 'vbox', align: 'middle', pack: 'center' },
                        closable: false,
                        header: false,
                        height: 400,
                        dockedItems: [
                          {
                            xtype: 'toolbar',
                            dock: 'bottom',
                            items: [
                              '->',
                              {
                                xtype: 'button',
                                ui: 'secondary',
                                text: translate('SETTINGS.CHANGE_PASSWORD_TAB.SUCCESS_MODAL.OK_BUTTON.TEXT'),
                                handler: () => {
                                  modal.close()
                                  Wl.api.SSO.logout()
                                },
                              },
                              '->',
                            ],
                          },
                        ],
                        items: [
                          {
                            xtype: 'image',
                            width: 200,
                            height: 170,
                            src: Ext.getResourcePath('images/success.png'),
                          },
                          {
                            cls: 'wl-title',
                            html: translate('SETTINGS.CHANGE_PASSWORD_TAB.SUCCESS_MODALL.TITLE'),
                          },
                          {
                            html: translate('SETTINGS.CHANGE_PASSWORD_TAB.SUCCESS_MODAL.CONTENT'),
                          },
                        ],
                      }).show()
                    },
                    () => {
                      this.getView().setLoading(false)
                      Wl.toast({
                        ui: 'error',
                        msg: translate('SETTINGS.CHANGE_PASSWORD_TAB.TOAST.ERROR.MESSAGE'),
                      })
                    }
                  )
                },
              },
              '->',
            ],
          },
        ],
        items: [
          {
            xtype: 'image',
            width: 200,
            height: 170,
            src: Ext.getResourcePath('images/success.png'),
          },
          {
            cls: 'wl-title',
            html: translate('SETTINGS.CHANGE_PASSWORD_TAB.CONFIRM_MODALL.TITLE'),
          },
          {
            html: translate('SETTINGS.CHANGE_PASSWORD_TAB.CONFIRM_MODAL.CONTENT'),
          },
        ],
      }).show()
    }
  },
})
