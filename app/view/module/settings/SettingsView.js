Ext.define('Wl.view.module.settings.SettingsView', {
  extend: 'Wl.view.module.Module',
  requires: ['Wl.view.module.settings.SettingsController', 'Wl.view.module.settings.SettingsModel'],
  controller: 'settings-settings',
  xtype: 'wl-settings',
  ui: 'module',
  viewModel: {
    type: 'settings-settings',
  },
  bodyCls: 'wl-module-settings',
  scrollable: {
    y: true,
    x: false,
  },

  layout: 'center',
  items: [
    {
      xtype: 'mainForm',

      cls: 'wl-settings-tabs',
      title: translate('SETTINGS.TITLE'),

      defaults: {
        xtype: 'wl-passwordfield',
        width: '100%',
      },
      bodyPadding: '10 32 10 32',

      dockedItems: [
        {
          xtype: 'toolbar',
          dock: 'bottom',
          items: [
            '->',
            {
              xtype: 'button',
              text: translate('SETTINGS.CHANGE_PASSWORD_TAB.ACCEPT_BUTTON.TEXT'),
              handler: 'onChangePasswordClick',
            },
            '->',
          ],
        },
      ],
      items: [
        {
          xtype: 'component',
          style: {
            margin: '10px 0',
          },
          cls: 'wl-title',
          html: translate('SETTINGS.CHANGE_PASSWORD_TAB.TITLE'),
        },
        {
          fieldLabel: translate('SETTINGS.CHANGE_PASSWORD_TAB.CURRENT_PASSWORD.LABEL'),
          inputType: 'password',
          name: 'currentPassword',
          reference: 'currentPassword',
          bind: {
            value: '{currentPassword}',
          },
          allowBlank: false,
        },
        {
          fieldLabel: translate('SETTINGS.CHANGE_PASSWORD_TAB.NEW_PASSWORD.LABEL'),
          inputType: 'password',
          name: 'newPassword',
          reference: 'newPassword',
          bind: {
            value: '{newPassword}',
          },
          allowBlank: false,
        },
        {
          fieldLabel: translate('SETTINGS.CHANGE_PASSWORD_TAB.CONFIRM_PASSWORD.LABEL'),
          inputType: 'password',
          name: 'confirmPassword',
          reference: 'confirmPassword',
          bind: {
            value: '{confirmPassword}',
          },
          allowBlank: false,
        },
      ],
    },
  ],
})
