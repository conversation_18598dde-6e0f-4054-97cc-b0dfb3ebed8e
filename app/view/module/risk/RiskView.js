Ext.define('Wl.view.module.risk.RiskView', {
  extend: 'Wl.view.module.Module',
  requires: ['Wl.view.module.risk.RiskController', 'Wl.view.module.risk.RiskModel'],
  controller: 'risk-risk',
  xtype: 'wl-risk',
  ui: 'module',
  viewModel: {
    type: 'risk-risk',
  },
  layout: 'card',
  bodyCls: 'wl-module-risk',
  items: [
    {
      xtype: 'panel',

      reference: 'overviewRiskPanel',

      layout: {
        type: 'hbox',
        align: 'stretch',
        pack: 'middle',
      },

      titleAlign: 'center',
      title: translate('RISK.OVERVIEW.HEADER.TITLE'),

      tools: [
        {
          xtype: 'button',
          ui: 'module-tool',
          text: translate('RISK.OVERVIEW.SAVE_BUTTON.TEXT'),
          iconCls: 'icon-tick-circle',
          handler: 'onRiskSaveClick',
        },
      ],
      scrollable: true,
      items: [
        {
          xtype: 'mainForm',
          reference: 'overviewRiskForm',

          items: [
            {
              xtype: 'numberfield',
              fieldLabel: translate('RISK.OVERVIEW.WEIGHT_FIELD.LABEL'),
              suffix: 'kg',
              bind: {
                value: '{clientProfile.weight}',
              },
            },
            {
              xtype: 'numberfield',
              suffix: 'cm',
              fieldLabel: translate('RISK.OVERVIEW.HEIGHT_FIELD.LABEL'),
              bind: {
                value: '{clientProfile.height}',
              },
            },
            {
              xtype: 'wl-selectfield',
              fieldLabel: translate('RISK.OVERVIEW.SMOKER_FIELD.LABEL'),
              bind: {
                value: '{clientProfile.smoker}',
              },
              options: [
                {
                  value: '0',
                  name: translate('GLOBAL.NO'),
                },
                {
                  value: '50',
                  name: translate('RISK.OVERVIEW.SMOKER_FIELD.OPTIONS.SOMETIMES'),
                },
                {
                  value: '100',
                  name: translate('GLOBAL.YES'),
                },
              ],
            },
            {
              margin: '32 0 16 0',
              style: {
                fontWeight: 'bold',
              },
              xtype: 'component',
              html: translate('RISK.OVERVIEW.HEALTH_INFO_FIELD.LABEL'),
            },
            {
              xtype: 'textarea',
              grow: true,
              height: 360,
              bind: {
                value: '{clientProfile.healthInfo}',
              },
            },
          ],
        },
      ],
    },
    {
      xtype: 'panel',
      reference: 'addRiskPanel',
      // This form is not used for now, but it will be used in the future (when API is extended)
      title: translate('RISK.ADD_RISK.HEADER.TITLE'),
      titleAlign: 'center',
      tools: [
        {
          xtype: 'button',
          ui: 'module-tool',
          text: translate('RISK.ADD_RISK.SAVE_BUTTON.TEXT'),
          iconCls: 'icon-tick-circle',
          handler: 'onAdditionalRiskSaveClick',
        },
      ],
      layout: {
        type: 'hbox',
        align: 'stretch',
        pack: 'middle',
      },
      items: [
        {
          xtype: 'mainForm',
          reference: 'addRiskForm',

          tools: [
            {
              type: 'left',
              tooltip: translate('RISK.ADD_RISK.BACK_BUTTON.TOOLTIP'),
              handler: 'onBack',
            },
          ],
          items: [
            {
              xtype: 'wl-selectfield',
              fieldLabel: translate('RISK.ADD_RISK.TYPE_FIELD.LABEL'),
              allowBlank: false,
              options: [
                {
                  value: 'ANIMALS',
                  name: translate('RISK.ADD_RISK.TYPE_FIELD.OPTION.ANIMALS'),
                },
                {
                  value: 'BOAT',
                  name: translate('RISK.ADD_RISK.TYPE_FIELD.OPTION.BOAT'),
                },
                {
                  value: 'BUNGEE',
                  name: translate('RISK.ADD_RISK.TYPE_FIELD.OPTION.BUNGEE'),
                },
                {
                  value: 'COLLECTIONS',
                  name: translate('RISK.ADD_RISK.TYPE_FIELD.OPTION.COLLECTIONS'),
                },
                {
                  value: 'DIVING',
                  name: translate('RISK.ADD_RISK.TYPE_FIELD.OPTION.DIVING'),
                },
                {
                  value: 'DRIVER_LICENCE_PERIOD',
                  name: translate('RISK.ADD_RISK.TYPE_FIELD.OPTION.DRIVER_LICENCE_PERIOD'),
                },
                {
                  value: 'HABITATE',
                  name: translate('RISK.ADD_RISK.TYPE_FIELD.OPTION.HABITATE'),
                },
                {
                  value: 'HEALTH_PROBLEMS',
                  name: translate('RISK.ADD_RISK.TYPE_FIELD.OPTION.HEALTH_PROBLEMS'),
                },
              ],
            },
            {
              xtype: 'textarea',
              fieldLabel: translate('RISK.ADD_RISK.INFO_FIELD.LABEL'),
              grow: true,
              height: 360,
            },
          ],
        },
      ],
    },
  ],
})
