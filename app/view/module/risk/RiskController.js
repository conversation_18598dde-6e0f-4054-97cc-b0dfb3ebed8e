Ext.define('Wl.view.module.risk.RiskController', {
  extend: 'Wl.components.panel.MainPanelController',
  alias: 'controller.risk-risk',
  routes: {
    'risk/overview': 'onRiskRoute',
    'risk/add': 'onRiskAddRoute',
  },

  onRiskRoute: function () {
    const me = this
    const view = me.lookup('overviewRiskPanel')

    me.getView().setActiveItem(view)
  },

  onRiskAddRoute: function () {
    const me = this
    const view = me.lookup('addRiskPanel')

    me.getView().setActiveItem(view)
  },

  onRiskSaveClick: function () {
    const me = this
    const view = me.lookup('overviewRiskPanel')
    const mainForm = me.lookup('overviewRiskForm')

    if (me.checkFormsIfValid([mainForm])) {
      view.setLoading(true)

      const clientProfile = mainForm.lookupViewModel().get('clientProfile')
      const riskData = Wl.pick(clientProfile, ['weight', 'height', 'smoker', 'healthInfo'])
      Wl.api.Profile.setRisks(riskData).then(
        () => {
          view.setLoading(false)
          Wl.toast({
            ui: 'success',
            msg: translate('RISK.OVERVIEW.TOAST.SAVE_SUCCESS.SUCCESS'),
          })
        },
        () => {
          view.setLoading(false)
          Wl.toast({
            ui: 'error',
            msg: translate('RISK.OVERVIEW.TOAST.SAVE_FAILED.ERROR'),
          })
        }
      )
    } else {
      Globals.invalidFormToast()
    }
  },

  onRiskAddClick: function () {
    this.redirectTo('risk/add')
  },

  onAdditionalRiskSaveClick: function () {
    const me = this
    // TODO There is currently no API for this
    me.lookup('addRiskForm').reset()

    me.redirectTo('risk/overview')
  },
})
