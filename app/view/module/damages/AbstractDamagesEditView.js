/** @require Ext
 * @abstract
 * @class Wl.view.module.damages.AbstractDamagesEditView
 * @extends Ext.form.Panel
 * @xtype damages-edit
 *
 * Abstract class for damages edit view
 * NOTE: Because of how 'Bindable' works, it would disable the field after Tab is handled,
 * so we need to use 'disabled' method instead of 'bind' method
 */
Ext.define('Wl.view.module.damages.AbstractDamagesEditView', {
  extend: 'Ext.panel.Panel',

  ui: 'module',
  bodyPadding: '50 20',
  cls: 'damage-edit',
  bodyCls: 'wl-module-profession',

  titlePosition: 1,
  scrollable: true,

  layout: {
    type: 'vbox',
    pack: 'center',
    align: 'center',
  },

  items: [
    {
      xtype: 'mainForm',
      scrollable: false,

      items: [
        {
          fieldLabel: translate('DAMAGES.EDIT_FORM.INTERNAL_NUMBER_FIELD.LABEL'),
          bind: { value: '{internalNumber}' },
        },
        {
          bind: { value: '{clientDamageNumber}' },
          fieldLabel: translate('DAMAGES.EDIT_FORM.CLIENT_DAMAGE_NUMBER_FIELD.LABEL'),
        },
        {
          bind: { value: '{estimatedAmount}' },
          fieldLabel: translate('DAMAGES.EDIT_FORM.ESTIMATED_AMOUNT_FIELD.LABEL'),
        },
        {
          bind: { value: '{location}' },
          fieldLabel: translate('DAMAGES.EDIT_FORM.LOCATION_FIELD.LABEL'),
        },
        {
          xtype: 'wl-selectfield',
          bind: { value: '{entity}' },
          fieldLabel: translate('DAMAGES.EDIT_FORM.ENTITY_FIELD.LABEL'),
          options: [
            'TREE',
            'GLASSES',
            'BIKE',
            'TV',
            'BUILDING',
            'DISHWASHER',
            'MOBILE',
            'CAR',
            'AWNING',
            'WALL',
            'NAVI',
            'PHONE_INTERCOM',
            'STAIRS',
            'DOOR',
            'FRONT_WALL',
            'OTHERS',
          ].map((value) => {
            return {
              value: value,
              name: translate(`DAMAGES.EDIT_FORM.ENTITY_FIELD.OPTION.${value.toUpperCase()}.TEXT`),
            }
          }),
        },
        {
          xtype: 'combo',
          queryMode: 'local',
          forceSelection: true,
          valueField: 'id',
          displayField: 'product_name',
          editable: false,
          store: 'contracts',
          allowBlank: false,
          bind: { value: '{contractId}', disabled: '{mode == "edit"}' },
          fieldLabel: translate('DAMAGES.EDIT_FORM.CONTRACT_FIELD.LABEL'),
          listeners: {
            select: 'onContractSelect',
          },
        },
        {
          bind: { value: '{date}' },
          itemId: 'damagedate',
          xtype: 'datetimefield',
          fieldLabel: translate('DAMAGES.EDIT_FORM.DAMAGE_DATE_FIELD.LABEL'),
        },
        {
          xtype: 'wl-selectfield',
          bind: { value: '{reason}' },
          fieldLabel: translate('DAMAGES.EDIT_FORM.REASON_FIELD.LABEL'),
          options: [
            'TOWING_COSTS',
            'WASTE_WATER',
            'COLLISION_REAR',
            'FREEWAY',
            'EVADE',
            'UNLOADING_DAMAGE',
            'DAMAGE_OTHER_VEHICLE',
            'BURGLARY',
            'ROAD_WIDTH',
            'BIKE_THEFT',
            'GLITCH',
            'PIPE_BROKE',
            'MANOEUVRING',
            'LANE_CHANGE',
            'HEAVY_RAIN',
            'ROCKFALL',
            'STORM',
            'THEFT_ALL',
            'STORM_TREE',
            'CARELESSNESS',
            'UNKNOWN',
            'VANDALISM',
            'CLOGGED',
            'CONSTIPATION',
            'THEFT_ATTEMPT',
            'WILD_ANIMAL_DAMAGE',
            'OTHERS',
          ].map((value) => {
            return {
              value: value,
              name: translate(`DAMAGES.EDIT_FORM.REASON_FIELD.OPTION.${value.toUpperCase()}.TEXT`),
            }
          }),
        },
        {
          bind: { value: '{providerReportDate}' },
          xtype: 'datetimefield',
          fieldLabel: translate('DAMAGES.EDIT_FORM.PROVIDER_REPORT_DATE_FIELD.LABEL'),
        },
        {
          bind: { value: '{policeReportDate}' },
          xtype: 'datetimefield',
          fieldLabel: translate('DAMAGES.EDIT_FORM.POLICE_REPORT_DATE_FIELD.LABEL'),
        },
        {
          bind: { value: '{externalNumberPolice}' },
          fieldLabel: translate('DAMAGES.EDIT_FORM.POLICE_REPORT_NUMBER_FIELD.LABEL'),
        },
        {
          xtype: 'wl-selectfield',
          disabled: true,
          store: {},
          bind: {
            value: '{type}',
            disabled: '{!contractId}',
            store: '{damageTypes}',
          },
          fieldLabel: translate('DAMAGES.EDIT_FORM.DAMAGE_TYPE_FIELD.LABEL'),
        },
        {
          bind: { value: '{witnesses}' },
          fieldLabel: translate('DAMAGES.EDIT_FORM.WITNESS_FIELD.LABEL'),
        },
        {
          xtype: 'textarea',
          bind: { value: '{description}' },
          grow: true,
          height: 180,
          fieldLabel: translate('DAMAGES.EDIT_FORM.DESCRIPTION_FIELD.LABEL'),
        },
        {
          xtype: 'dataview',
          store: {},
          margin: '16 0 16 0',
          cls: 'documents-list',
          bind: {
            store: '{files}',
          },
          tpl: new Ext.XTemplate(
            '<tpl for=".">',
            '<div class="document wl-shadow">',
            '<div class="document__icon">',
            '<i class="icon icon-folder"></i>',
            '</div>',
            '<div class="document__details">',
            '<div class="document__name">',
            '{originalName}',
            '</div>',
            '<div class="document__date">',
            '{modified}',
            '</div>',
            '</div>',
            '<div class="document__actions">',
            '<div class="document__actions--uploaded">' +
              translate('DAMAGES.EDIT_FORM.DOCUMENTS_FIELD.DOCUMENT.STATUS_LABEL.UPLOADED.TEXT') +
              '</div>',
            '</div>',
            '</div>',
            '</div>',
            '</tpl>'
          ),
          itemSelector: 'div.document',
        },
        {
          xtype: 'container',
          cls: 'x-form-item',
          layout: 'fit',
          margin: '16 0 16 0',
          items: [
            {
              xtype: 'html5fileupload',
              name: 'files',
              labelAlign: 'top',
              label: translate('DAMAGES.EDIT_FORM.DOCUMENTS_FIELD.TEXT'),
              requestURL: '',
              bind: {
                value: '{newFiles}',
              },
            },
          ],
        },
      ],
    },
  ],
})
