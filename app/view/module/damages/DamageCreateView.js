Ext.define('Wl.view.module.damages.DamagesCreateView', {
  extend: 'Wl.view.module.damages.AbstractDamagesEditView',
  xtype: 'wl-damages-create',

  title: translate('DAMAGES.EDIT_FORM.CREATE_MODE.TITLE'),
  ui: 'module',
  bodyPadding: '50 20',
  cls: 'damage-create',
  titlePosition: 1,

  tools: [
    {
      type: 'left',
      tooltip: translate('DAMAGES.EDIT_FORM.BACK_BUTTON.TEXT'),
      handler: 'gotoDefaultRoute',
    },
    {
      xtype: 'button',
      ui: 'module-tool',
      text: translate('GLOBAL.BTN.SAVE'),
      testId: 'saveDamageButton',
      iconCls: 'icon-tick-circle',
      handler: 'submitDamageCreate',
    },
  ],
})
