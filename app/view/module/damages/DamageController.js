Ext.define('Wl.view.module.damages.DamagesController', {
  extend: 'Wl.components.panel.MainPanelController',
  alias: 'controller.damages-damages',

  routes: {
    'damages/list': 'onDamagesListRoute',
    'damages/details/:id@:contractId': 'onDamagesDetailsRoute',
    'damages/create': 'onDamagesCreateRoute',
  },

  gotoDefaultRoute: function () {
    this.redirectTo('damages/list')
  },

  onDamagesListRoute: function () {
    const store = this.getStore('damages')

    if (!store.isLoaded()) {
      store.load()
    }
    this.getView().setActiveItem('damagesList')
  },

  onDamagesDetailsRoute: function (id, contractId) {
    const me = this
    const detailsView = me.lookupReference('damageDetails')

    me.getView().setLoading(true)

    Wl.api.Damages.getById(contractId, id)
      .then(function (damage) {
        detailsView.getViewModel().setData(damage)

        return Wl.api.Damages.getDocuments(id)
      })
      .then(function (documents) {
        detailsView.getViewModel().getStore('documents').loadRawData(documents)
        detailsView.down('tabpanel').setActiveItem(0)
        me.getView().setActiveItem('damageDetails')
        me.getView().setLoading(false)
      })
      .catch(function (error) {
        Wl.toast({
          ui: 'error',
          msg: translate('DAMAGES.DETAILS.TOAST.NOT_FOUND.MESSAGE'),
        })

        me.getView().setLoading(false)
      })
  },

  onDamagesCreateRoute: function () {
    const me = this
    const createView = me.lookupReference('damageCreateForm')

    createView.getViewModel().setData(undefined)
    createView.getViewModel().setData({ mode: 'create' })

    this.getView().setActiveItem('damageCreateForm')
  },

  onContractSelect: function (combo, record) {
    if (record) {
      this.setValidDamageTypes(record.get('module'))
    }
  },

  onAddDamageClick: function () {
    this.redirectTo('damages/create')
  },

  onEditDamageClick: function () {
    const me = this
    const detailsView = me.lookupReference('damageDetails')
    const payload = detailsView.getViewModel()
    this.redirectTo(`damages/edit/${payload.get('id')}@${payload.get('contractId')}`)
  },

  /* onBackToDetailsClick: function () {
    const me = this
    const detailsView = me.lookupReference('damageEditForm')
    const payload = detailsView.getViewModel()
    this.redirectTo(`damages/details/${payload.get('id')}@${payload.get('contractId')}`)
  }, */

  onDamageClick: function (grid, record) {
    this.redirectTo(`damages/details/${record.get('id')}@${record.get('contractId')}`)
  },

  submitDamageCreate: function () {
    const me = this
    const createView = me.lookupReference('damageCreateForm')
    const mainForm = createView.down('mainForm')
    const payload = createView.getViewModel().getData()

    if (me.checkFormsIfValid([mainForm])) {
      createView.setLoading(true)

      Wl.api.Damages.createDamage(payload.contractId, Wl.omit(payload, ['damageTypes', 'newFiles', 'files']))
        .then(function (response) {
          // Upload files in parallel
          return Ext.Promise.all(
            payload.newFiles.map(function (file) {
              // Read file with FileReader
              const fileReader = new FileReader()
              return new Ext.Promise(function (resolve, reject) {
                fileReader.onload = function () {
                  resolve({
                    name: file.name,
                    type: file.type,
                    size: file.size,
                    base64: fileReader.result,
                  })
                }
                fileReader.onerror = function () {
                  reject()
                }
                fileReader.readAsDataURL(file)
              })
            })
          ).then(function (files) {
            const responseData = Ext.decode(response.responseText)
            // Upload files one by one
            return Ext.Promise.all(
              files.map(function (file) {
                return Wl.util.Request.post('/documents_base64', {
                  params: {
                    owner_id: responseData.id,
                    owner_type: 'DAMAGE',
                    name: file.name,
                    type: file.type === 'application/pdf' ? 'DOCUMENT' : 'PICTURE',
                    image: file.base64,
                    'file[name]': file.name,
                    'file[type]': file.type,
                  },
                })
              })
            )
          })
        })
        .then(function (files) {
          // Create a toast with a success message
          Wl.toast({
            msg: translate('DAMAGES.EDIT_FORM.CREATE_MODE.TOAST.SUCCESS.MESSAGE'),
            ui: 'success',
          })
          createView.setLoading(false)
          // redirect to the damages list
          me.redirectTo('damages/list')
          me.getStore('damages').load()
        })
        .catch(function () {
          createView.setLoading(false)
          // Create a toast with an error message
          Wl.toast({
            msg: translate('DAMAGES.EDIT_FORM.CREATE_MODE.TOAST.ERROR.MESSAGE'),
            ui: 'error',
          })
        })
    } else {
      Globals.invalidFormToast()
    }
  },

  // This part is tricky, its behaviour is copied from MVPE
  setValidDamageTypes: function (module) {
    const me = this
    const viewModel = me.lookupReference('damageCreateForm').getViewModel()
    let valuesArray = []

    switch (module) {
      case 'ANHAENGER':
      case 'CAMPING':
      case 'EVB':
      case 'KRAD':
      case 'LKWL':
      case 'PKW':
        valuesArray = me.storeItems1
        break

      case 'ART_INSURANCE':
      case 'GLAS':
      case 'ELECTRONIC':
      case 'BOAT':
      case 'GEV':
        valuesArray = me.storeItems2
        break

      case 'BAU':
      case 'BAU_VERSICHERUNG':
        valuesArray = me.storeItems3
        break

      case 'HAU':
        valuesArray = me.storeItems4
        break

      case 'KLV':
      case 'LRV':
      case 'RIE':
      case 'RLV':
      case 'PRV':
      case 'BU':
      case 'CARE_PENSION':
        valuesArray = me.storeItems5
        break

      case 'KV':
      case 'ANIMAL_SICK':
        valuesArray = me.storeItems6
        break

      case 'THI':
      case 'HUN':
      case 'PFE':
      case 'HUG':
      case 'OTHERS':
      case 'RES':
      case 'SGV':
      case 'BICYCLE_EBIKE':
      case 'TRAVEL_CANCELLATION':
      case 'TRAVEL_LUGGAGE':
      case 'LOSS_OF_RENT':
      case 'GEW':
      case 'GBHV':
      case 'GFRV':
        valuesArray = me.storeItems7
        break

      case 'UNF':
        valuesArray = me.storeItems8
        break

      case 'WOH':
        valuesArray = me.storeItems9
        break

      case 'PHV':
        valuesArray = me.storeItems10
        break

      default:
        valuesArray = me.defaultStoreItems
        break
    }

    valuesArray = valuesArray.map((item) => {
      return {
        value: item,
        name: translate(`DAMAGES.EDIT_FORM.TYPE_FIELD.OPTION.${item}.TEXT`),
      }
    })

    viewModel.getStore('damageTypes').setData(valuesArray)
  },

  storeItems1: [
    'KH_PERSONAL',
    'KH_PROPERTY',
    'KH_P_C',
    'KH_ASSET',
    'VK',
    'COVERAGE_ADD',
    'DRIVERPLUS_IU',
    'TK_GLASS',
    'TK_ED',
    'TK_ST_H',
    'TK_WILDANIMAL',
    'TK_OTHERS',
    'OTHERS',
    'REAR_COLLISION',
    'LOADING_UNLOADING_DAMAGE',
    'SALVAGE_DAMAGE',
    'HIT_AND_RUN',
    'BIKE_CAR',
    'HAIL_DAMAGE',
    'MARTEN_BITE',
    'GLITCH',
    'PROTECTION_LETTER_DAMAGE',
    'ROCKFALL',
    'TOTAL_LOSS',
    'VANDALISM',
  ],
  storeItems2: [
    'THEFT',
    'DAMAGE',
    'OTHERS',
    'LIGHTNING_STRIKE',
    'LIGHTNING_SURGE',
    'BURGLARY',
    'BURGLARY_VANDALISM',
    'EL_DAMAGE',
    'FIRE',
    'VANDALISM',
    'ATTEMPTED_BURGLARY',
  ],
  storeItems3: [
    'ASSET',
    'DAMAGE',
    'INSOLVENCY',
    'OTHERS',
    'PIPE_DAMAGE',
    'BROKEN_GLASS',
    'GLASS',
    'FIRE',
    'BURGLARY_VANDALISM',
    'BURGLARY_THEFT',
  ],
  storeItems4: [
    'FIRE',
    'WATER',
    'STORM',
    'HAIL',
    'E_D_VANDALISMUS',
    'THEFT',
    'ELEMENTARY',
    'GLASS',
    'BIKE_ED',
    'ALL_RISK',
    'OTHERS',
    'LIGHTNING_STRIKE',
    'LIGHTNING_SURGE',
    'LEAK_TEST',
    'THEFT',
    'BURGLARY',
    'BURGLARY_VANDALISM',
    'BURGLARY_THEFT',
    'EL_DAMAGE',
    'BICYCLE_THEFT',
    'FIRE',
    'GLASS',
    'BROKEN_GLASS',
    'ATTEMPTED_BURGLARY',
  ],
  storeItems5: ['DEATH', 'ASSET', 'DISABILITY', 'PROFESSION_CARE', 'OTHERS', 'PERSONAL_INJURY'],
  storeItems6: ['AMBULATORY', 'STATONARY', 'TOOTH', 'OTHERS'],
  storeItems7: ['PERSONAL', 'PROPERTY', 'P_S', 'ASSET', 'OTHERS'],
  storeItems8: ['DISABILITY', 'DEATH', 'KHT_GG', 'OTHERS', 'PERSONAL_INJURY'],
  storeItems9: [
    'FIRE',
    'WATER',
    'STORM',
    'HAIL',
    'E_D_VANDALISMUS',
    'THEFT',
    'ELEMENTARY',
    'GLASS',
    'OTHERS',
    'ATTEMPTED_BURGLARY',
    'KEY_BREAK',
    'PIPE_DAMAGE',
    'GRAFFITI',
    'BROKEN_GLASS',
    'GLASS',
    'BURGLARY',
    'LEAK_TEST',
    'LIGHTNING_SURGE',
    'LIGHTNING_STRIKE',
  ],
  storeItems10: [
    'PERSONAL',
    'PROPERTY',
    'P_S',
    'ASSET',
    'OTHERS',
    'KEY_BREAK',
    'DUTY_SUPERVISION',
    'DEPRIVATION',
    'EXTERNAL_DAMAGE_AGENCY',
    'LIABILITY_DAMAGE',
    'ONLINE_FRAUD',
    'LEGAL_PROTECTION',
    'PET_OWNER_LIABILITY',
    'PECUNIARY_LOSS',
  ],
  defaultStoreItems: [
    'PERSONAL',
    'PROPERTY',
    'P_S',
    'ASSET',
    'REAR_COLLISION',
    'DUTY_SUPERVISION',
    'LOADING_UNLOADING_DAMAGE',
    'DEPRIVATION',
    'SALVAGE_DAMAGE',
    'DISABILITY',
    'LIGHTNING_STRIKE',
    'LIGHTNING_SURGE',
    'LEAK_TEST',
    'THEFT',
    'BURGLARY',
    'BURGLARY_VANDALISM',
    'BURGLARY_THEFT',
    'EL_DAMAGE',
    'HIT_AND_RUN',
    'BIKE_CAR',
    'BICYCLE_THEFT',
    'FIRE',
    'EXTERNAL_DAMAGE_AGENCY',
    'GLASS',
    'BROKEN_GLASS',
    'GRAFFITI',
    'LIABILITY_DAMAGE',
    'HAIL_DAMAGE',
    'CAR_DAMAGE',
    'KH_DAMAGE',
    'LW_DAMAGE',
    'MARTEN_BITE',
    'ONLINE_FRAUD',
    'GLITCH',
    'PERSONAL_INJURY',
    'LEGAL_PROTECTION',
    'PIPE_DAMAGE',
    'DAMAGE',
    'KEY_BREAK',
    'PROTECTION_LETTER_DAMAGE',
    'ROCKFALL',
    'STORM_DAMAGE',
    'PARTIALLY_COMPREHENSIVE',
    'PET_OWNER_LIABILITY',
    'TOTAL_LOSS',
    'ACCIDENT',
    'VANDALISM',
    'PECUNIARY_LOSS',
    'ATTEMPTED_BURGLARY',
    'FULLY_COMPREHENSIVE_DAMAGE',
    'WILD_ANIMAL_DAMAGE',
    'OTHERS',
  ],
})
