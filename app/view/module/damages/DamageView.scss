.damages-list {
  padding: 20px 40px;
	
		.x-grid-item-alt, .x-grid-item-over, .x-grid-item-selected {
			background-color: transparent !important;
		}

    .x-grid-row {
			cursor: pointer;

			.x-grid-cell-first {
				overflow: visible;
				.x-grid-cell-inner {
					background-color: $white-color;
					box-shadow: $box-shadow;
					border-radius: $border-radius;
					padding: 18px 24px;
				}
			}
	
			.x-grid-cell-inner {
				margin: 0 26px 16px 26px;
				padding: 0;
				display: flex;
        align-items: center;
        
				&::after {
					font-family: $font-icon-font-family;
					color: $dark-color;
					position: absolute;
					right: 24px;
					content: '\e936';
					font-size: $lead-font-size;
				}
			}
	
			.icon {
				font-size: $lead-font-size;
			}
	
			.item-title, .item-date {
				margin-left: 20px;
				vertical-align: middle;
				line-height: $lead-font-size;
			}
		}
}

.damage-create, .damage-edit {

}

.damage-details {

  .status-label {
    font-weight: 700;
    display: flex;
    justify-content: space-between;

    .status-indicator {
      border-radius: 100px;
      font-weight: 700;
      font-size: $small-font-size;
      padding: 2px 8px;

      color: $gray200-color;
      background-color: rgba($gray200-color, 0.2);

      &.status-open {
        color: $success-color;
        background-color: rgba($success-color, 0.2);
      }
    }
  }

  .x-form-item-label-text {
    color: $gray200-color;
  }

  .x-form-item {
    width: Min(800px, 100%) !important;
  }
}


.wl-damages {
  .documents-list {
    .document {
      padding: 10px 20px;
      margin: 30px 0;
      border-radius: $border-radius;
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;
      gap: 20px;
      overflow: hidden;

      &__icon {
        font-size: $lead-font-size;
      }

      &__details {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: center;
      }
      &__name {
        font-weight: 400;
      }
      &__date {
        color: $gray200-color;
        font-size: $small-font-size;
      }

      &__actions--download {
        font-size: $lead-font-size;

        a {
          color: $gray200-color;
        }

        &:hover a  {
          text-decoration: none;
        }
      }

      &__actions--uploaded {
        
        color: $gray200-color;
      }
    }
  }
}