Ext.define('Wl.view.module.damages.DamagesView', {
  extend: 'Wl.view.module.Module',
  xtype: 'wl-damages',

  requires: ['Wl.view.module.damages.DamagesController', 'Wl.view.module.damages.DamagesModel'],

  controller: 'damages-damages',
  viewModel: {
    type: 'damages-damages',
  },

  defaults: {
    titleAlign: 'center',
    scrollable: true,
  },

  baseCls: 'wl-damages',

  layout: 'card',

  items: [
    {
      title: translate('DAMAGES.LIST.TITLE'),
      xtype: 'panel',
      ui: 'module',

      bodyPadding: 32,

      itemId: 'damagesList',

      tools: [
        {
          xtype: 'button',
          ui: 'module-tool',
          text: translate('DAMAGES.LIST.ADD_BUTTON.TEXT'),
          iconCls: 'icon-card',
          handler: 'onAddDamageClick',
        },
      ],

      bbar: {
        xtype: 'pagingtoolbar',
        displayInfo: true,
        store: {},
        bind: {
          store: '{damages}',
        },
      },

      layout: {
        type: 'vbox',
        align: 'middle',
      },

      items: [
        {
          xtype: 'grid',
          cls: 'damages-list',
          store: {},
          bind: {
            store: '{damages}',
          },
          listeners: {
            itemclick: 'onDamageClick',
          },
          responsiveConfig: {
            'width < 1150': {
              width: '100%',
            },
            'width >= 1150': {
              width: 840,
            },
          },
          emptyText: translate('DAMAGES.LIST.EMPTY_TEXT'),
          itemSelector: '.damage-item',
          disableSelection: true,
          focusOnToFront: false,
          rowLines: false,
          hideHeaders: true,

          columns: [
            {
              dataIndex: 'name',
              flex: 1,
              xtype: 'templatecolumn',
              tpl: `<i class="icon icon-receipt"></i><div><div class="item-title">${translate(
                'DAMAGES.LIST.ITEM.TITLE.TEXT'
              )} {internalNumber}</div><div class="item-date wl-muted">{date}</div></div>`,
            },
          ],
        },
      ],
    },
    {
      xtype: 'wl-damages-create',
      itemId: 'damageCreateForm',
      reference: 'damageCreateForm',
      viewModel: {
        type: 'damages-edit',
      },
    },
    {
      xtype: 'wl-damages-details',
      itemId: 'damageDetails',
      reference: 'damageDetails',

      viewModel: {
        type: 'damages-details',
      },
    },
  ],
})
