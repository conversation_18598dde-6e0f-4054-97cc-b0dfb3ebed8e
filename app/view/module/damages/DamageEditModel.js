Ext.define('Wl.view.module.damages.DamagesEditModel', {
  extend: 'Wl.view.primarycontainer.PrimaryContainerModel',
  alias: 'viewmodel.damages-edit',

  data: {
    mode: null,
    newFiles: [],
  },

  stores: {
    damageTypes: Ext.create('Ext.data.Store', {
      fields: ['value', 'name'],
    }),
    files: Ext.create('Ext.data.Store', {}),
  },

  formulas: {
    estimatedAmount: {
      get: function (get) {
        return get('_estimatedAmount')
      },
      set: function (value) {
        return this.set('_estimatedAmount', Number(value))
      },
    },
  },
})
