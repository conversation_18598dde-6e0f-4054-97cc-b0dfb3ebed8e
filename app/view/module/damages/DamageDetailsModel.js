Ext.define('Wl.view.module.damages.DamagesDetailsModel', {
  extend: 'Wl.view.primarycontainer.PrimaryContainerModel',
  alias: 'viewmodel.damages-details',
  data: {
    id: null,
  },

  formulas: {
    statusLowercase: {
      get: function (get) {
        return (get('status') || '').toLowerCase()
      },
    },

    entityTranslated: {
      get: function (get) {
        const value = get('entity')
        return value ? translate(`DAMAGES.EDIT_FORM.ENTITY_FIELD.OPTION.${value.toUpperCase()}.TEXT`) : ''
      },
    },

    reasonTranslated: {
      get: function (get) {
        const value = get('reason')
        return value ? translate(`DAMAGES.EDIT_FORM.REASON_FIELD.OPTION.${value.toUpperCase()}.TEXT`) : ''
      },
    },

    typeTranslated: {
      get: function (get) {
        const value = get('type')
        return value ? translate(`DAMAGES.EDIT_FORM.TYPE_FIELD.OPTION.${value.toUpperCase()}.TEXT`) : ''
      },
    },

    statusName: {
      get: function (get) {
        return translate(`DAMAGES.DETAILS.STATUS.${get('status').toUpperCase()}.TEXT`)
      },
    },
  },

  stores: {
    documents: {
      model: 'Wl.model.DamageDocument',
    },
  },
})
