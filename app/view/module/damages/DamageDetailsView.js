Ext.define('Wl.view.module.damages.DamagesDetailsView', {
  extend: 'Ext.panel.Panel',
  xtype: 'wl-damages-details',

  title: translate('DAMAGES.DETAILS.TITLE.TEXT'),
  ui: 'module',
  cls: 'damage-details',
  titlePosition: 1,
  layout: 'fit',

  tools: [
    {
      type: 'left',
      tooltip: translate('DAMAGES.DETAILS.BACK_BUTTON.TEXT'),
      handler: 'gotoDefaultRoute',
    },
    /* {
      xtype: 'button',
      ui: 'module-tool',
      text: translate('DAMAGES.DETAILS.EDIT_BUTTON.TEXT'),
      iconCls: 'icon-edit',
      handler: 'onEditDamageClick',
    }, */
  ],

  items: [
    {
      xtype: 'tabpanel',
      items: [
        {
          xtype: 'panel',
          scrollable: {
            x: false,
            y: true,
          },
          layout: 'center',
          title: translate('DAMAGES.DETAILS.DETAILS_TAB.TITLE'),
          responsiveConfig: {
            'width >= 1150': {
              bodyPadding: 32,
            },
          },
          items: [
            {
              xtype: 'panel',

              responsiveConfig: {
                'width < 1150': {
                  userCls: '',
                  width: '100%',
                },
                'width >= 1150': {
                  userCls: 'wl-shadow wl-border-radius',
                  width: 540,
                },
              },

              items: [
                {
                  xtype: 'container',
                  layout: 'anchor',
                  flex: 1,
                  margin: 32,

                  defaults: {
                    xtype: 'displayfield',
                    labelAlign: 'top',
                    labelStyle: 'text-align: left;',
                    labelSeparator: '',
                    msgTarget: 'under',
                    allowBlank: false,
                  },

                  items: [
                    {
                      bind: { value: 'Damage {id}' },
                      fieldCls: 'wl-title',
                    },
                    {
                      xtype: 'component',
                      margin: '16 0 16 0',
                      cls: 'horizontal-line',
                    },
                    {
                      xtype: 'component',
                      cls: 'status-label',
                      bind: {
                        html:
                          translate('DAMAGES.EDIT_FORM.STATUS_FIELD.LABEL') +
                          '<div class="status-indicator status-{statusLowercase}">{statusName}</div>',
                      },
                    },
                    {
                      fieldLabel: translate('DAMAGES.DETAILS.DETAILS_TAB.INTERNAL_NUMBER_FIELD.LABEL'),
                      bind: '{internalNumber}',
                    },
                    {
                      fieldLabel: translate('DAMAGES.DETAILS.DETAILS_TAB.CLIENT_DAMAGE_NUMBER_FIELD.LABEL'),
                      bind: '{clientDamageNumber}',
                    },
                    {
                      fieldLabel: translate('DAMAGES.DETAILS.DETAILS_TAB.ESTIMATED_DAMAGE_AMOUNT_FIELD.LABEL'),
                      bind: '{estimatedAmount}',
                    },
                    {
                      xtype: 'component',
                      margin: '16 0 16 0',
                      cls: 'horizontal-line',
                    },
                    {
                      fieldLabel: translate('DAMAGES.DETAILS.DETAILS_TAB.LOCATION_FIELD.LABEL'),
                      bind: '{location}',
                    },
                    {
                      fieldLabel: translate('DAMAGES.DETAILS.DETAILS_TAB.DAMAGED_ENTITY_FIELD.LABEL'),
                      bind: '{entityTranslated}',
                    },
                    {
                      fieldLabel: translate('DAMAGES.DETAILS.DETAILS_TAB.CONTRACT_FIELD.LABEL'),
                      bind: '{contractProductName}',
                    },
                    {
                      xtype: 'component',
                      margin: '16 0 16 0',
                      cls: 'horizontal-line',
                    },
                    {
                      fieldLabel: translate('DAMAGES.DETAILS.DETAILS_TAB.DAMAGE_TIME_FIELD.LABEL'),
                      bind: '{date}',
                    },
                    {
                      fieldLabel: translate('DAMAGES.DETAILS.DETAILS_TAB.DAMAGE_REASON_FIELD.LABEL'),
                      bind: '{reasonTranslated}',
                    },
                    {
                      fieldLabel: translate('DAMAGES.DETAILS.DETAILS_TAB.REPORTED_TO_AGENT_DATE_FIELD.LABEL'),
                      bind: '{providerReportDate}',
                    },
                    {
                      fieldLabel: translate('DAMAGES.DETAILS.DETAILS_TAB.REPORTED_TO_POLICE_DATE_FIELD.LABEL'),
                      bind: '{policeReportDate}',
                    },
                    {
                      fieldLabel: translate('DAMAGES.DETAILS.DETAILS_TAB.POLICE_REPORT_NUMBER_FIELD.LABEL'),
                      bind: '{externalNumberPolice}',
                    },
                    {
                      xtype: 'component',
                      margin: '16 0 16 0',
                      cls: 'horizontal-line',
                    },
                    {
                      fieldLabel: translate('DAMAGES.DETAILS.DETAILS_TAB.DAMAGE_TYPE_FIELD.LABEL'),
                      bind: '{typeTranslated}',
                    },
                    {
                      fieldLabel: translate('DAMAGES.DETAILS.DETAILS_TAB.WITNESS_FIELD.LABEL'),
                      bind: '{witnesses}',
                    },
                    {
                      fieldLabel: translate('DAMAGES.DETAILS.DETAILS_TAB.EXPERT_FIELD.LABEL'),
                      bind: '{expert}',
                    },
                    {
                      xtype: 'component',
                      margin: '16 0 16 0',
                      cls: 'horizontal-line',
                    },
                    {
                      fieldLabel: translate('DAMAGES.DETAILS.DETAILS_TAB.DAMAGE_DESCRIPTION_FIELD.LABEL'),
                      bind: '{description}',
                    },
                  ],
                },
              ],
            },
          ],
        },
        {
          title: translate('DAMAGES.DETAILS.DOCUMENTS_TAB.TITLE'),
          bodyPadding: '50 20',
          cls: 'documents-list',
          scrollable: {
            x: false,
            y: true,
          },
          items: [
            {
              xtype: 'dataview',
              store: {},
              bind: {
                store: '{documents}',
              },

              emptyText: translate('DAMAGES.DETAILS.DOCUMENTS_TAB.EMPTY_TEXT'),
              itemSelector: 'div.document',
              itemTpl: new Ext.XTemplate(
                '<tpl for=".">',
                '<div class="document wl-shadow">',
                '<div class="document__icon">',
                '<i class="icon icon-folder"></i>',
                '</div>',
                '<div class="document__details">',
                '<div class="document__name">',
                '{originalName}',
                '</div>',
                '<div class="document__date">',
                '{modified}',
                '</div>',
                '</div>',
                '<div class="document__actions">',
                '<div class="document__actions--download"><a href="{[this.getDocumentUrl(values.id)]}" target="_blank"><i class="icon icon-document-download"></i></a></div>',
                '</div>',
                '</div>',
                '</div>',
                '</tpl>',
                {
                  getDocumentUrl: function (id) {
                    return `${Wl.Env.getApiBaseUrl()}/file?document_id=${id}&download=1&access_token=${Wl.api.SSO.getAccessToken()}`
                  },
                }
              ),
            },
          ],
        },
      ],
    },
  ],
})
