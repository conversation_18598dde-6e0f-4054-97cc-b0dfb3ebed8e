.wl-offers {
	.wl-button-revoke {
		background-color: $danger-color;
	}

.x-grid-empty {
	text-align: center	;
	font-size: $lead-font-size;
	font-weight: 700;
}


	.wl-offers-list {
		.wl-offers-list-wrapper {
			margin: 0 auto;
		}


		.wl-offers-header {
			font-weight: 700;
			font-size: $lead-font-size;
			margin-left: 45px;
		}
	
	
		.x-grid-item-alt, .x-grid-item-over, .x-grid-item-selected {
			background-color: transparent !important;
		}
	
		.x-grid-group-hd-collapsed {
			.x-grid-group-title::after {
				content: '\ff90' !important;
			}
		}
	
		.x-group-hd-container {
			overflow: visible;
		}
	
		.x-grid-group-hd  {
			border: none;
			padding: 0;
			background: transparent;
	
	
			.x-grid-group-title {
				color: $white-color;
				font-weight: 400;
				border-radius: $border-radius;
				background-color: $blue-color;
				box-shadow: $box-shadow;
		
				padding: 18px 24px;
				margin: 0 26px 16px 26px;
			}
	
			.x-grid-group-title::before {
				display: none;
			}
	
			.x-grid-group-title::after {
				font-family: $font-icon-font-family;
				color: $white-color;
				position: absolute;
				right: 24px;
			}
	
			&.x-grid-group-hd-collapsible {
				.x-grid-group-title::after {
					content: '\e940';
				}
			}
		}
	
		.x-grid-row {
			cursor: pointer;
			.x-grid-cell-first {
				overflow: visible;
				.x-grid-cell-inner {
					background-color: $white-color;
					box-shadow: $box-shadow;
					border-radius: $border-radius;
					padding: 18px 24px;
				}
			}
	
			.x-grid-cell-inner {
				margin: 0 26px 16px 26px;
				padding: 0;
				display: flex;
	
				&::after {
					font-family: $font-icon-font-family;
					color: $dark-color;
					position: absolute;
					right: 24px;
					content: '\e936';
					font-size: $lead-font-size;
				}
			}
	
			.icon {
				font-size: $lead-font-size;
			}
	
			.item-title {
				margin-left: 20px;
				vertical-align: middle;
				line-height: $lead-font-size;
			}
		}
	}

	.wl-offer-items {

		.wl-offer-items-list {
			display: flex;
			flex-wrap: wrap;
			flex-direction: row;
			justify-content: center;
			align-items: center;
			gap: 20px;


			.item {
				position: relative;
				padding: 28px;
				box-shadow: $box-shadow;
				border-radius: $border-radius;
				background-color: $white-color;

				.horizontal-line {
					margin: 20px 0;
				}

				.recommended-banner {
					.icon {
						font-size: $regular-font-size;
						padding-right: 12px;
					}

					display:flex;
					visibility: hidden;
					text-align: center;
					position: absolute;
					align-items: center;
					justify-content: center;
					top: 0;
					right: 0;
					font-size: $small-font-size;
					line-height: $regular-font-size;
					font-weight: 400;
					color: $white-color;
					background-color: $dark-color;
					padding: 8px 16px;
					width: 100%;
					overflow: hidden;
					border-radius: $border-radius $border-radius 0 0;
					background: $lightblue-gradient;
				}

				&.recommended {
					padding: 50px 28px 28px 28px;
					
					.recommended-banner {
						visibility: visible;
					}
				}

				.item-image {
					width: 60px;
					height: 60px;
					padding: 5px;
					object-fit: contain;
					background-color: $light-background-color;
					border-radius: $border-radius;
					display: inline-block;
					img {
						width: 50px;
						height: 50px;
						object-fit: contain;
					}
				}

				.image-wrapper {
					display: flex;
					flex-direction: row;
					align-items: center;
					justify-content: flex-start;
					gap: 20px;
					
					.item-title {
						font-weight: 700;
					}
				}

				.item-details {
					.label {
						color: $gray200-color;
						margin-bottom: 2px;
					}

					.value {
						margin-bottom: 8px;
					}
				}

				.item-actions {
					display: flex;
					flex-direction: row;
					align-items: stretch;
					flex-wrap: nowrap;
					gap: 20px;
					justify-content: space-evenly;

					.action {
						padding: 15px 40px;
						border-radius: 100px;
						color: $white-color;
						font-weight: 700;
						cursor: pointer;
						user-select: none;
					}

					.action-accept {
						background-color: $dark-color;
					}

					.action-revoke {
						display: none;
						background-color: $danger-color;
					}

					.action-details {
						background-color: $blue-color;
					}
				}

				&.accepted {
					.item-actions {
						.action-accept {
							display: none;
						}

						.action-revoke {
							display: block;
						}
					}
				}
			}
		}
	}

	.wl-offer-details {

		.wl-offer-recommended-header {
			background: $lightblue-gradient;
			padding: 8px;

			.x-panel-header-title {
				display: flex;

				flex-direction: row;
				flex-wrap: nowrap;
				justify-content: center;
				align-items: center;
			}

			.x-title-icon {
				font-size: 24px;
				height: 24px;
			}

			.x-title-text {
				font-weight: 400;
				display: inline-block;
			}
		}

		.wl-image-wrapper {
			background-color: $light-background-color;
			padding: 0px;
			border-radius: $border-radius;
			img {
				object-fit: contain;
			}
		}

		.wl-offer-details {
			.x-form-item-label-text {
				color: $gray200-color;
			}
		}

		.x-tab-bar {
			.x-tab-default {
				padding: 8px;
				border-radius: $border-radius;
				box-shadow: $box-shadow;
			}

			.x-tab-active {

				background-color: $dark-color;

				.x-tab-inner {
					font-weight: 400;
					color: $white-color;
				}
			}
		}

		.wl-offer-parameters-table {

			.icon {				
				display: inline-block;
				width: 20px;
				height: 20px;
				margin: 0 5px;
				background-repeat: no-repeat;
				background-position: center center;

				&.icon-europe_alt_red {
					width: 24px;
					background-image: url('images/offer_icons/europe_alt_red.svg');
				}
				
				&.icon-europe_alt {
					width: 24px;
					background-image: url('images/offer_icons/europe_alt.svg');
				}
				
				&.icon-europe {
					width: 24px;
					background-image: url('images/offer_icons/europe.svg');
				}
				
				&.icon-euro {
					background-image: url('images/offer_icons/euro.svg');
				}

				&.icon-minus_alt {
					background-image: url('images/offer_icons/minus_alt.svg');
				}
				&.icon-plus_alt {
					background-image: url('images/offer_icons/plus_alt.svg');
				}
				&.icon-minus {
					background-image: url('images/offer_icons/minus.svg');
				}
				&.icon-plus {
					background-image: url('images/offer_icons/plus.svg');
				}
				&.icon-smile {
					background-image: url('images/offer_icons/smile.svg');
				}

			}

			.x-grid-item-alt, .x-grid-item-over, .x-grid-item-selected {
				background-color: transparent !important;
			}

			.x-grid-body {
				background-color: transparent;
				border: none;
			}

			.x-grid-header-ct {
				background-color: transparent;
				border: none;
			}

			.x-column-header {
				background-color: transparent;
				border: none;
				color: $dark-color;
				pointer-events: none;
			}
		
			.x-grid-cell {
				padding: 8px;
				.x-grid-cell-inner {
					position: relative;
					padding: 10px;
					background-color: $light-background-color;
					border-radius: 6px;
					min-height: 62px;
					white-space: normal;
				}

				.label {
					padding: 2px;
				}

				.value {
					padding: 2px;
					color: $gray200-color
				}

				.tooltip {
					position: absolute;
					top: 6px;
					right: 6px;
					font-family: $font-icon-font-family;
					color: $dark-color;
					font-size: $lead-font-size;
					cursor: pointer;
				}
			}

		}
	}
}