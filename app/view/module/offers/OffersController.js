Ext.define('Wl.view.module.offers.OffersController', {
  extend: 'Wl.components.panel.MainPanelController',
  alias: 'controller.offers-offers',

  routes: {
    'offers/list': 'onOffersListRoute',
    'offers/packages/:id': 'onOfferPackagesRoute',
    'offers/item/:package/:id': 'onOfferItemRoute',
  },

  gotoDefaultRoute: function () {
    this.redirectTo('offers/list')
  },

  onOffersListRoute: function () {
    const me = this
    const vm = me.getViewModel()
    const view = me.lookupReference('offersList')
    const store = vm.getStore('offers')

    me.getView().setActiveItem('offersList')

    if (!store.isLoaded()) {
      view.setLoading(true)
      Wl.api.Proposals.getPackages().then(
        (packages) => {
          vm.getStore('offers').setData(packages)
          view.setLoading(false)
        },
        () => {
          view.setLoading(false)
        }
      )
    }
  },

  onOfferClick: function (grid, record) {
    this.redirectTo(`offers/packages/${record.get('id')}`)
  },

  onOfferPackagesRoute: function (id) {
    const me = this
    const vm = me.getViewModel()
    const view = me.lookupReference('offerItems')
    const store = vm.getStore('offerItems')

    me.getView().setActiveItem('offerItems')
    view.setLoading(true)

    Wl.api.Proposals.getProposals(id).then(
      (proposals) => {
        store.setData(proposals)
        view.setLoading(false)
      },
      () => {
        view.setLoading(false)
      }
    )
  },

  gotoBackToPackage: function () {
    const me = this
    const vm = me.getViewModel()

    const packageId = vm.get('offer').get('proposalPackageId')

    this.redirectTo(`offers/packages/${packageId}`)
  },

  onOfferItemClick: function (grid, record, el, idx, evt) {
    if (Ext.dom.Query.is(evt.target, '.action-details')) {
      this.redirectTo(`offers/item/${record.get('proposalPackageId')}/${record.get('id')}`)
    }

    if (Ext.dom.Query.is(evt.target, '.action-accept')) {
      return this.acceptOfferFromPackage(record)
    }

    if (Ext.dom.Query.is(evt.target, '.action-revoke')) {
      return this.revokeOfferFromPackage(record)
    }
  },

  acceptOfferFromPackage: function (record) {
    const me = this

    const view = me.lookupReference('offerItems')
    const vm = me.getViewModel()

    view.setLoading(true)

    const id = record.get('id')

    Wl.api.Proposals.acceptProposal(id).then(
      () => {
        record.set('accepted', true)
        view.setLoading(false)
      },
      () => {
        view.setLoading(false)
        Wl.toast({
          msg: translate('OFFERS.PACKAGE.TOAST.ERROR_ACCEPTING_OFFER.MESSAGE'),
          ui: 'error',
        })
      }
    )
  },
  revokeOfferFromPackage: function (record) {
    const me = this
    const view = me.lookupReference('offerItems')
    const vm = me.getViewModel()

    view.setLoading(true)

    const id = record.get('id')

    Wl.api.Proposals.revokeProposal(id).then(
      () => {
        record.set('accepted', false)
        view.setLoading(false)
      },
      () => {
        view.setLoading(false)
        Wl.toast({
          msg: translate('OFFERS.PACKAGE.TOAST.ERROR_REVOKING_OFFER.MESSAGE'),
          ui: 'error',
        })
      }
    )
  },

  acceptOfferFromDetails: function () {
    const me = this
    const view = me.lookupReference('offerItem')
    const vm = me.getViewModel()

    view.setLoading(true)

    const id = vm.get('offer').get('id')

    Wl.api.Proposals.acceptProposal(id).then(
      () => {
        vm.get('offer').set('status', 'accepted')
        vm.get('offer').set('accepted', 'true')
        view.setLoading(false)
      },
      () => {
        view.setLoading(false)
        Wl.toast({
          msg: translate('OFFERS.DETAILS.TOAST.ERROR_ACCEPTING_OFFER.MESSAGE'),
          ui: 'error',
        })
      }
    )
  },

  revokeOfferFromDetails: function () {
    const me = this
    const view = me.lookupReference('offerItem')
    const vm = me.getViewModel()

    view.setLoading(true)

    const id = vm.get('offer').get('id')

    Wl.api.Proposals.revokeProposal(id).then(
      () => {
        vm.get('offer').set('status', 'revoked')
        vm.get('offer').set('accepted', 'false')
        view.setLoading(false)
      },
      () => {
        view.setLoading(false)
        Wl.toast({
          msg: translate('OFFERS.DETAILS.TOAST.ERROR_REVOKING_OFFER.MESSAGE'),
          ui: 'error',
        })
      }
    )
  },

  onOfferItemRoute: function (packageId, id) {
    const me = this
    const vm = me.getViewModel()
    const view = me.lookupReference('offerItem')
    const parametersTable = me.lookupReference('parametersTable')

    parametersTable.removeAll()
    me.getView().setActiveItem('offerItem')
    view.setLoading(true)

    Wl.api.Proposals.getProposal(packageId, id).then(
      (proposal) => {
        vm.linkTo('offer', {
          create: proposal,
          type: 'Wl.model.Proposal',
        })
        const tables = vm.get('offer').getParametersTable()

        parametersTable.add(
          tables.map(function (table) {
            return {
              xtype: 'grid',
              title: table.name,
              cls: 'wl-offer-parameters-table',
              disableSelection: true,
              focusOnToFront: false,
              rowLines: false,
              columns: [
                {
                  flex: 1,
                  text: translate('OFFERS.DETAILS.PARAMETERS_TABLE.DESCRIPTION_COLUMN.HEADER'),
                  defaults: {
                    sortable: false,
                    resizable: false,
                    locked: true,
                    hideable: false,
                    draggable: false,
                  },
                  xtype: 'templatecolumn',
                  tpl: [
                    '<tpl if="label">',
                    '<div class="label">{label}</div>',
                    '<div class="tooltip" data-qtip="{description}">',
                    '<i class="icon icon-info-circle"></i>',
                    '</div>',
                    '<div class="value">{value}</div>',
                    '</tpl>',
                  ],
                },
                {
                  width: 150,
                  text: translate('OFFERS.DETAILS.PARAMETERS_TABLE.CONDITIONS_COLUMN.HEADER'),
                  align: 'end',
                  defaults: {
                    sortable: false,
                    resizable: false,
                    locked: true,
                    hideable: false,
                    draggable: false,
                  },
                  xtype: 'templatecolumn',
                  tpl: [
                    '<tpl for="icons">',
                    '<i class="status-icon icon icon-{icon}" data-qtip="{tooltip}"></i>',
                    '</tpl>',
                  ],
                },
              ],
              store: {
                data: table.parameters,
              },
            }
          })
        )

        parametersTable.setActiveItem(0)

        view.setLoading(false)
      },
      () => {
        view.setLoading(false)
      }
    )
  },
})
