Ext.define('Wl.view.module.offers.OffersView', {
  extend: 'Wl.view.module.Module',
  xtype: 'wl-offers',
  bodyCls: 'wl-offers',

  requires: ['Wl.view.module.offers.OffersController', 'Wl.view.module.offers.OffersModel'],

  controller: 'offers-offers',
  viewModel: {
    type: 'offers-offers',
  },

  defaults: {
    titleAlign: 'center',
    scrollable: true,
  },

  layout: 'card',

  items: [
    {
      xtype: 'panel',
      itemId: 'offersList',
      reference: 'offersList',
      title: translate('OFFERS.TITLE'),
      bodyPadding: 20,
      ui: 'module',
      cls: 'wl-offers-list',
      scrollable: {
        y: true,
        x: false,
      },

      bbar: {
        xtype: 'pagingtoolbar',
        displayInfo: true,
        store: {},
        hidden: true, // For now API does not support pagination
        bind: {
          store: '{offers}',
        },
      },

      items: [
        {
          xtype: 'panel',

          cls: 'wl-offers-list-wrapper',

          layout: { type: 'vbox', align: 'stretch' },

          width: 800,

          responsiveConfig: {
            'width < 1150': {
              layout: { align: 'stretch' },
              width: '100%',
            },
            'width >= 1150': {
              width: 800,
              layout: { pack: 'middle' },
            },
          },

          items: [
            {
              xtype: 'component',
              cls: 'wl-offers-header',
              html: translate('OFFERS.PACKAGES_LIST.TITLE'),
            },
            {
              xtype: 'grid',
              reference: 'offersTree',
              padding: 20,
              disableSelection: true,
              focusOnToFront: false,
              requires: ['Ext.grid.feature.Grouping'],
              rowLines: false,
              hideHeaders: true,
              emptyText: translate('OFFERS.PACKAGES_LIST.EMPTY_TEXT'),
              features: [
                {
                  ftype: 'grouping',
                  startCollapsed: false,
                  groupHeaderTpl: '{name}',
                },
              ],

              columns: [
                {
                  dataIndex: 'name',
                  flex: 1,
                  xtype: 'templatecolumn',
                  tpl: '<i class="icon icon-gift"></i><span class="item-title">{name}</span>',
                },
              ],
              store: {},
              bind: {
                store: '{offers}',
              },
              listeners: {
                itemclick: 'onOfferClick',
              },
            },
          ],
        },
      ],
    },
    {
      itemId: 'offerItems',
      reference: 'offerItems',
      userCls: 'wl-offer-items',

      xtype: 'panel',
      title: translate('OFFERS.OFFERS_LIST.TITLE'),
      bodyPadding: 20,
      ui: 'module',
      scrollable: {
        y: true,
        x: false,
      },
      titlePosition: 1,
      tools: [
        {
          type: 'left',
          tooltip: translate('OFFERS.OFFERS_LIST.BACK_BUTTON.TEXT'),
          handler: 'gotoDefaultRoute',
        },
        {
          xtype: 'button',
          ui: 'module-tool',
          hidden: true,
          text: translate('OFFERS.OFFERS_LIST.COMPARE_BUTTON.TEXT'),
          iconCls: 'icon-element',
        },
      ],
      layout: 'fit',

      items: [
        {
          xtype: 'dataview',
          reference: 'offerItemsList',
          store: {},
          cls: 'wl-offer-items-list',
          bind: {
            store: '{offerItems}',
          },
          listeners: {
            itemclick: 'onOfferItemClick',
          },
          itemSelector: 'div.item',
          itemTpl: new Ext.XTemplate(
            '<tpl for=".">',
            '<div class="item {[this.getAdditionalClasses(values)]}">',
            '<div class="recommended-banner"><i class="icon icon-star"></i><span class="text">{[translate("OFFERS.OFFERS_LIST.ITEM.HEADER.RECOMMENDED.TEXT")]}</span></div>',

            '<div class="image-wrapper">',
            '<div class="item-image">',
            '<img src="{insurerLogo}" />',
            '</div>',
            '<div class="item-title">{company}</div>',
            '</div>',
            '<div class="horizontal-line"></div>',
            '<div class="item-details">',
            '<div class="label">{[translate("OFFERS.OFFERS_LIST.ITEM.TARIFF_FIELD.LABEL")]}</div>',
            '<div class="value">{tariff}</div>',
            '<div class="label">{[translate("OFFERS.OFFERS_LIST.ITEM.COVERAGE_AMOUNT_FIELD.LABEL")]}</div>',
            '<div class="value">{insuredAmount}</div>',
            '<div class="label">{[translate("OFFERS.OFFERS_LIST.ITEM.CONTRIBUTION_FIELD.LABEL")]}</div>',
            '<div class="value">{contribution}</div>',
            '<div class="label">{[translate("OFFERS.OFFERS_LIST.ITEM.STARTING_AT_FIELD.LABEL")]}</div>',
            '<div class="value">{startingAt}</div>',
            '<div class="label">{[translate("OFFERS.OFFERS_LIST.ITEM.PAYMENT_MODE_FIELD.LABEL")]}</div>',
            '<div class="value">{paymentMode}</div>',
            '</div>',
            '<div class="horizontal-line"></div>',
            '<div class="item-actions">',
            '<div class="action action-details">{[translate("OFFERS.OFFERS_LIST.ITEM.DETAILS_BUTTON.TEXT")]}</div>',
            '<div class="action action-revoke">{[translate("OFFERS.OFFERS_LIST.ITEM.REVOKE_BUTTON.TEXT")]}</div>',
            '<div class="action action-accept">{[translate("OFFERS.OFFERS_LIST.ITEM.ACCEPT_BUTTON.TEXT")]}</div>',
            '</div>',
            '</div>',
            '</tpl>',
            {
              getAdditionalClasses: function (values) {
                const classes = []
                if (values.recommended) {
                  classes.push('recommended')
                }

                if (values.accepted) {
                  classes.push('accepted')
                }

                return classes.join(' ')
              },
              getImageUrl: function (values) {
                if (values.productInfo.providerImgId) {
                  const id = values.providerInfo.providerImgId

                  return `${Wl.Env.getApiBaseUrl()}/file?document_id=${id}&download=0&access_token=${Wl.api.SSO.getAccessToken()}`
                }

                if (values.productInfo.logos) {
                  return values.productInfo.logos.versicherer
                }
              },
            }
          ),
        },
      ],
    },
    {
      xtype: 'form',
      itemId: 'offerItem',
      reference: 'offerItem',
      title: translate('OFFERS.DETAILS.TITLE'),

      ui: 'module',

      userCls: 'wl-offer-details',

      tools: [
        {
          type: 'left',
          tooltip: translate('OFFERS.DETAILS.BACK_BUTTON.TEXT'),
          handler: 'gotoBackToPackage',
        },
      ],

      titlePosition: 1,

      layout: 'center',
      responsiveConfig: {
        'width >= 1150': {
          bodyPadding: 32,
        },
      },
      items: [
        {
          xtype: 'panel',
          cls: 'wl-offer-item-wrapper',
          layout: {
            type: 'vbox',
            align: 'stretch',
          },
          bodyPadding: 20,

          header: {
            title: translate('OFFERS.DETAILS.HEADER.RECOMMENDED.TITLE'),
            titleAlign: 'center',
            cls: 'wl-offer-recommended-header',
            iconCls: 'icon-star',
            bind: {
              hidden: '{!offer.recommended}',
            },
          },

          responsiveConfig: {
            'width < 1150': {
              userCls: '',
              width: '100%',
            },
            'width >= 1150': {
              userCls: 'wl-shadow wl-border-radius',
              width: 820,
            },
          },

          items: [
            {
              xtype: 'panel',
              layout: {
                type: 'hbox',
              },
              responsiveConfig: {
                'width < 1150': {},
                'width >= 1150': {},
              },
              items: [
                {
                  xtype: 'container',
                  padding: 20,
                  layout: {
                    type: 'vbox',
                    align: 'center',
                  },
                  items: [
                    {
                      xtype: 'container',
                      cls: 'wl-image-wrapper',
                      items: [
                        {
                          xtype: 'image',
                          height: 260,
                          width: 260,
                          margin: 10,
                          bind: {
                            src: '{offer.insurerLogo}',
                          },
                        },
                      ],
                    },
                    {
                      xtype: 'button',
                      margin: 10,
                      text: translate('OFFERS.DETAILS.ACCEPT_BUTTON.TEXT'),
                      handler: 'acceptOfferFromDetails',
                      bind: {
                        hidden: '{offer.accepted}',
                      },
                    },
                    {
                      xtype: 'button',
                      margin: 10,
                      cls: 'wl-button-revoke',
                      text: translate('OFFERS.DETAILS.REVOKE_BUTTON.TEXT'),
                      handler: 'revokeOfferFromDetails',
                      bind: {
                        hidden: '{!offer.accepted}',
                      },
                    },
                  ],
                },
                {
                  xtype: 'container',
                  flex: 1,

                  cls: 'wl-offer-details',

                  padding: 20,
                  defaults: {
                    xtype: 'displayfield',
                    labelAlign: 'top',
                    labelStyle: 'text-align: left;',
                    labelSeparator: '',
                    msgTarget: 'under',
                    allowBlank: false,
                  },

                  items: [
                    {
                      xtype: 'component',
                      cls: 'wl-title',
                      bind: {
                        html: '{offer.company}',
                      },
                    },
                    {
                      xtype: 'component',
                      cls: 'horizontal-line',
                      margin: '24 0 0 0',
                    },
                    {
                      fieldLabel: translate('OFFERS.DETAILS.TARIFF_FIELD.LABEL'),
                      bind: {
                        value: '{offer.tariff}',
                      },
                    },
                    {
                      fieldLabel: translate('OFFERS.DETAILS.COVERAGE_AMOUNT_FIELD.LABEL'),
                      bind: {
                        value: '{offer.insuredAmount}',
                      },
                    },
                    {
                      fieldLabel: translate('OFFERS.DETAILS.CONTRIBUTION_FIELD.LABEL'),
                      bind: {
                        value: '{offer.contribution}',
                      },
                    },
                    {
                      fieldLabel: translate('OFFERS.DETAILS.STARTING_AT_FIELD.LABEL'),
                      bind: {
                        value: '{offer.startingAt}',
                      },
                    },
                    {
                      fieldLabel: translate('OFFERS.DETAILS.PAYMENT_MODE_FIELD.LABEL'),
                      bind: {
                        value: '{offer.paymentMode}',
                      },
                    },
                  ],
                },
              ],
            },
            {
              xtype: 'tabpanel',
              flex: 1,
              reference: 'parametersTable',
              tabBar: {
                baseCls: 'wl-tab-bar',
              },
              items: [],
            },
          ],
        },
      ],
    },
  ],
})
