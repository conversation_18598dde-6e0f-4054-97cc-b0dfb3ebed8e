Ext.define('Wl.view.signup.SignupView', {
  extend: 'Wl.view.fullscreencontainer.FullscreenContainerView',
  xtype: 'wl-signup',

  baseCls: 'wl-signup',
  items: [
    {
      xtype: 'container',

      layout: 'card',

      controller: 'signup',
      viewModel: 'signup',
      bodyPadding: 0,

      width: 800,

      items: [
        {
          xtype: 'mainForm',
          tools: [
            {
              type: 'left',
              tooltip: translate('LOGIN.SIGNUP.BACK_BUTTON.TOOLTIP'),
              handler: 'onBack',
            },
          ],

          bodyPadding: '50 220',
          titlePosition: 10,
          itemId: 'signupForm',
          reference: 'signupForm',

          items: [
            {
              xtype: 'component',
              style: {
                margin: '20px 0',
              },
              cls: 'wl-title',
              html: translate('LOGIN.SIGNUP.TITLE'),
            },
            {
              xtype: 'wl-selectfield',
              fieldLabel: translate('LOGIN.SIGNUP.SALUTATION_FIELD.LABEL'),
              name: 'salutation',
              allowBlank: false,
              bind: {
                value: '{salutation}',
              },

              options: [
                {
                  value: 'MISTER',
                  name: translate('LOGIN.SIGNUP.SALUTATION_FIELD.OPTION.MISTER'),
                },
                {
                  value: 'MISSIS',
                  name: translate('LOGIN.SIGNUP.SALUTATION_FIELD.OPTION.MISSIS'),
                },
                {
                  value: 'UNKNOWN',
                  name: translate('LOGIN.SIGNUP.SALUTATION_FIELD.OPTION.UNKNOWN'),
                },
                {
                  value: 'COMPANY',
                  name: translate('LOGIN.SIGNUP.SALUTATION_FIELD.OPTION.COMPANY'),
                },
              ],
            },
            {
              xtype: 'textfield',
              fieldLabel: translate('LOGIN.SIGNUP.FIRSTNAME_FIELD.LABEL'),
              name: 'firstName',
              allowBlank: false,
              bind: {
                disabled: '{isCompany}',
                value: '{firstName}',
              },
            },
            {
              xtype: 'textfield',
              fieldLabel: translate('LOGIN.SIGNUP.LASTNAME_FIELD.LABEL'),
              name: 'lastName',
              allowBlank: false,
              bind: {
                disabled: '{isCompany}',
                value: '{lastName}',
              },
            },
            {
              xtype: 'fieldcontainer',
              fieldLabel: translate('LOGIN.SIGNUP.PHONE_FIELD.LABEL'),
              layout: { type: 'column' },
              allowBlank: false,
              defaults: {
                allowBlank: false,
                msgTarget: 'under',
              },
              items: [
                {
                  xtype: 'combobox',
                  width: 110,
                  name: 'phonePrefix',
                  reference: 'phonePrefixField',
                  valueField: 'phonePrefix',
                  displayField: 'phonePrefixWithFlag',
                  store: 'countries',
                  bind: {
                    value: '{phonePrefix}',
                  },
                },
                {
                  xtype: 'textfield',
                  name: 'phoneNumber',
                  columnWidth: 1,
                  padding: '0 0 0 10',
                  bind: {
                    value: '{phoneNumber}',
                  },
                },
              ],
            },
            {
              xtype: 'textfield',
              fieldLabel: translate('LOGIN.SIGNUP.EMAIL_FIELD.LABEL'),
              name: 'email',
              vtype: 'email',
              allowBlank: false,
              bind: {
                value: '{email}',
              },
            },
            {
              xtype: 'wl-passwordfield',
              fieldLabel: translate('LOGIN.SIGNUP.FIELD_PASSWORD.LABEL'),
              name: 'password',
              allowBlank: false,
              bind: {
                value: '{password}',
              },
            },
            {
              xtype: 'wl-passwordfield',
              fieldLabel: translate('LOGIN.SIGNUP.FIELD_PASSWORD_REPEAT.LABEL'),
              name: 'passwordRepeat',
              allowBlank: false,
              bind: {
                value: '{passwordRepeat}',
              },
              validator: function (value) {
                const password = this.up('form').down('[name=password]').getValue()
                return password === value ? true : translate('LOGIN.SIGNUP.FIELD_PASSWORD_REPEAT.INVALID.MESSAGE')
              },
            },
            {
              xtype: 'button',
              style: {
                marginTop: '40px',
              },
              handler: 'doSignup',
              anchor: '100%',
              text: translate('LOGIN.SIGNUP.SUBMIT_BUTTON.TEXT'),
            },
            {
              xtype: 'component',
              autoEl: 'p',
              anchor: '100%',
              style: {
                textAlign: 'center',
                marginTop: '40px',
              },
              html: translate('LOGIN.SIGNUP.TERMS_AND_CONDITIONS.TEXT'),
            },
          ],
        },
        {
          xtype: 'panel',
          layout: { type: 'vbox', align: 'center' },
          itemId: 'signupSuccess',
          bodyPadding: '140 220',
          items: [
            {
              xtype: 'container',
              layout: 'center',
              items: [
                {
                  xtype: 'image',
                  width: 200,
                  height: 170,
                  src: Ext.getResourcePath('images/success.png'),
                },
              ],
            },
            {
              xtype: 'component',
              style: {
                margin: '20px 0',
              },
              cls: 'wl-title',
              html: translate('LOGIN.SIGNUP_SUCCESS.TITLE'),
            },
            {
              xtype: 'component',
              style: {
                margin: '20px 0',
                textAlign: 'center',
              },
              html: translate('LOGIN.SIGNUP_SUCCESS.DESCRIPTION'),
            },
            {
              xtype: 'button',
              style: {
                marginTop: '40px',
              },
              handler: 'goToLogin',
              text: translate('LOGIN.SIGNUP_SUCCESS.CONFIRM_BUTTON.TEXT'),
            },
          ],
        },
      ],
    },
  ],
})
