Ext.define('Wl.view.signup.SignupController', {
  extend: 'Wl.components.panel.MainPanelController',

  alias: 'controller.signup',

  routes: {
    signup: 'onSignupRoute',
  },

  onBack: function () {
    this.redirectTo('welcome')
  },

  doSignup: function () {
    const me = this
    const formView = me.lookupReference('signupForm')

    formView.setLoading(true)

    if (!formView.isValid()) {
      formView.setLoading(false)
      Wl.toast({
        ui: 'error',
        msg: translate('LOGIN.SIGNUP.TOAST.INVALID.MESSAGE'),
      })
      return
    } else {
      Wl.api.Signup.signup(me.getApiData()).then(
        function (response) {
          me.onSignupSuccess()

          formView.setLoading(false)
        },
        function (error) {
          Wl.toast({
            ui: 'error',
            msg: translate('LOGIN.SIGNUP.TOAST.ERROR.MESSAGE'),
          })

          formView.setLoading(false)
        }
      )
    }
  },

  /* @private */
  getApiData: function () {
    const vm = this.getViewModel()

    return {
      email: vm.get('email'),
      password: vm.get('password'),
      passwordConfirm: vm.get('passwordRepeat'),
      first_name: vm.get('firstName'),
      last_name: vm.get('lastName'),
      phone: vm.get('phonePrefix') + vm.get('phoneNumber'),
      salutation: vm.get('salutation'),
      type: 'PERSON',
    }
  },

  onSignupRoute: function () {
    this.getView().setActiveItem('signupForm')
    const field = this.lookupReference('phonePrefixField')

    field.setValue('DE')
  },

  onSignupSuccess: function () {
    Wl.toast({
      ui: 'success',
      msg: translate('LOGIN.SIGNUP.TOAST.SUCCESS.MESSAGE'),
    })

    this.getView().setActiveItem('signupSuccess')
  },

  goToLogin: function () {
    this.redirectTo('login')
  },
})
