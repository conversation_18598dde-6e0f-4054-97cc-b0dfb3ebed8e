Ext.define('Wl.view.signup.SignupModel', {
  extend: 'Ext.app.ViewModel',
  alias: 'viewmodel.signup',

  data: {
    salutation: 'UNKNOWN',
    phonePrefix: null,
  },

  formulas: {
    isCompany: function (get) {
      return get('salutation') === 'COMPANY'
    },
  },

  stores: {
    countryPrefixes: {
      fields: ['value', 'name'],
      type: 'json',
      autoLoad: true,
      proxy: {
        type: 'ajax',
        url: Ext.getResourcePath('data/countryPrefixes.json'),
      },
    },
  },
})
