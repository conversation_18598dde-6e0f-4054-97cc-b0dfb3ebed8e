Ext.define('Wl.view.welcome.WelcomeView', {
  extend: 'Wl.view.fullscreencontainer.FullscreenContainerView',
  xtype: 'wl-welcome',

  baseCls: 'wl-welcome',
  controller: 'welcome',

  items: [
    {
      xtype: 'container',
      layout: {
        type: 'vbox',
        pack: 'start',
        align: 'stretch',
      },

      padding: '25 120',
      items: [
        {
          xtype: 'container',
          layout: 'center',
          items: [
            {
              xtype: 'image',
              src: Ext.getResourcePath('images/branding/logo.png'),
              width: 242,
              height: 186,
              style: {
                marginBottom: '80px',
              },
            },
          ],
        },
        {
          xtype: 'component',
          cls: 'wl-title',
          style: {
            textAlign: 'center',
          },
          html: translate('LOGIN.WELCOME.TITLE'),
        },
        {
          xtype: 'component',
          style: {
            textAlign: 'center',
          },
          autoEl: 'p',
          margin: 20,
          html: translate('LOGIN.WELCOME.DESCRIPTION'),
        },
        {
          xtype: 'button',
          margin: '20 0 0 0',
          text: translate('LOGIN.WELCOME.SIGNUP_BUTTON.TEXT'),
          handler: 'doSignup',
        },
        {
          xtype: 'button',
          ui: 'secondary',
          margin: '20 0 0 0',
          text: translate('LOGIN.WELCOME.LOGIN_BUTTON.TEXT'),
          handler: 'doLogin',
        },
      ],
    },
  ],
})
