Ext.define('Wl.view.ModalWindow', {
  extend: 'Ext.window.Window',

  layout: 'fit',

  width: 700,
  height: '90%',
  modal: true,
  resizable: false,
  maximizable: false,
  closable: true,
  border: false,
  frame: false,
  cls: 'wl-help-modal',
  scrollable: true,
  defaultAlign: 'c-c',
  // bodyPadding: 10,
  defaults: {
    xtype: 'component',
    margin: 10,
    style: 'text-align: center;',
  },

  constructor: function (config) {
    config = config || {}

    Ext.applyIf(config, {
      dockedItems: [
        {
          xtype: 'toolbar',
          dock: 'bottom',

          items: [
            '->',
            {
              xtype: 'button',
              text: translate('HELP.HELP_MODAL.CLOSE_BUTTON.TEXT'),
              handler: function () {
                this.close()
              },
              scope: this,
            },
            '->',
          ],
        },
      ],
    })

    this.callParent(arguments)
  },

  close: function () {
    this.callParent(arguments)
  },
})
