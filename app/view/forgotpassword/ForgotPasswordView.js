Ext.define('Wl.view.forgotpassword.ForgotPasswordView', {
  extend: 'Wl.view.fullscreencontainer.FullscreenContainerView',
  xtype: 'wl-forgotpassword',

  baseCls: 'wl-forgotpassword',

  items: [
    {
      xtype: 'container',

      controller: 'forgotpassword',

      viewModel: 'forgotpassword',

      bodyPadding: 0,
      layout: 'card',

      width: 800,

      items: [
        {
          tools: [
            {
              type: 'left',
              tooltip: translate('LOGIN.FORGOT_PASSWORD.BACK_BUTTON.TOOLTIP'),
              handler: 'onBack',
            },
          ],

          xtype: 'mainForm',
          reference: 'forgotPasswordForm',
          titlePosition: 10,
          bodyPadding: '0 220 50 220',

          items: [
            {
              xtype: 'component',
              style: {
                margin: '20px 0',
              },
              cls: 'wl-title',
              html: translate('LOGIN.FORGOT_PASSWORD.TITLE'),
            },
            {
              fieldLabel: translate('LOGIN.FORGOT_PASSWORD.USERNAME_FIELD.LABEL'),
              name: 'username',
              allowBlank: false,
              bind: {
                value: '{username}',
              },
            },
            {
              xtype: 'component',
              height: 20,
            },
            {
              xtype: 'button',
              style: {
                marginTop: '40px',
              },
              text: translate('LOGIN.FORGOT_PASSWORD.SUBMIT_BUTTON.TEXT'),
              handler: 'doResetPassword',
            },
          ],
        },
        {
          xtype: 'panel',
          layout: { type: 'vbox', align: 'center' },
          bodyPadding: '40',
          itemId: 'resetSuccess',
          items: [
            {
              xtype: 'container',
              layout: 'center',
              items: [
                {
                  xtype: 'image',
                  width: 200,
                  height: 170,
                  src: Ext.getResourcePath('images/success.png'),
                },
              ],
            },

            {
              xtype: 'component',
              style: {
                margin: '20px 0',
              },
              cls: 'wl-title',
              html: translate('LOGIN.FORGOT_PASSWORD_SUCCESSFUL.TITLE.TEXT'),
            },
            {
              xtype: 'component',
              style: {
                margin: '20px 0',
                textAlign: 'center',
              },
              html: translate('LOGIN.FORGOT_PASSWORD_SUCCESSFUL.DESCRIPTION.TEXT'),
            },
            {
              xtype: 'button',
              style: {
                marginTop: '40px',
              },
              handler: 'onResetSuccess',
              text: translate('LOGIN.FORGOT_PASSWORD_SUCCESSFUL.BUTTON.TEXT'),
            },
          ],
        },
      ],
    },
  ],
})
