Ext.define('Wl.view.forgotpassword.ForgotPasswordController', {
  extend: 'Wl.components.panel.MainPanelController',

  alias: 'controller.forgotpassword',

  doResetPassword: function () {
    const me = this
    const vm = me.getViewModel()

    if (this.lookupReference('forgotPasswordForm').isValid()) {
      Wl.Request.post(`${Wl.Env.getSSOUrl()}/b2c/forgot-password`, {
        skipAuth: true,
        params: {
          username: vm.get('username'),
        },
      }).then(
        function (response) {
          me.getView().setActiveItem('resetSuccess')
        },
        function (error) {
          Wl.toast({
            ui: 'error',
            msg: translate('LOGIN.FORGOT_PASSWORD.TOAST.FAILED.MESSAGE'),
          })
        }
      )
    }
  },

  onBack: function () {
    this.redirectTo('login')
  },

  onResetSuccess: function () {
    this.redirectTo('login')
  },
})
