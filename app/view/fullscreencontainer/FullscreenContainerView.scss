.wl-fullscreencontainer {
  background-image: url('images/branding/background.png');
  background-repeat: no-repeat;
  background-size: cover;

  // Panel styles interfere with containers border radius and box shadow
  // these classes are used to override the panel styles so it uses containers background
  .x-panel-body-default {
    background-color: transparent;
  }

  .x-panel-header-default {
    background-color: transparent;
    color: $dark-color;

    &-horizontal.x-header {
      padding: 10px 20px;
    }

    .x-tool-tool-el {
      color: $dark-color;
    }
  }
}

.wl-fullscreencontainer-item {
  box-shadow: $box-shadow;
  border-radius: $border-radius;
  background-color: $panel_body_background_color;
}
