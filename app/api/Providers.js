Ext.define('Wl.api.Providers', {
  extend: 'Wl.api.Connection',
  singleton: true,

  getAll: function () {
    return this.get('/providers').then((response) => {
      return Wl.optional('_embedded.providers', Ext.decode(response.responseText)) || []
    }).then((providers) => {
      return providers.map((provider) => {
        provider.categories = provider.categories.split(',')
        return provider
      })
    })
  },
})
