Ext.define('Wl.api.Contracts', {
  extend: 'Wl.api.Connection',

  singleton: true,

  getAll: function (id) {
    return this.get('/contracts-simple/').then((response) => {
      return Wl.optional('data', Ext.decode(response.responseText) || {}) || []
    })
  },

  getById: function (id) {
    return this.get(`/contracts/${id}`).then((response) => {
      return Ext.decode(response.responseText) || {}
    })
  },

  getDocuments: function (id) {
    return this.get(`/contracts/${id}/documents`).then((response) => {
      const documents = Ext.decode(response.responseText)
      return Wl.optional('data', documents) || []
    })
  },

  updateContract: function (id, data) {
    return this.put(`/contracts/${id}`, {
      params: data,
    }).then((response) => {
      return Ext.decode(response.responseText) || {}
    })
  },

  createContract: function (data) {
    return this.post(`/contracts/`, {
      params: data,
    }).then((response) => {
      return Ext.decode(response.responseText) || {}
    })
  },
})
