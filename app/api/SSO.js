Ext.define(
  'Wl.api.SSO',
  {
    extend: 'Ext.Base',
    singleton: true,

    pendingTokenRefreshPromise: null,

    requires: ['Wl.Env', 'Wl.util.State'],

    login: function (username, password) {
      const me = this

      return Ext.Ajax.request({
        url: `${Wl.Env.getSSOUrl()}/oauth`,
        method: 'POST',
        jsonData: {
          username: username,
          grant_type: 'password',
          scope: 'client',
          client_id: Wl.Env.clientId,
          password: password,
        },
      })
        .then(function (response) {
          const data = Ext.decode(response.responseText)
          me.setAccessToken(data.access_token, data.expires_in)
          me.setRefreshToken(data.refresh_token, data.refresh_expires_in)
          return Wl.api.Profile.getUserInfo()
        })
        .then(function (profile) {
          me.setUserInfo(profile)
          return profile
        })
        .then(function () {
          return Wl.state.resolve('isLoggedIn', true)
        })
    },

    logout: function () {
      const me = this
      return Ext.Ajax.request({
        url: `${Wl.Env.getSSOUrl()}/logout/${me.getAccessToken()}`,
        method: 'DELETE',
      }).then(me._reloadApp, me._reloadApp, Ext.emptyFn, me)
    },

    _reloadApp: function () {
      this.clearTokens()

      Ext.GlobalEvents.fireEvent('logout')
      window.location.hash = '#welcome'
      location.reload()
    },

    getUserInfo: function () {
      const mainViewModel = Globals.getMainViewModel()
      return mainViewModel ? mainViewModel.get('clientProfile') : null
    },

    getAgentInfo: function () {
      const mainViewModel = Globals.getMainViewModel()
      return mainViewModel ? mainViewModel.get('agentProfile') : null
    },

    setUserInfo: function (userInfo) {
      const mainViewModel = Globals.getMainViewModel()
      userInfo && mainViewModel.set('clientProfile', userInfo)
    },

    refreshUserInfo: function () {
      const me = this
      return Wl.api.Profile.getUserInfo().then(function (profile) {
        me.setUserInfo(profile)
        return profile
      })
    },

    clearTokens: function () {
      localStorage.removeItem('accessToken')
      localStorage.removeItem('accessTokenExpires')
      localStorage.removeItem('refreshToken')
    },

    setAccessToken: function (accessToken, expires) {
      localStorage.setItem('accessToken', accessToken)
      localStorage.setItem('accessTokenExpires', new Date().getTime() + expires * 1000)
    },

    getAccessToken: function () {
      return localStorage.getItem('accessToken')
    },

    setRefreshToken: function (refreshToken) {
      localStorage.setItem('refreshToken', refreshToken)
    },

    getRefreshToken: function () {
      return localStorage.getItem('refreshToken')
    },

    isLoggedIn: function () {
      return !!this.getAccessToken()
    },

    refreshAccessToken: function () {
      const me = this

      if (me.pendingTokenRefreshPromise) {
        return me.pendingTokenRefreshPromise
      }

      const deferred = new Ext.Deferred()

      Ext.Ajax.request({
        url: `${Wl.Env.getSSOUrl()}/oauth`,
        method: 'POST',
        jsonData: {
          grant_type: 'refresh_token',
          refresh_token: me.getRefreshToken(),
        },
        success: function (response) {
          me.pendingTokenRefreshPromise = null
          const data = Ext.decode(response.responseText)
          if (data.refresh_token && data.access_token) {
            me.setRefreshToken(data.refresh_token)
            me.setAccessToken(data.access_token, data.expires_in)
            deferred.resolve()
          } else {
            deferred.reject('Invalid refresh token')
          }
        },
        failure: function (response) {
          deferred.reject(response)
        },
      })

      me.pendingTokenRefreshPromise = deferred.promise

      return deferred.promise
    },

    changePassword: function (oldPassword, newPassword) {
      const me = this

      const deferred = new Ext.Deferred()

      Ext.Ajax.request({
        url: `${Wl.Env.getSSOUrl()}/oauth/change-password`,
        method: 'POST',
        jsonData: {
          current_password: oldPassword,
          new_password: newPassword,
          repeat_password: newPassword,
        },
        success: function (response) {
          const data = Ext.decode(response.responseText)
          if (data) {
            deferred.resolve()
          } else {
            deferred.reject('Could not change password')
          }
        },
        failure: function (response) {
          deferred.reject(response)
        },
      })

      me.pendingTokenRefreshPromise = deferred.promise

      return deferred.promise
    },

    changePasswordWithToken: function (token, newPassword) {
      return Ext.Ajax.request({
        url: `${Wl.Env.getSSOUrl()}/password-reset/update`,
        method: 'POST',
        jsonData: {
          token: token,
          password: newPassword,
          confirmpassword: newPassword,
        },
      })
    },
  },
  function (SSO) {
    if (SSO.isLoggedIn()) {
      Wl.state.resolve('isLoggedIn', true)
      Ext.util.TaskManager.newTask({
        run: function () {
          SSO.refreshAccessToken()
        },
        interval: 1000 * 60 * 2,
      }).start()
    }
  }
)
