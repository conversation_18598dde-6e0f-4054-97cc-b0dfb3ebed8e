Ext.define('Wl.api.Connection', {
  extend: 'Ext.data.Connection',
  requires: ['Wl.api.SSO'],

  constructor: function (config) {
    this.callParent([config])
  },

  skipAuth: false,

  get: function (url, options) {
    return this.request(Ext.apply({}, options, { url: url, method: 'GET' }))
  },
  post: function (url, options) {
    return this.request(Ext.apply({}, options, { url: url, method: 'POST' }))
  },
  put: function (url, options) {
    return this.request(Ext.apply({}, options, { url: url, method: 'PUT' }))
  },
  patch: function (url, options) {
    return this.request(Ext.apply({}, options, { url: url, method: 'PATCH' }))
  },
  delete: function (url, options) {
    return this.request(Ext.apply({}, options, { url: url, method: 'DELETE' }))
  },

  /** @private */
  request: function (options) {
    const me = this
    // TODO: for now we do not check expiration time in advance. We can check it
    // in the future if we want to avoid unnecessary requests.

    if (options.url.indexOf('http') !== 0) {
      options.url = Wl.Env.getApiBaseUrl() + options.url
    }

    if (!options.skipAuth) {
      options.headers = Ext.apply(
        {
          Authorization: 'Bearer ' + Wl.api.SSO.getAccessToken(),
          'Wl-Authorization': 'Bearer ' + Wl.api.SSO.getAccessToken(),
        },
        options.headers
      )
    }
    const deferred = new Ext.Deferred()
    const originalOptions = Ext.apply({}, options)
    const newOptions = Ext.applyIf(
      {
        failure: Ext.emptyFn,
        success: Ext.emptyFn,
        callback: function (options, success, response) {
          if (!success) {
            me.onError(originalOptions, deferred, response)
          } else {
            if (originalOptions.callback) {
              Ext.callback(originalOptions.callback, originalOptions.scope, [originalOptions, true, response])
            }

            deferred.resolve(response)
          }
        },
      },
      options
    )

    newOptions.headers = Ext.apply(
      {
        Authorization: 'Bearer ' + Wl.api.SSO.getAccessToken(),
        'Wl-Authorization': 'Bearer ' + Wl.api.SSO.getAccessToken(),
      },
      newOptions.headers
    )

    if (me.isValidOptions(newOptions)) {
      this.callParent([newOptions])
    } else {
      deferred.reject('Invalid options')
    }

    return deferred.promise
  },

  /** @private */
  onError: function (options, deferred, response) {
    const me = this
    if (!options.skipAuth && me.isAuthError(response)) {
      Wl.api.SSO.refreshAccessToken().then(
        function () {
          options.headers = Ext.apply(options.headers || {}, {
            Authorization: 'Bearer ' + Wl.api.SSO.getAccessToken(),
            'Wl-Authorization': 'Bearer ' + Wl.api.SSO.getAccessToken(),
          })
          const request = Ext.Ajax.request(options)
          request.then(
            (response) => {
              deferred.resolve(response)
            },
            (err) => {
              deferred.reject(err)
            }
          )
        },
        function (error) {
          Wl.api.SSO.logout()
        }
      )
    } else {
      if (options.callback) {
        Ext.callback(options.callback, options.scope, [options, false, response])
      }

      if (options.failure) {
        Ext.callback(options.failure, options.scope, [response])
      }

      deferred.reject(response)
    }
  },

  /** @private
   * Check if request failed because of invalid access token
   */
  isAuthError: function (response) {
    if (~response.request.url.indexOf(Wl.Env.getSSOUrl())) {
      return false
    }

    if (
      response.status === 401 &&
      (response.statusText == 'Unauthorized' ||
        (response.statusText != 'Forbidden' && response.statusText != 'invalid_grant'))
    ) {
      return true
    }

    return false
  },

  /** @private */
  isValidOptions: function (options) {
    return options.url && options.method
  },
})
