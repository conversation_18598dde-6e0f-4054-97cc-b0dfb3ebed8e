Ext.define('Wl.api.Proposals', {
  extend: 'Wl.api.Connection',
  singleton: true,

  getPackages: function () {
    return this.get('/proposal_packages').then((response) => {
      return Wl.optional('data', Ext.decode(response.responseText)) || []
    })
  },
  getProposals: function (id) {
    return this.get(`/proposal_packages/${id}/proposals`).then((response) => {
      return Wl.optional('data', Ext.decode(response.responseText)) || []
    })
  },
  getProposal: function (packageId, id) {
    return this.get(`/proposal_packages/${packageId}/proposals`)
      .then((response) => {
        return Wl.optional('data', Ext.decode(response.responseText)) || []
      })
      .then((proposals) => {
        const proposal = Ext.Array.findBy(proposals, function (proposal) {
          return proposal.id === id
        })
        if (!proposal) {
          return null
        }
        /* this field has bad syntax, but it is not needed for now anyway */
        delete proposal.productInfo['user.iban_info']
        return proposal
      })
  },
  acceptProposal: function (id) {
    return this.patch(`/proposals/${id}/accept`)
  },
  revokeProposal: function (id) {
    return this.patch(`/proposals/${id}/revoke`)
  },
})
