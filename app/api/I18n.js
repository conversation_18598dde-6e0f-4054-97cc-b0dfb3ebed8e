Ext.define('Wl.api.I18n', {
  extend: 'Ext.Base',
  singleton: true,

  requires: ['Wl.Env', 'Wl.util.Request'],

  get: function (language, callback) {
    const me = this
    const deffered = Ext.create('Ext.Deferred')

    Ext.Ajax.request({
      url: `${Wl.Env.getApiBaseUrl()}/language?params=%22language%22:%22${language}%22,%22collection%22:%22B2C_FE%22`,
      async: false,

      success: function (response) {
        const data = Ext.decode(response.responseText)
        callback(data.data || {})
      },
      failure: function (response) {
        callback({})
      },
    })

    return deffered.promise
  },
})
