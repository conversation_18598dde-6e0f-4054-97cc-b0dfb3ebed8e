describe('Connection', () => {
  beforeAll((cb) => {
    Ext.require(['Wl.api.Connection', 'Wl.util.Request'], () => {
      cb()
    })
  })

  beforeEach(function () {
    Wl.api.SSO.setAccessToken('fake-token', 1000)
    jasmine.Ajax.install()
  })

  afterEach(function () {
    jasmine.Ajax.uninstall()
  })

  it('should set the Authorization header', (cb) => {
    jasmine.expectCount(2)
    const connection = Ext.create('Wl.api.Connection')
    const options = {}

    jasmine.Ajax.stubRequest(`${Wl.Env.getApiBaseUrl()}/test`).andReturn({
      responseText: 'immediate response',
    })

    connection.get('/test', options).then((response) => {
      expect(response.request.headers.Authorization).toBe('Bearer fake-token')
      expect(response.request.headers['Wl-Authorization']).toBe('Bearer fake-token')
      cb()
    })
  })

  it('should refresh the access token if it is expired', () => {
    jasmine.expectCount(3)
    const spy = spyOn(Wl.api.SSO, 'refreshAccessToken').and.callThrough()

    jasmine.Ajax.stubRequest(`${Wl.Env.getApiBaseUrl()}/test`).andReturn({
      status: 401,
      responseText: 'Forbidden',
    })

    jasmine.Ajax.stubRequest(`${Wl.Env.getSSOUrl()}/oauth`).andReturn({
      status: 200,
      responseText: '{"access_token": "fake-new-token", "refresh_token": "fake-new-refresh-token"}',
    })

    return Wl.Request.get('/test', {}).then(
      (response) => {},
      function (err) {
        // there is no way to stub ajax once, so second run has also 401 but token should be changed anyway
        expect(spy).toHaveBeenCalled()

        expect(jasmine.Ajax.requests.mostRecent().url).toBe(`${Wl.Env.getApiBaseUrl()}/test`)

        expect(localStorage.getItem('accessToken')).toBe('fake-new-token')
      }
    )
  })
})
