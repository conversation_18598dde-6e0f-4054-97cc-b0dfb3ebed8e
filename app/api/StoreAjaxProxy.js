Ext.define('Wl.api.StoreAjaxProxy', {
  extend: 'Ext.data.proxy.Ajax',

  alias: 'proxy.wl-api',

  constructor: function (config) {
    config.reader = Ext.apply(
      {
        type: 'json',
        rootProperty: '_embedded',
        totalProperty: 'total_items',
      },
      config.reader || {}
    )

    if (config.rootProperty) {
      config.reader.rootProperty = config.rootProperty
    }

    this.callParent([config])
  },

  sendRequest: function (request) {
    request.setRawRequest(Wl.util.Request.request(request.getCurrentConfig()))
    this.lastRequest = request

    return request
  },
})
