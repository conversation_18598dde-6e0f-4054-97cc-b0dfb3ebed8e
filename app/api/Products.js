Ext.define('Wl.api.Products', {
  extend: 'Wl.api.Connection',

  singleton: true,

  getAll: function () {
    return this.get('/products/all').then((response) => {
      return (Wl.optional('data', Ext.decode(response.responseText)) || []).map((product) => {
        // Field mapping did not work in Model. It makes no difference to make it here.
        return {
          categoryId: product.c,
          name: product.n,
          providerId: product.p,
          id: product.i,
        }
      })
    })
  },
})
