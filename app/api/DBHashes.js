Ext.define('Wl.api.DBHashes', {
  extend: 'Wl.api.Connection',

  singleton: true,

  /**
   * @return {Promise}
   * @public
   * @method getAll
   * @memberOf Wl.api.DBHashes
   * @instance
   * @description
   * Get all hashes from the database.
   * @example
   * Wl.api.DBHashes.getAll().then(function (hashes) {
   * 	console.log(hashes);
   * });
   **/
  getAll: function () {
    return this.get('/hash').then((response) => {
      let data = Ext.decode(response.responseText)
      data = Wl.optional('_embedded.hash[0].json', data) || '{}'
      data = Ext.decode(data)
      return {
        categories: data.categories,
        products: data.products,
        providers: data.providers,
      }
    })
  },
})
