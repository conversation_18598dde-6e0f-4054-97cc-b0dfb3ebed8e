Ext.define('Wl.api.Agent', {
  extend: 'Wl.api.Connection',

  singleton: true,

  getById: function (id) {
    return this.get('/agents/' + id).then((response) => {
      return Ext.decode(response.responseText) || {}
    })
  },
  signAgreement: function (signature) {
    const clientProfile = Wl.api.SSO.getUserInfo()
    return this.post('/documents_base64', {
      jsonData: {
        image: signature,
        owner_id: clientProfile.id,
        owner_type: 'CLIENT',
        type: 'AUTHORISATION',
        name: 'Maklervollmacht',
      },
    })
  },
})
