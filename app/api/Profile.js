Ext.define('Wl.api.Profile', {
  extend: 'Wl.api.Connection',

  singleton: true,

  getUserInfo: function () {
    return this.get('/profile').then((response) => {
      return Wl.optional('_embedded.profile[0]', Ext.decode(response.responseText)) || {}
    })
  },

  setRisks: function (data) {
    return this.updateUserInfo(data)
  },

  changePassword: function (currentPassword, newPassword) {
    return this.post('/change-password', {
      jsonData: {
        currentPassword: currentPassword,
        newPassword: newPassword,
        repeatPassword: newPassword,
      },
    })
  },

  setProfession: function (data) {
    return this.updateUserInfo(data)
  },

  updateUserInfo: function (data) {
    return this.put('/profile', { jsonData: { profile: data } })
  },

  updateUserProfileImage: function (userId, file) {
    const me = this
    const fileReader = new FileReader()
    return new Ext.Promise(function (resolve, reject) {
      fileReader.onload = function () {
        me.post('/documents_base64', {
          jsonData: {
            owner_id: userId,
            owner_type: 'CLIENT',
            type: 'AVATAR',
            image: fileReader.result,
            name: file.name,
            file: {
              name: file.name,
              type: file.type,
            },
          },
        }).then((response) => {
          resolve(Ext.decode(response.responseText))
        }, reject)
      }
      fileReader.onerror = function (err) {
        reject(err)
      }
      fileReader.readAsDataURL(file)
    })
  },

  getClientDocuments: function () {
    return this.get('/documents?params="CLIENT"').then((response) => {
      return Wl.optional('data', Ext.decode(response.responseText)) || []
    })
  },

  getLegalAgreement: function () {
    return this.getClientDocuments().then((documents) => {
      return Ext.Array.findBy(documents, function (document) {
        return document.type === 'AUTHORISATION'
      })
    })
  },

  uploadPrivacyProtectionAgreement: function (userId, signature) {
    return this.post('/documents_base64', {
      jsonData: {
        owner_id: userId,
        owner_type: 'CLIENT',
        type: 'CLIENT_LEGAL_PROTECTION',
        image: signature,
        name: 'legal',
      },
    })
  },
})
