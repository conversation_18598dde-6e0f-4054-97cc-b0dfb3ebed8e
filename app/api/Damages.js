Ext.define('Wl.api.Damages', {
  extend: 'Wl.api.Connection',

  singleton: true,

  getAll: function (id) {
    return this.get('/damages/').then((response) => {
      return Wl.optional('data', Ext.decode(response.responseText) || {}) || []
    })
  },

  getById: function (contractId, id) {
    return this.get(`/contracts/${contractId}/damages/${id}`).then((response) => {
      return Ext.decode(response.responseText) || {}
    })
  },

  getDocuments: function (id) {
    return this.get(`/documents?params=\"DAMAGE\",${id}`).then((response) => {
      return Wl.optional('data', Ext.decode(response.responseText) || {}) || []
    })
  },

  updateDamage: function (contractId, damageId, data) {
    return this.put(`/contracts/${contractId}/damages/${damageId}`, {
      jsonData: data,
    })
  },

  createDamage: function (contractId, data) {
    return this.post(`/contracts/${contractId}/damages`, {
      jsonData: data,
    })
  },
})
