// There is no mixin or theme variables for paging toolbar, needs to be styled with plain CSS

.x-grid-paging-toolbar {

	& > .x-box-inner {
		overflow: visible;
	}
	.x-toolbar-text {
		font-weight: 400;
	}

	.x-toolbar-item {
		.x-form-field {

			min-height: 40px;
			max-height: 40px;
		}
	}

	.x-btn-primary-medium  {
		border-radius: $border-radius;
		box-shadow: $box-shadow;
		background-color: $white-color;

		padding: 8px;

		&.x-btn-disabled {
			background-color: $light-background-color;
		}

		.x-btn-icon-el {
			line-height: 22px;
		}
	}
}