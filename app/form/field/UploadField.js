/**
 * Mvpe.ux.fields.FileUpload
 *
 * This components provides an HTML5 file upload with
 * a preview image and a progress bar.
 */
Ext.define('Wl.form.field.UploadField', {
  extend: 'Ext.form.FieldContainer',
  alternateClassName: ['Ext.FileUpload', 'FileUpload'],
  alias: 'widget.html5fileupload',
  baseCls: 'wl-dropzone-field',

  selectedFiles: [],

  margin: 0,

  hideDropZone: false,

  asAttachments: false,

  /**
   * Request URL where the AJAX
   * request will be sended.
   *
   * @example requestURL: '{url controller="index" action="test"}'
   *
   * @string
   */
  requestURL: null,

  /**
   * Checks for the file reader api
   *
   * @boolean
   */
  supportsFileAPI: !!window.FileReader,

  /**
   * Renders a file input
   *
   * @boolean
   */
  showInput: true,

  /**
   * Checks the files amount
   *
   * @boolean
   */
  checkAmount: true,

  /**
   * Max file upload. Will be ignored if
   * checkAmount is set to "false"
   * Default: 3
   *
   * @integer
   */
  maxAmount: 50,

  /**
   * Callback function which will be called when the
   * user reaches the max amount limit.
   *
   * @function
   */
  maxAmountErrorFunction: Ext.emptyFn,

  /**
   * Checks the file types.
   *
   * @boolean
   */
  checkType: true,

  /**
   * Supported file extensions. Will be ignored if
   * checkType is set to "false"
   *
   * @array
   */
  // prettier-ignore
  validTypes: ['png', 'jpeg', 'jpg', 'jpe', 'pdf', 'doc', 'docx', 'txt', 'csv', 'xls', 'xlsx', 'msg', 'eml', 'odt', 'bmg', 'bmp', 'zip'],

  /**
   * Callback function which will be called when the
   * user uploads an unsupported file type
   *
   * @function
   */
  validTypeErrorFunction: Ext.emptyFn,

  /**
   * Checks file size
   *
   * @boolean
   */
  checkSize: true,

  /**
   * Max file size (in byte). Will be ignored if
   * checkSize is set to "false"
   * Default: 1048576 bytes (= 1 megabyte)
   *
   * @integer
   */
  maxSize: 1148576000,

  /**
   * Callback function which will be called when the
   * user uploads an file which exceed the max file size
   *
   * @function
   */
  maxSizeErrorFunction: Ext.emptyFn,

  afterUploadFunction: Ext.emptyFn,

  /**
   * Configuration object for the file input
   *
   * @object
   */
  fileInputConfig: {
    name: 'images[]',
    fieldLabel: '',
    buttonText: translate('GLOBAL.UPLOAD_FIELD.DROPZONE.TEXT'),
    ui: 'transparent',
    labelWidth: 0,
    buttonMargin: 0,
    margin: 0,
    allowBlank: true,
    width: '100%',
    buttonOnly: true,
  },

  inputConfig: {},

  /**
   * Rendering template for the drop zone
   *
   * @array
   */
  dropZoneTpl: [
    '<div class="inner-dropzone">',
    '<span class="text">',
    '<tpl if="actualQuantity">',
    '{actualQuantity} ' + 'from' + ' {totalQuantity}&nbsp;',
    '</tpl>',
    '{text}',
    '</span>',
    '</div>',
  ],

  /**
   * Default class name for the drop zone
   *
   * @string
   */
  dropZoneCls: '-dropzone',

  /**
   * Class name for the drop zone which will
   * be set when the user hovers over the
   * zone.
   *
   * @string
   */
  dropZoneOverCls: 'dropzone-over',

  /**
   * Class name for the drop zone which will
   * be set when the user drops an element
   * on the drop zone.
   *
   * @string
   */
  dropZoneDropCls: 'dropzone-drop',

  /**
   * Default text for the drop zone
   *
   * @string
   */
  dropZoneText: '',

  /**
   * Class name for the rendered drop item
   *
   * @string
   */
  dropItemCls: 'dropzone-item',

  /**
   * Class name for the rendered preview image container:
   */
  dropItemImageCls: 'dropzone-item-image',

  /**
   * Class name for the rendered drop item info box
   *
   * @string
   */
  dropItemInfoCls: 'dropzone-item-info',

  /**
   * Template for the information item
   *
   * @array
   */
  dropItemTpl: ['<div class="{infoCls}">', '<div class="file-name-area">{name:ellipsis(30)}</div>', '</div>'],

  /**
   * Clas name for the progress bar
   *
   * @string
   */
  progressBarCls: 'dropzone-item-progress-bar',

  /**
   * Template for the progress bar
   *
   * @array
   */
  progressBarTpl: ['<span style="width: {percent}%"></span>'],

  /**
   * Handles the hint text status
   *
   * @private
   * @boolean
   */
  initial: true,

  /**
   * Key for the uploaded file
   */
  fileField: 'fileId',

  /**
   * If set to true, the file upload component creates an preview image while uploading the files.
   * Default: false
   */
  enablePreviewImage: true,

  /**
   * CSS Class which indicates that a preview will be rendered.
   *
   * @string
   */
  previewEnabledCls: 'preview-image-displayed',

  /**
   * Indicates if the need to render a notice into the drop zone for
   * legacy browsers.
   *
   * @default false
   * @boolean
   */
  showLegacyBrowserNotice: false,

  /**
   * Truthy to hide the whole input field. Falsy to show it.
   */
  hideOnLegacy: false,

  /**
   * Snippet for this component.
   *
   * @object
   */
  snippets: {},
  /**
   * Initializes the component, binds the necessary event listeners and
   * creates the drop zone
   *
   * @return void
   */
  initComponent: function () {
    var me = this
    me.selectedFiles = []

    me.items = me.items || []

    // Check if the request url is set
    /* if (!me.requestURL) {
		  Ext.Error.raise(me.$className + ' needs the property "requestURL"')
		} */

    // Checking if the browser supports the File and FileReader API
    if (me.supportsFileAPI) {
      // Create the drop zone
      me.dropZone = me.createDropZone()
      me.previewContainer = Ext.create('Ext.container.Container', {
        cls: `preview-container ${me.asAttachments ? 'attachments-list' : ''}`,
        items: [],
      })
      me.items.push(me.previewContainer)
      me.items.push(me.dropZone)
    } else {
      // Force the browser to display the input field
      me.showInput = true
      //me.showLegacyBrowserNotice = true;
      me.enablePreviewImage = false

      if (me.hideOnLegacy) {
        me.showInput = false
        me.showLegacyBrowserNotice = false
        me.hidden = true
      }
    }

    // Create the file input field if necessary
    if (me.showInput) {
      if (me.showLegacyBrowserNotice) {
        me.fileInputConfig.supportText = me.snippets.legacyMessage
      }

      me.fileInput = me.createFileInputField()
      me.dropZone.add(me.fileInput)
    }

    /**
     * Add generic function to display an error if the
     * file type is not allowed in the configured valid file types array.
     */
    if (me.validTypeErrorFunction == Ext.emptyFn) {
      me.validTypeErrorFunction = me.validTypeErrorCallback
    }

    /**
     * Add generic function to display an error if the
     * file excesses the configured file size limit.
     */
    if (me.maxSizeErrorFunction == Ext.emptyFn) {
      me.maxSizeErrorFunction = me.maxSizeErrorCallback
    }
    me.callParent(arguments)
  },

  getValue: function () {
    return this.selectedFiles
  },

  setValue: function (value) {
    const me = this

    if (!Ext.isArray(value)) {
      value = [value]
    }

    me.selectedFiles = value

    me.previewContainer.removeAll()
    me.hideMe()

    me.iterateFiles(value)
  },

  /**
   * Creates the drop zone and all used events
   *
   * @return [object]
   */
  createDropZone: function () {
    var me = this,
      dropZone,
      text

    text = Ext.create('Ext.Component', {
      renderTpl: me.createDropZoneTemplate(),
      renderData: {
        text: me.dropZoneText,
      },
    })

    dropZone = Ext.create('Ext.container.Container', {
      focusable: false,
      hidden: me.hideDropZone,
      cls: me.baseCls + me.dropZoneCls,
      items: [text],
    })

    me.on('afterrender', me.createDropZoneEvents, me)

    return dropZone
  },

  /**
   * Callback method if the file type is not allowed.
   *
   * @private
   * @return vpid
   */
  validTypeErrorCallback: function () {
    var me = this

    Wl.toast({
      ui: 'error',
      translated: true,
      title: me.snippets.validTypeTitle,
      msg: me.snippets.validTypeText + ': ' + me.validTypes,
    })
  },

  /**
   * Callback method if the file is bigger than the max upload size.
   *
   * @private
   * @return vpid
   */
  maxSizeErrorCallback: function () {
    var me = this

    Wl.toast({
      ui: 'error',
      translated: true,
      title: me.snippets.maxUploadSizeTitle,
      msg: me.snippets.maxUploadSizeText,
    })

    // Create the drop zone
    // me.dropZone.removeAll()

    //create default drop zone
    var text = Ext.create('Ext.Component', {
      renderTpl: me.dropZoneTpl,
      tpl: me.dropZoneTpl,
      renderData: {
        text: me.dropZoneText,
      },
    })
    me.dropZone.add(text)
    me.fireEvent('uploadReady')
  },

  /**
   * Creates the template for the drop zone.
   *
   * @public
   * @return [object] Ext.XTemplate
   */
  createDropZoneTemplate: function () {
    var me = this

    return new Ext.XTemplate(
      '<div class="inner-dropzone">',
      '<span class="text">',
      '<tpl if="actualQuantity">',
      '{actualQuantity} ' + 'from' + ' {totalQuantity}&nbsp;',
      '</tpl>',
      '{text}',
      '</span>',
      '</div>'
    )
  },

  /**
   * Creates the necessary events for the drop zone
   *
   * @return void
   */
  createDropZoneEvents: function () {
    var me = this,
      el = me.dropZone.getEl().dom

    me.hideMe()

    el.addEventListener(
      'dragenter',
      function (event) {
        event.preventDefault()
        event.stopPropagation()
      },
      false
    )

    el.addEventListener(
      'dragover',
      function (event) {
        me.dropZone.getEl().addCls(me.dropZoneOverCls)
        event.preventDefault()
        event.stopPropagation()
      },
      false
    )

    el.addEventListener(
      'dragleave',
      function (event) {
        me.dropZone.getEl().removeCls(me.dropZoneOverCls)
        event.preventDefault()
        event.stopPropagation()
      },
      false
    )

    el.addEventListener(
      'drop',
      function (event) {
        var dropEl = me.dropZone.getEl(),
          files

        event.preventDefault()
        event.stopPropagation()

        // Handle the element classes
        if (dropEl.hasCls(me.dropZoneOverCls)) {
          dropEl.removeCls(me.dropZoneOverCls)
        }
        dropEl.addCls(me.dropZoneDropCls)

        if (event.dataTransfer && event.dataTransfer.files) {
          files = event.dataTransfer.files
        }
        me.iterateFiles(files)
      },
      false
    )
  },

  /**
   * Creates an multiple file input for the upload component
   *
   * @return [object] file
   */
  createFileInputField: function () {
    var me = this,
      file,
      el,
      ret

    var config = Ext.apply(
      {
        cls: this.baseCls + '-file-input',
      },
      me.inputConfig,
      me.fileInputConfig
    )
    config.name = me.fileField
    file = me.inputFileCmp = Ext.create('Ext.form.field.File', config)
    ret = file

    // Add "multiple" attribute to the file input field
    file.on(
      'afterrender',
      function () {
        el = file.fileInputEl.dom

        if (!me.showLegacyBrowserNotice) {
          if (me.maxAmount > 1) {
            el.setAttribute('multiple', 'multiple')
          }
          el.setAttribute('size', me.maxAmount)
        }
      },
      me
    )

    file.on(
      'change',
      function (field) {
        var fileField = field.getEl().down('input[type=file]').dom

        me.iterateFiles(fileField.files)
      },
      me
    )

    return ret
  },

  /**
   * Iterates through the file object
   *
   * @param [object] files
   */
  iterateFiles: function (files) {
    var me = this

    if (typeof files === 'undefined') {
      return false
    }

    // Check file amount
    if (me.checkAmount) {
      if (files && files.length > me.maxAmount) {
        if (me.maxAmountErrorFunction) {
          me.maxAmountErrorFunction.call()
        }

        return false
      }
    }
    for (var i = 0, l = files.length; i < l; i++) {
      me.createPreviewImage(files[i])
    }
  },

  /**
   * Creates a preview image using the FileReader API
   *
   * @param [object] file
   * @return void
   */
  createPreviewImage: function (file) {
    var reader = new FileReader(),
      img,
      me = this

    reader.onload = (function () {
      return function (event) {
        let format, progressBar, kbValue, info, infoPnl, actions

        let selectedFiles = me.selectedFiles
        let fileFound = false
        for (let index = 0; index < selectedFiles.length; index++) {
          const selectedFile = selectedFiles[index]

          if (
            selectedFile &&
            file &&
            selectedFile.lastModified === file.lastModified &&
            selectedFile.name === file.name &&
            selectedFile.size === file.size &&
            selectedFile.type === file.type
          ) {
            fileFound = true
            break
          }
        }

        if (!fileFound) {
          // Check file type and size
          if (me.checkType) {
            const split_name = file.name.split('.')
            const extension = split_name[split_name.length - 1]

            //format = file.type || extension
            format = extension
            if (!Ext.isEmpty(format)) {
              format = format.replace(/(.*\/)/i, '').toLowerCase()
            }

            if (!me.in_array(format, me.validTypes)) {
              if (me.validTypeErrorFunction) {
                me.validTypeErrorFunction.call(me, format)
              }
              return false
            }
          }

          // Check file size
          if (me.checkSize && me.maxSize < file.size) {
            if (me.maxSizeErrorFunction) {
              me.maxSizeErrorFunction.call(me, file.size)
            }
            return false
          }

          // Remove all other elements in the drop zone
          if (me.initial) {
            // me.dropZone.removeAll()
            me.initial = false
          }

          // Create the progress bar
          progressBar = Ext.create('Ext.Component', {
            cls: me.progressBarCls,
            tpl: me.progressBarTpl,
          })
          progressBar.update({ percent: 0 })
          progressBar.addCls(me.previewEnabledCls)

          // Divide by 1000 - See http://de.wikipedia.org/wiki/Byte#Vergleich
          kbValue = ~~(file.size / 1000)

          // Create information panel
          info = Ext.create('Ext.Component', {
            renderTpl: me.dropItemTpl,
            renderData: {
              infoCls: me.dropItemInfoCls,
              name: file.name,
              size: kbValue,
            },
            cls: 'file-info-content',
            items: [progressBar],
          })

          actions = Ext.create('Ext.button.Button', {
            // ui: 'transparent',
            tooltip: translate('GLOBAL.UPLOAD_FIELD.DELETE_BUTTON.TOOLTIP'),
            scale: 'small',
            cls: 'file-action-delete',
            iconCls: 'icon-trash',
            fileName: file.name,
            handler: function (cmp) {
              let filtered = me.selectedFiles.filter(function (value) {
                return value.name !== cmp.fileName
              })
              me.selectedFiles = filtered
              me.previewContainer.remove(cmp.up('panel'))
              me.hideMe()

              if (me.maxAmount === 1) {
                me.dropZone.show()
              }
            },
          })

          // Check if it's an image
          if (/image/i.test(file.type)) {
            // Create the preview image
            img = Ext.create('Ext.container.Container', {
              cls: me.dropItemImageCls,
              items: [
                {
                  xtype: 'image',
                  src: event.target.result,
                },
                info,
                actions,
              ],
            })
          } else {
            img = Ext.create('Ext.container.Container', {
              cls: me.dropItemImageCls,
              items: [
                {
                  xtype: 'component',
                  html: '<i class="icon icon-document"></i>',
                },
                info,
                actions,
              ],
            })
          }
          // Create information holder panel
          infoPnl = Ext.create('Ext.panel.Panel', {
            ui: 'plain',
            cls: me.dropItemCls,
            items: [img, progressBar],
          })
          me.previewContainer.add(infoPnl)

          // me.uploadFile(file, progressBar, infoPnl)
          me.selectedFiles.push(file)
          me.hideMe()

          if (me.maxAmount === 1) {
            me.dropZone.hide()
          }
        }

        me.inputFileCmp.fileInputEl.dom.value = ''
      }
    })(img)

    reader.readAsDataURL(file)
  },

  hideMe: function () {
    const me = this

    if (me.hideDropZone) {
      if (!Ext.isEmpty(me.selectedFiles)) {
        me.show()
      } else {
        me.hide()
      }
    }
  },

  /**
   * Uploads the passed media to the server
   *
   * @param [object] file - Content of the file to be uploaded
   * @param [object] progressBar - Ext.Component to display the upload progress
   * @param [object] infoText - Ext.panel.Panel which contains the info text '0 von 10 ...'
   * @param [int] count - Number of files to be uploaded.
   */
  uploadFile: function (file, progressBar, infoText, count) {
    var xhr = new XMLHttpRequest(),
      me = this

    progressBar = progressBar || 0
    infoText = infoText || null
    xhr.open('post', me.requestURL, false)

    // Upload complete
    xhr.addEventListener(
      'load',
      function (e) {
        let target = e.target,
          response = Ext.JSON.decode(target.responseText, true)

        if (response && response.success == false && response.blacklist == true) {
          Wl.toast({
            ui: 'error',
            title: me.snippets.blackListTitle,
            msg: Ext.String.format(me.snippets.blackListMessage, response.extension),
          })
        }

        // Check file size
        if (me.checkSize && me.maxSize < file.size) {
          if (me.maxSizeErrorFunction) {
            me.maxSizeErrorFunction.call(me, file.size)
          }
          return false
        }

        //increase progress bar value
        if (me.enablePreviewImage) {
          progressBar.update({ percent: 100 })
        } else {
          if (Ext.isNumeric(progressBar)) {
            progressBar++
          } else {
            progressBar.value++
            progressBar.update({ percent: (progressBar.value / count) * 100 })
          }

          if (infoText) {
            infoText.tpl = me.createDropZoneTemplate()
            infoText.renderTpl = me.createDropZoneTemplate()

            try {
              //update info text panel
              infoText.update({
                actualQuantity: progressBar.value,
                totalQuantity: count,
                text: me.snippets.uploadReady,
              })
            } catch (e) {
              //todo@dr: throw exception
            }
          }
        }

        if (target.readyState === 4 && target.status === 200) {
          try {
            me.fireEvent('fileUploaded', target)
          } catch (e) {
            //todo@dr: throw exception
          }
        }

        //last item? remove progress bar, display initial drop zone area and create a growl message
        if (infoText && progressBar.value === count) {
          // safari does not have a dropzone
          if (me.dropZone !== Ext.undefined) {
            // Create the drop zone
            // me.dropZone.removeAll()

            //create default drop zone
            var text = Ext.create('Ext.Component', {
              renderTpl: me.dropZoneTpl,
              tpl: me.dropZoneTpl,
              renderData: {
                text: me.dropZoneText,
              },
            })
            me.dropZone.add(text)
          }
          me.fireEvent('uploadReady', target)
          if (response.success) {
            //show info how much files uploaded
            Wl.toast({
              ui: 'success',
              title: me.snippets.messageTitle,
              msg: Ext.String.format(me.snippets.messageText, count),
            })
          } else {
            Wl.toast({
              ui: 'danger',
              translated: true,
              title: me.snippets.maxUploadSizeTitle,
              msg: me.snippets.maxUploadSizeText,
            })
          }
        } else {
          me.fireEvent('uploadReady', target)
        }
      },
      false
    )

    //send xml http request
    var formData = new FormData()
    formData.append(this.fileField, file)
    xhr.send(formData)
  },

  /**
   * Checks if a value exists in an array
   *
   * @example in_array('van', ['Kevin', 'van', 'Zonneveld']); - returns true
   *          in_array('vlado', { 0: 'Kevin', vlado: 'van', 1: 'Zonneveld' }); - returns false
   *          in_array(1, ['1', '2', '3']); - returns true
   *          in_array(1, ['1', '2', '3'], false); - returns true
   *          in_array(1, ['1', '2', '3'], true); - returns false
   *
   * @param [mixed] needle
   * @param [array] haystack
   * @param [boolean] argStrict
   * @return [boolean]
   */
  in_array: function (needle, haystack, argStrict) {
    var key = '',
      strict = !!argStrict

    if (strict) {
      for (key in haystack) {
        if (haystack[key] === needle) {
          return true
        }
      }
    } else {
      for (key in haystack) {
        if (haystack[key] == needle) {
          return true
        }
      }
    }
    return false
  },
})
