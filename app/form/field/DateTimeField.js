/*
 * File: DateTimeField.js
 *
 * This file requires use of the Ext JS library, under independent license.
 * This is part of the UX for DateTimeField developed by <PERSON>uilher<PERSON> Portela
 */
Ext.define('Wl.form.field.DateTimeField', {
  extend: 'Ext.form.field.Date',
  alias: 'widget.datetimefield',
  requires: ['Wl.form.field.DateTimePicker'],

  //<locale>
  /**
   * @cfg {String} format
   * The default date format string which can be overriden for localization support. The format must be valid
   * according to {@link Ext.Date#parse}.
   */
  format: 'd.m.Y H:i',
  submitFormat: Ext.Date.patterns.ISO8601Long,

  //</locale>
  /**
   * @cfg {String} altFormats
   * Multiple date formats separated by "|" to try when parsing a user input value and it does not match the defined
   * format.
   */
  altFormats: 'm/d/Y H:i:s|c',

  collapseIf: function (e) {
    var me = this,
      picker = me.picker

    if (picker.timePicker && !e.within(picker.timePicker.el, false, true)) {
      me.callParent([e])
    }
  },

  createPicker: function () {
    var me = this,
      parentPicker = this.callParent(),
      parentConfig = Ext.clone(parentPicker.initialConfig),
      initialConfig = Ext.clone(me.initialConfig),
      excludes = ['renderTo', 'width', 'height', 'bind', 'reference']

    // Avoiding duplicate ids error
    parentPicker.destroy()

    for (var i = 0; i < excludes.length; i++) {
      if (initialConfig.hasOwnProperty([excludes[i]])) {
        delete initialConfig[excludes[i]]
      }
    }

    return Ext.create('Wl.form.field.DateTimePicker', Ext.merge(initialConfig, parentConfig))
  },

  // We use view models across the application, so we need to override the getValue method because it's not used by view models
  getValue: function () {
    return this.getSubmitValue()
  },

  getErrors: function (value) {
    value = arguments && arguments.length > 0 ? value : this.formatDate(this.processRawValue(this.getRawValue()))

    var me = this,
      format = Ext.String.format,
      errors = me.superclass.superclass.getErrors.apply(this, arguments),
      disabledDays = me.disabledDays,
      disabledDatesRE = me.disabledDatesRE,
      minValue = me.minValue,
      maxValue = me.maxValue,
      len = disabledDays ? disabledDays.length : 0,
      i = 0,
      svalue,
      fvalue,
      day,
      time

    if (me.allowBlank && value == '') {
      return errors
    }

    if (value === null || (value && value.length < 1)) {
      // if it's blank and textfield didn't flag it then it's valid
      return errors
    }

    svalue = value
    value = me.parseDate(value)
    if (!value) {
      errors.push(format(me.invalidText, svalue, Ext.Date.unescapeFormat(me.format)))
      return errors
    }

    time = value.getTime()
    if (minValue && time < minValue.getTime()) {
      errors.push(format(me.minText, me.formatDate(minValue)))
    }

    if (maxValue && time > maxValue.getTime()) {
      errors.push(format(me.maxText, me.formatDate(maxValue)))
    }

    if (disabledDays) {
      day = value.getDay()

      for (; i < len; i++) {
        if (day === disabledDays[i]) {
          errors.push(me.disabledDaysText)
          break
        }
      }
    }

    fvalue = me.formatDate(value)
    if (disabledDatesRE && disabledDatesRE.test(fvalue)) {
      errors.push(format(me.disabledDatesText, fvalue))
    }

    return errors
  },

  getRefItems: function () {
    var me = this,
      result = me.callParent()

    if (me.picker && me.picker.timePicker) {
      result.push(me.picker.timePicker)
    }

    return result
  },

  onExpand: function () {
    var me = this,
      timePicker

    me.callParent()
    timePicker = me.picker && me.picker.timePicker

    if (timePicker) {
      me.picker.alignTimePicker()
    }
  },
})
