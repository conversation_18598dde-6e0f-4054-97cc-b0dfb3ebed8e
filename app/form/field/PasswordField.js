Ext.define('Wl.form.field.PasswordField', {
  extend: 'Ext.form.field.Text',
  xtype: 'wl-passwordfield',

  inputType: 'password',

  triggers: {
    visibility: {
      cls: 'icon-eye',
      handler: function (field) {
        var isShowPassword = field.inputEl.dom.type === 'password'
        field.inputEl.dom.type = isShowPassword ? 'text' : 'password'
        if (isShowPassword) {
          field.triggerEl.elements[0].removeCls('icon-eye').addCls('icon-eye-slash')
        } else {
          field.triggerEl.elements[0].removeCls('icon-eye-slash').addCls('icon-eye')
        }
      },
    },
  },
})
