.wl-dropzone-field {
  line-height: 16px;
  position: relative;

  .x-form-item-label {
    font-weight: 700;
  }

  .wl-dropzone-field-file-input {
    padding: 0 !important;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-weight: 700;
    font-size: 13px;

    .x-form-item-body {
      box-shadow: none;
    }

    .x-form-trigger-wrap {
      background-color: transparent;
    }

    .x-btn-inner {
      padding: 20px;
      font-style: normal;
      font-size: 13px;
      color: $base-color;
      background-color: transparent;
    }
  }

  .wl-dropzone-field-dropzone {
    position: relative;
    display: block;
    background: $light-background-color;
    border: 2px dashed $gray100-color;
    border-radius: 8px;
    height: 100px;
    line-height: 100px;
    text-align: center;
    flex: 1;
    width: 100%;

    &.dropzone-over {
      border-color: $gray200-color;
    }
  }

  .x-autocontainer-outerCt {
    width: 100%;
    position: relative;
    height: 100%;
  }
  .x-img {
    max-width: 30px;
  }

  .dropzone-item {
    margin-bottom: 20px;
    flex: 1;
    width: 100%;
  }

  .dropzone-item-info {
    margin-left: 12px;
    font-size: 13px;
    flex: 1;
  }

  .dropzone-item-image {
    .x-autocontainer-innerCt {
      display: flex;
      flex-direction: row;
    }
  }

  .dropzone-item {
    border: none;
    box-shadow: $box-shadow;
    padding: 12px 18px;
    border-radius: $border-radius;
    overflow: hidden;
    text-align: left;
    height: 45px;
    flex: none;

    .x-autocontainer-outerCt,
    .x-autocontainer-outerCt .x-autocontainer-innerCt,
    .dropzone-item-image {
      display: block;
    }
    .dropzone-item-image .x-autocontainer-innerCt {
      display: flex;
    }
    .dropzone-item-image .x-autocontainer-innerCt {
      align-items: center;
      img {
        height: 30px;
        object-fit: contain;
        object-position: center;
        width: 30px;
      }
    }

    .file-info-content {
      flex: 1;
    }

    .dropzone-item-info {
      margin: 0 6px;
    }

    .file-action-delete {
      background-color: transparent;
      .x-btn-icon-el {
        color: $dark-color;
      }
    }
  }
}
