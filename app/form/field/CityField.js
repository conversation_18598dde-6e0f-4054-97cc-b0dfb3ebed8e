Ext.define('Wl.form.field.CityField', {
  extend: 'Ext.form.field.ComboBox',
  xtype: 'wl-cityfield',

  cls: 'wl-cityfield',

  triggerCls: 'icon-location',

  queryDelay: 300,
  minChars: 1,

  forceSelection: true,

  queryMode: 'local',
  displayField: 'locationName',
  valueField: 'locationName',
  caseSensitive: false,

  config: {
    zipCode: null,
  },

  initComponent: function () {
    const me = this

    me.store = Ext.create('Ext.data.Store', {
      fields: [
        {
          name: 'locationName',
          type: 'string',
        },
        {
          name: 'locationNameAscii',
          type: 'string',
        },
        {
          name: 'zip',
          type: 'array',
        },
      ],
      autoLoad: true,
      proxy: {
        type: 'ajax',
        reader: {
          type: 'json',
          rootProperty: 'data',
          transform: this.transformStoreData,
        },
        url: Ext.getResourcePath('data/locations_de.json'),
      },
    })

    return me.callParent(arguments)
  },

  doLocalQuery: function (queryPlan) {
    var me = this,
      queryString = queryPlan.query,
      store = me.getStore(),
      value = queryString,
      filter

    me.changingFilters = true
    me.store.clearFilter(true)
    me.changingFilters = false

    // Querying by a string...
    if (queryString) {
      // User can be typing a regex in here, if it's invalid
      // just swallow the exception and move on
      if (me.enableRegEx) {
        try {
          value = new RegExp(value)
        } catch (e) {
          value = null
        }
      }

      if (value !== null && value.length > me.minChars) {
        // Must set changingFilters flag for this.checkValueOnChange.
        // the suppressEvents flag does not affect the filterchange event
        me.changingFilters = true

        if (me.zipCode) {
          me.store.filterBy(function (item) {
            return ~item.data.zip.indexOf(me.zipCode)
          })
        }

        let matcher = new RegExp(
          Ext.String.escapeRegex(value.replace(/ä/g, 'ae').replace(/ö/g, 'oe').replace(/ü/g, 'ue').replace(/ß/g, 'ss')),
          'i'
        )

        filter = me.queryFilter = new Ext.util.Filter({
          id: me.id + '-filter',
          anyMatch: me.anyMatch,
          caseSensitive: me.caseSensitive,
          root: 'data',
          property: me.displayField,
          filterFn: function (item) {
            return matcher.test(item.data.locationNameAscii)
          },
        })

        store.addFilter(filter, true)

        me.changingFilters = false
      }
    }

    // Expand after adjusting the filter if there are records (but less than 100) or if emptyText is configured.
    if ((me.store.getCount() && me.store.getCount() < 100) || me.getPicker().emptyText) {
      // The filter changing was done with events suppressed, so
      // refresh the picker DOM while hidden and it will layout on show.
      me.getPicker().refresh()
      me.expand()
    } else {
      me.collapse()
    }

    me.afterQuery(queryPlan)
  },

  onTriggerClick: function (comboBox, trigger, e) {
    const me = this
    // list tends to be huge, so we limit it to 100 items
    if (me.getCount < 100) {
      return this.callParent(arguments)
    }
  },

  setZipCode: function (zipCode) {
    const me = this
    const selection = me.getSelectedRecord()

    if (selection && !~selection.data.zip.indexOf(zipCode)) {
      me.clearValue()
      me.clearInvalid()
    }
    this.callParent(arguments)
  },

  transformStoreData: function (data) {
    let locationName = ''
    const set = (data.data || []).reduce((acc, item) => {
      locationName = `${item.c}-${item.d}`
      if (!acc[locationName]) {
        acc[locationName] = ['' + item.z]
      } else {
        acc[locationName].push('' + item.z)
      }

      return acc
    }, {})

    return Object.keys(set).reduce((acc, key) => {
      acc.push({
        locationName: key,
        locationNameAscii: key.replace(/ä/g, 'ae').replace(/ö/g, 'oe').replace(/ü/g, 'ue').replace(/ß/g, 'ss'),
        zip: set[key],
      })
      return acc
    }, [])
  },
})
