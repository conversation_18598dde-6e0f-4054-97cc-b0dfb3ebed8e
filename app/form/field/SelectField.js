Ext.define('Wl.field.form.SelectField', {
  extend: 'Ext.form.field.ComboBox',

  xtype: 'wl-selectfield',

  queryMode: 'local',
  forceSelection: true,
  valueField: 'value',
  displayField: 'name',
  editable: false,

  config: {
    options: null,
  },

  constructor: function (config) {
    var me = this

    if (config.options) {
      config.store = {
        fields: ['value', 'name'],
        data: config.options,
      }
    }

    me.callParent([config])
  },
})
