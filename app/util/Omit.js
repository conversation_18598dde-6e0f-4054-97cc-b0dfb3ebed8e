Ext.define(
  'Wl.util.Omit',
  {
    extend: 'Ext.Base',
    singleton: true,

    omit: function (object, keys) {
      const result = {}

      for (const key in object) {
        if (object.hasOwnProperty(key) && keys.indexOf(key) === -1) {
          result[key] = object[key]
        }
      }

      return result
    },
    omitBy: function (object, predicateFn) {
      const result = {}

      for (const key in object) {
        if (object.hasOwnProperty(key) && !predicateFn(object[key], key)) {
          result[key] = object[key]
        }
      }

      return result
    },
  },
  function (Omit) {
    Wl.omit = Omit.omit.bind(Omit)
    Wl.omitBy = Omit.omitBy.bind(Omit)
  }
)
