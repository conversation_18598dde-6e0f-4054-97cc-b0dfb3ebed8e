describe('Pick', function () {
  beforeAll((cb) => {
    Ext.require('Wl.util.Pick', () => {
      cb()
    })
  })

  it('should return undefined if object is null or undefined', function () {
    expect(Wl.util.Pick.pick(null, ['foo'])).toBeUndefined()
    expect(Wl.util.Pick.pick(undefined, ['foo'])).toBeUndefined()
  })

  it('should return undefined if none of the keys are found in the object', function () {
    const obj = { bar: 'baz' }
    expect(Wl.util.Pick.pick(obj, ['foo'])).toEqual({})
  })

  it('should return the value of the first key that is found in the object', function () {
    const obj = { foo: 'bar', baz: 'qux' }
    expect(Wl.util.Pick.pick(obj, ['foo', 'baz'])).toEqual({ foo: 'bar', baz: 'qux' })
  })

  it('should return the value of the first key that is not undefined', function () {
    const obj = { foo: undefined, bar: 'baz' }
    expect(Wl.util.Pick.pick(obj, ['foo', 'bar'])).toEqual({ foo: undefined, bar: 'baz' })
  })
})
