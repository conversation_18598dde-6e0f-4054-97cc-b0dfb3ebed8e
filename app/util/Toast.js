Ext.define(
  'Wl.window.Toast',
  {
    extend: 'Ext.window.Toast',

    alternativeClassName: ['Wl.Toast'],

    baseCls: 'wl-toast',

    align: 'tr',
    closable: true,
    header: false,
    bodyPadding: '10 16',
    layout: {
      type: 'hbox',
      align: 'center',
    },

    onRender: function () {
      const me = this
      this.callParent(arguments)

      me.el.on('click', me.onMouseClick, me)
    },

    onMouseClick: function () {
      this.close()
    },
  },
  function (Toast) {
    Wl.toast = function (config) {
      if (!Ext.isString(config.msg)) {
        return
      }

      let iconCls = 'wl-toast-icon isax '

      switch (config.ui) {
        case 'error':
          iconCls += 'isax-close-circle5'
          break
        case 'warning':
          iconCls += 'isax-danger5'
          break
        case 'success':
          iconCls += 'isax-tick-circle5'
          break
        default:
          iconCls += 'isax-info-circle5'
      }

      toast = new Toast(
        Ext.applyIf(config, {
          items: [
            {
              xtype: 'component',
              margin: '0 18 0 0',
              cls: iconCls,
            },
            {
              xtype: 'container',
              flex: 1,
              items: [
                {
                  xtype: 'component',
                  html: config.title,
                  margin: '0 0 5 0',
                },
                {
                  xtype: 'component',
                  html: config.msg,
                },
              ],
            },
          ],
        })
      )

      toast.show()

      return toast
    }
  }
)
