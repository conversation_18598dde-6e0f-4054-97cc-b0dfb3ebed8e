Ext.define(
  'Wl.util.OptionalChain',
  {
    extend: 'Ext.Base',
    singleton: true,

    partMatcher: /^([\w\d\-_]+)(\[(\d+)\])?$/i,

    optional: function (path, object) {
      if (!path) {
        return undefined
      }

      // Split path by dots, and start with the object
      const parts = path.split('.')
      let current = object

      // Iterate over parts
      for (let i = 0, part, path, index; i < parts.length; i++) {
        // Match the part with regex
        part = this.partMatcher.exec(parts[i])

        // Get the groups of the match
        path = part[1]
        index = part[3]

        if (current === undefined || current === null) {
          return undefined
        }

        // Set current to the current path
        current = current[path]

        // If the current path has an index, set current to the value at the index
        if (index !== undefined) {
          current = current[index]
        }
      }

      // Return the result
      return current
    },
  },
  function (OptionalChain) {
    Wl.optional = OptionalChain.optional.bind(OptionalChain)
  }
)
