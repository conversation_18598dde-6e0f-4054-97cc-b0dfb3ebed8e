describe('OptionalChain', () => {
  beforeAll((cb) => {
    Ext.require('Wl.util.OptionalChain', () => {
      cb()
    })
  })

  it('should return the value of the last property', () => {
    const obj = {
      foo: {
        bar: {
          baz: 'qux',
        },
      },
      fak: 123,
    }

    expect(Wl.optional('foo.bar.baz', obj)).toBe('qux')
    expect(Wl.optional('fak', obj)).toBe(123)
  })

  it('should return undefined if the path is not found', () => {
    const obj = {
      foo: {
        bar: {
          baz: 'qux',
        },
      },
    }

    expect(Wl.optional('foo.bar.baz.qux', obj)).toBe(undefined)
    expect(Wl.optional('fak.foo.bar.baz.qux', obj)).toBe(undefined)
  })

  it('should return array item by index if the path is found', () => {
    const obj = {
      foo: {
        bar: {
          baz: 'qux',
        },
        tux: ['a', 'b', 'c'],
      },
    }

    expect(Wl.optional('foo.tux[1]', obj)).toBe('b')
  })

  it('should return undefined if the array index is not found', () => {
    const obj = {
      foo: {
        bar: {
          baz: 'qux',
        },
        tux: ['a', 'b', 'c'],
      },
    }

    expect(Wl.optional('foo.tux[6]', obj)).toBe(undefined)
  })
})
