describe('LocalStorage', () => {
  beforeAll((cb) => {
    Ext.require('Wl.util.LocalStorage', () => {
      cb()
    })
  })

  it('should set and get a primitive value', () => {
    Wl.util.LocalStorage.set('foo', 'bar')

    expect(Wl.util.LocalStorage.get('foo')).toBe('bar')
  })

  it('should set and get an object', () => {
    Wl.util.LocalStorage.set('foo', { bar: 'baz' })

    expect(Wl.util.LocalStorage.get('foo')).toEqual({ bar: 'baz' })
  })
})
