Ext.define(
  'Wl.util.Pick',
  {
    extend: 'Ext.Base',
    singleton: true,

    pick: function (object, keys) {
      if (object === null || object === undefined) {
        return undefined
      }

      const result = {}

      for (let i = 0; i < keys.length; i++) {
        const key = keys[i]

        result[key] = object[key]
      }

      return result
    },
  },
  function (Pick) {
    Wl.pick = Pick.pick.bind(Pick)
  }
)
