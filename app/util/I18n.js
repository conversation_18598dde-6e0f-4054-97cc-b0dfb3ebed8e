window.missingTranslations = window.missingTranslations || {}

Ext.define('Wl.util.I18n', {
  extend: 'Ext.Base',
  singleton: true,
  requires: ['Wl.util.LocalStorage', 'Wl.api.I18n'],
  alternateClassName: ['I18n'],
  locale: null,
  values: {},

  setLocale: function (locale) {
    Wl.util.LocalStorage.set('language', locale)
  },

  getLocale: function () {
    return Wl.util.LocalStorage.get('language') || Wl.Env.getLanguage()
  },

  loadLocale: function () {
    const me = this
    let locale

    // Old app had set the language to 'de' without JSON parsing it. If we find it, we update its format.
    try {
      locale = me.getLocale()
    } catch (err) {
      locale = 'de'
      this.setLocale(locale)
    }

    return Wl.api.I18n.get(locale, function (response) {
      me.values = response
    })
  },

  get: function (key) {
    if (!key) {
      return ''
    }

    var translatedText = this.values[key]

    if (translatedText) {
      return translatedText
    } else {
      window.missingTranslations[key] = ''
      return key
    }
  },
})

window.translate = function (key) {
  return Wl.util.I18n.get(key)
}
