Ext.define(
  'Wl.util.State',
  {
    extend: 'Ext.Base',
    singleton: true,

    state: {},

    when: function (key) {
      if (!Ext.isDefined(this.state[key])) {
        this.state[key] = Ext.create('Ext.Deferred')
      }

      return this.state[key].promise
    },

    resolve: function (key, value) {
      if (!Ext.isDefined(this.state[key])) {
        this.state[key] = Ext.create('Ext.Deferred')
      }

      this.state[key].resolve(value)
    },

    reject: function (key, value) {
      if (!Ext.isDefined(this.state[key])) {
        this.state[key] = Ext.create('Ext.Deferred')
      }

      this.state[key].reject(value)
    },
  },
  function (State) {
    Wl.state = State
  }
)
