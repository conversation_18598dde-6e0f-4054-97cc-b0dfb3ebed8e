Ext.define('Wl.util.Globals', {
  extend: 'Ext.Base',
  singleton: true,
  alternateClassName: ['Globals'],

  requires: ['Ext.window.Toast'],

  invalidFormToast: function () {
    Wl.toast({
      msg: translate('TOAST.INVALID_FORM.MESSAGE'),
      ui: 'error',
    })
  },

  addMainSpinner: function () {
    Ext.create('Wl.view.loading.LoadingView', {
      renderTo: document.body,
      height: '100vh',
      width: '100vw',
      flex: 1,
      id: 'welcome-mobilversichert-spinner',
      cls: 'main-spinner',
      floating: true,
    })
  },

  destroyMainSpinner: function () {
    if (Ext.getCmp('welcome-mobilversichert-spinner')) {
      Ext.getCmp('welcome-mobilversichert-spinner').destroy()
    }
  },

  openDocumentIntoNewTab: function (documentId) {
    Globals.openNewTab(Globals.getDocument(documentId))
  },

  openNewTab: function (url) {
    if (!Globals.isString(url)) {
      throw Error('Parameter url must be string')
    }
    window.open(url)
  },

  getDocument: function (documentId, size) {
    if (typeof documentId === 'number' || Globals.isNotEmptyString(documentId)) {
      return `${Wl.Env.getApiBaseUrl()}/file?document_id=${documentId}&download=0&${
        Globals.isString(size) ? size : ''
      }&access_token=${Wl.api.SSO.getAccessToken()}`
    } else {
      return ''
    }
  },

  isString: function (a) {
    return typeof a === 'string' || a instanceof String
  },

  isNotEmptyString: function (a) {
    return a !== '' && Globals.isString(a) ? true : false
  },

  getChangedProperties: function (obj1, obj2) {
    Object.entries(obj2).reduce((changedProperties, [key, value]) => {
      if (!obj1.hasOwnProperty(key) || obj1[key] !== value) {
        changedProperties[key] = value
      }
      return changedProperties
    }, {})
  },

  getMainViewModel: function () {
    return Ext.getCmp('app-main').getViewModel()
  },
})
