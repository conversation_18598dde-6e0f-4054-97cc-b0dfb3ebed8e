@import 'variables';
@import 'Panel';
@import 'Button';
@import 'TabPanel';
@import 'Menu';
@import 'Combobox';
@import 'Window';
@import 'Paging';

@use 'sass:math';

@font-face {
  font-family: 'Mulish';
  src: url('fonts/Mulish-VariableFont_wght.ttf') format('truetype');
  font-weight: normal;
  font-style: normal;
}

.x-form-invalid-under-default {
  background-position-y: center !important;
}

.x-form-trigger-wrap {
  background-color: $form-field-background-color;
}

.x-form-checkbox-default:before {
  position: absolute;
  box-sizing: border-box;
  width: $form-checkbox-size;
  height: $form-checkbox-size;
  color: transparent;
  border-radius: 4px;
  border: 1px solid $blue-color;
  background-color: $panel-body-background-color;
}

.x-form-text-suffix {
  font:
    400 14px/1.5em 'Mulish',
    sans-serif;
  line-height: $form-field-height - 2px;
  padding-right: 30px;
}

.x-form-text-suffix::before {
  content: '';
}

.x-form-spinner-up {
  font-family: $font-icon-font-family;
  &::before {
    content: '\e940';
    // static value, looks the best but it is unrelated to any other value ;
    line-height: 35px;
  }
}

.x-form-spinner-down {
  font-family: $font-icon-font-family;
  &::before {
    content: '\ff90';
  }
}

.x-form-cb-checked .x-form-checkbox-default:before {
  color: transparent;
  background-color: $blue-color;
}

input[type='password']::-ms-reveal,
input[type='password']::-ms-clear {
  display: none;
  width: 0;
  height: 0;
}

.no-border {
  border: none !important;
}

.horizontal-line {
  height: 2px;
  width: 100%;
  background-color: $light-background-color;
}

.x-form-text-field-body {
  box-shadow: $box-shadow;
  border-radius: $border-radius;
}

.wl-title {
  font-size: $lead-font-size;
  font-weight: 700;
}

.wl-center {
  text-align: center;
  margin: 0 auto;
}

.wl-bold {
  font-weight: 700;
}

.wl-muted {
  color: $gray200-color;
}

.wl-text-secondary {
  color: $blue-color;
}

.wl-shadow {
  box-shadow: $box-shadow;
}

.wl-border-radius {
  border-radius: $border-radius;
}

.wl-iframe {
  width: 100%;
  height: 100%;
  border: none;
}

.main-spinner {
  background-color: white;
}
