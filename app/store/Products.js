Ext.define('Wl.store.Products', {
  extend: 'Wl.data.LocalHashedStore',
  storeId: 'products',

  model: 'Wl.model.Product',
  proxy: {
    type: 'memory',
    id: 'products',
  },

  apiFn: () => {
    return Wl.api.Products.getAll()
  },
  remoteHashToken: 'products',
  localHashToken: 'productsUpdatedAt',

  sorters: [
    {
      property: 'name',
      direction: 'ASC',
    },
  ],

  filterByProviderAndCategory: function (providerId, categoryId) {
    // If the category is "All" or "Other", we only care about the provider
    if (categoryId == '0' || categoryId == '99') {
      this.filterBy(function (product) {
        const curRemoteId = product.get('remote_id')
        const curProvider = product.get('providerId')

        // If the provider is "All", we don't need to filter
        if (curRemoteId == '0') return true

        // Otherwise, we filter by the provider
        return curProvider == providerId
      })
    } else {
      // Otherwise, we filter by the provider and the category
      this.filterBy(function (product) {
        const curRemoteId = product.get('remote_id')
        const curProvider = product.get('providerId')
        const curCategory = product.get('categoryId')

        if (curRemoteId == '0') return true

        // We filter by the provider and category
        return curProvider == providerId && curCategory.startsWith(categoryId)
      })
    }

    return this
  },
})
