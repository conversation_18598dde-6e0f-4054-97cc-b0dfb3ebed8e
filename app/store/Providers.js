Ext.define('Wl.store.Providers', {
  extend: 'Wl.data.LocalHashedStore',
  storeId: 'providers',

  model: 'Wl.model.Provider',
  proxy: {
    type: 'memory',
    id: 'providers',
  },

  apiFn: () => {
    return Wl.api.Providers.getAll()
  },
  remoteHashToken: 'providers',
  localHashToken: 'providersUpdatedAt',

  sorters: [
    {
      property: 'name',
      direction: 'ASC',
    },
  ],

  // Filter the providers by category. If the category is "All" or "Other", don't filter.
  filterByCategory: function (categoryId) {
    if (categoryId == '0' || categoryId == '99') return false
    this.filterBy(function (provider) {
      if (provider.get('remote_id') == '0') return true
      return Ext.Array.contains(provider.get('categories'), categoryId)
    })

    return this
  },
})
