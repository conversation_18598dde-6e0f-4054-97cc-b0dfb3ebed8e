Ext.define('Wl.store.Categories', {
  extend: 'Wl.data.LocalHashedStore',
  storeId: 'categories',

  model: 'Wl.model.Category',
  proxy: {
    type: 'memory',
    id: 'categories',
  },

  apiFn: () => {
    return Wl.api.Categories.getAll()
  },
  remoteHashToken: 'categories',
  localHashToken: 'categoriesUpdatedAt',

  sorters: [
    {
      property: 'name',
      direction: 'ASC',
    },
  ],
})
