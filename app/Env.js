// I did not find a simple way to override env variables from wordpress.
// So for now we stick with semi-legacy solution from MVPE.

Ext.define('Wl.Env', {
  extend: 'Ext.Base',
  singleton: true,
  requires: ['Wl.util.LocalStorage'],
  alternateClassName: ['Env'],

  oauthGrantType: 'password',
  oauthRefreshTokenGrantType: 'refresh_token',
  oauthScope: 'agent',

  getApiBaseUrl: function () {
    return globalConfig.apiBaseUrl
  },

  getSSOUrl: function () {
    return globalConfig.ssoUrl
  },

  getHomeUrl: function () {
    return globalConfig.homeUrl || '#welcome'
  },

  getLanguage: function () {
    return 'de'
  },

  getCustomerBenefitsUrl: function () {
    return globalConfig.customerBenefitsUrl
  },

  getTermsOfUseFileUrl: function () {
    return globalConfig.termsOfUseFileUrl
  },

  getImpressumFileUrl: function () {
    return globalConfig.impressumFileUrl
  },

  getDataProtectionFileUrl: function () {
    return window.mgis_data_protection_file_url || globalConfig.dataProtectionFileUrl
  },
})
