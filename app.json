{
  "name": "Wl",
  "namespace": "Wl",
  "version": "1.0.0.0",
  "framework": "ext",
  "toolkit": "classic",
  "theme": "Mobilversichert",
  "requires": ["font-awesome"],
  "bypass_lp": true,
  "indexHtmlPath": "index.html",
  "classpath": ["app"],
  "overrides": ["overrides"],
  "fashion": {
    "missingParameters": "error",
    "inliner": {
      /**
       * Disable resource inliner. Production builds enable this by default.
       */
      "enable": false
    }
  },
  /* "language": {
    "js": {
      "input": "ES8",
      "output": "ES5"
    }
  }, */
  "sass": {
    "namespace": "Wl",
    "generated": {
      "var": "sass/save.scss",
      "src": "sass/save"
    },
    "etc": ["sass/etc/all.scss"],
    "var": ["sass/var/all.scss", "sass/var"],
    "src": ["sass/src"]
  },
  "js": [
    {
      "path": "${framework.dir}/build/ext-all-rtl-debug.js"
    },
    {
      "path": "https://cdnjs.cloudflare.com/ajax/libs/signature_pad/4.1.5/signature_pad.umd.min.js",
      "remote": true
    },
    {
      "path": "https://cdnjs.cloudflare.com/ajax/libs/lz-string/1.5.0/lz-string.min.js",
      "remote": true
    },
    {
      "path": "app.js",
      "bundle": true
    }
  ],
  "css": [
    {
      "path": "${build.out.css.path}",
      "bundle": true,
      "exclude": ["fashion"]
    }
  ],
  "loader": {
    "cache": false,
    "cacheParam": "_dc"
  },
  "production": {
    "output": {
      "js": {
        "compressor": {
          "type": "yui",
          "java": "-Xmx1024m"
        }
      },
      "appCache": {
        "enable": false,
        "path": "cache.appcache"
      }
    },
    "loader": {
      "cache": "${build.timestamp}"
    },
    "cache": {
      "enable": true
    },
    "compressor": {
      "type": "yui"
    }
  },
  "testing": {
    "watch": {
      "delay": 250
    },
    "loader": {
      "cache": "${build.timestamp}"
    },
    "compressor": {
      "type": "yui"
    },
    "output": {
      "appCache": {
        "enable": false,
        "path": "cache.appcache"
      }
    },
    "cache": {
      "enable": true
    }
  },
  "development": {
    "watch": {
      "delay": 250
    }
    //"tags": ["fashion"]
  },
  "bootstrap": {
    "base": "${app.dir}",
    "microloader": "bootstrap.js",
    "css": "bootstrap.css"
  },
  "output": {
    "base": "${workspace.build.dir}/${build.environment}/${app.name}",
    "appCache": {
      "enable": false
    }
  },
  "cache": {
    "enable": false,
    "deltas": true
  },
  "appCache": {
    "cache": ["index.html"],
    "network": ["*"],
    "fallback": []
  },
  "resources": [
    {
      "path": "resources",
      "output": "shared"
    }
  ],
  "archivePath": "archive",
  "slicer": {
    "js": [
      {
        "path": "sass/example/custom.js",
        "isWidgetManifest": true
      }
    ],
    "output": {
      "appCache": {
        "enable": false
      }
    },
    "cache": {
      "enable": false
    }
  },
  "ignore": ["(^|/)CVS(/?$|/.*?$)"],
  "id": "5991668e-3763-45f4-ab84-a137eb36540c"
}
