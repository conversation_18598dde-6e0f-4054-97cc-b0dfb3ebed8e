# App variables
# -------------
NGINX_PORT=9040
NGINX_INNER_PORT=80
SSH_PATH=~/.ssh/id_rsa

BASE_URL="https://b2b-middleware-local.mobilversichert-dev.de/b2b/"
API_URL="https://b2b-middleware-local.mobilversichert-dev.de/"
CDN_URL="https://cdn-local.mobilversichert-dev.de/"
MVP_URL="https://mvp-local.mobilversichert-dev.de/"

OAUTH_CLIENT_ID=""
OAUTH_CLIENT_SECRET=""
OAUTH_GRANT_TYPE=""
OAUTH_REFRESH_TOKEN_GRANT_TYPE=""

VERSION="1"
BUILD="local"

SSH_KEY='-----BEGIN RSA PRIVATE KEY-----
Place
Your key
here
-----END RSA PRIVATE KEY-----'
