<?php
/*
Plugin Name: Mgis Kundenportal
Plugin URI: http://mobilversichert.de/wp-plugin
Description: Mgis Client App - specific page
Version: @@VERSION
Author: Mobilversichert
Author URI: http://mobilversichert.de/
Update Server: http://mobilversichert.de/wp-plugin/download/wp/
Min WP Version: 4
Max WP Version: 2.0.4
*/
function pages_with_shortcode($shortcode, $args = []) {
  if (!shortcode_exists($shortcode)) {
    // shortcode was not registered (yet?)
    return NULL;
  }

  // replace get_pages with get_posts
  // if you want to search in posts
  $pages = get_pages($args);
  $list = [];

  foreach ($pages as $page) {
    if (has_shortcode($page->post_content, $shortcode)) {
      $list[] = $page;
    }
  }

  $pages = [];

  foreach ($list as $p) {
    $pages[] = $p->ID;
  }

  return $pages;
}


function mgis_plugin_options() {
  if (!current_user_can('manage_options')) {
    wp_die(__('You do not have sufficient permissions to access this page.'));
  }

  // variables for the field and option names
  $opt_key_name = 'mgis_licence_key';
  $opt_token_name = 'mgis_token';
  $opt_endpoint_name = 'mgis_endpoint';
  $opt_sso_endpoint_name = 'mgis_sso_endpoint';
  $opt_calculator_url_name = 'mgis_calculator_endpoint';
  $opt_customer_benefit_url_name = 'mgis_customer_benefit_endpoint';
  $opt_primary_color_name = 'mgis_primary_color';
  $opt_headline_name = 'mgis_headline';
  $opt_mgis_nav_name = 'enable_mgis_navigation';
  $opt_mgis_chat_name = 'enable_mgis_chat';
  $opt_mgis_input_floating_label_name = 'enable_mgis_input_floating_label';
  $opt_mgis_input_undeline_name = 'enable_mgis_input_undeline';
  $opt_mgis_enable_promotion_code_name = 'enable_mgis_promotion_code';
  // link1
  $opt_link1_name = 'mgis_link1_name';
  $opt_link1_url = 'mgis_link1_url';
  $opt_link1_show = 'mgis_link1_show';
  // link2
  $opt_link2_name = 'mgis_link2_name';
  $opt_link2_url = 'mgis_link2_url';
  $opt_link2_show = 'mgis_link2_show';
  // link3
  $opt_link3_name = 'mgis_link3_name';
  $opt_link3_url = 'mgis_link3_url';
  $opt_link3_show = 'mgis_link3_show';
  // link4
  $opt_link4_name = 'mgis_link4_name';
  $opt_link4_url = 'mgis_link4_url';
  $opt_link4_show = 'mgis_link4_show';

  $hidden_field_name = 'mgis_licence_key_submit_hidden';
  $data_key_name = 'mgis_licence_key';
  $data_token_name = 'mgis_token';
  $data_endpoint_name = 'mgis_endpoint';
  $data_sso_endpoint_name = 'mgis_sso_endpoint';
  $data_calculator_name = 'mgis_calculator_endpoint';
  $data_customer_benefit_name = 'mgis_customer_benefit_endpoint';
  $data_primary_color_name = 'mgis_primary_color';
  $data_headline_name = 'mgis_headline';
  $data_mgis_nav_name = 'enable_mgis_navigation';
  $data_mgis_chat_name = 'enable_mgis_chat';
  $data_mgis_input_floating_label = 'enable_mgis_input_floating_label';
  $data_mgis_input_undeline = 'enable_mgis_input_undeline';
  $data_mgis_enable_promotion_code = 'enable_mgis_promotion_code';
  //link1
  $data_link1_name = 'mgis_link1_name';
  $data_link1_url = 'mgis_link1_url';
  $data_link1_show = 'mgis_link1_show';
  //link2
  $data_link2_name = 'mgis_link2_name';
  $data_link2_url = 'mgis_link2_url';
  $data_link2_show = 'mgis_link2_show';
  //link3
  $data_link3_name = 'mgis_link3_name';
  $data_link3_url = 'mgis_link3_url';
  $data_link3_show = 'mgis_link3_show';
  //link4
  $data_link4_name = 'mgis_link4_name';
  $data_link4_url = 'mgis_link4_url';
  $data_link4_show = 'mgis_link4_show';

  // Read in existing option value from database
  $opt_key_val = get_option($opt_key_name);
  $opt_token_val = get_option($opt_token_name);
  $opt_endpoint_val = get_option($opt_endpoint_name);
  $opt_sso_endpoint_val = get_option($opt_sso_endpoint_name);
  $opt_calculator_url_val = get_option($opt_calculator_url_name);
  $opt_customer_benefit_url_val = get_option($opt_customer_benefit_url_name);
  $opt_primary_color_val = get_option($opt_primary_color_name);
  $opt_headline_val = get_option($opt_headline_name);
  $opt_mgis_nav_val = get_option($opt_mgis_nav_name);
  $opt_mgis_chat_val = get_option($opt_mgis_chat_name);
  $opt_mgis_input_floating_label_val = get_option($opt_mgis_input_floating_label_name);
  $opt_mgis_input_undeline_val = get_option($opt_mgis_input_undeline_name);
  $opt_mgis_enable_promotion_code_val = get_option($opt_mgis_enable_promotion_code_name);
  //link1
  $opt_link1_name_val = get_option($opt_link1_name);
  $opt_link1_url_val = get_option($opt_link1_url);
  $opt_link1_show_val = get_option($opt_link1_show);
  //link2
  $opt_link2_name_val = get_option($opt_link2_name);
  $opt_link2_url_val = get_option($opt_link2_url);
  $opt_link2_show_val = get_option($opt_link2_show);
  //link3
  $opt_link3_name_val = get_option($opt_link3_name);
  $opt_link3_url_val = get_option($opt_link3_url);
  $opt_link3_show_val = get_option($opt_link3_show);
  //link4
  $opt_link4_name_val = get_option($opt_link4_name);
  $opt_link4_url_val = get_option($opt_link4_url);
  $opt_link4_show_val = get_option($opt_link4_show);

  // See if the user has posted us some information
  // If they did, this hidden field will be set to 'Y'
  if (isset($_POST[$hidden_field_name]) && $_POST[$hidden_field_name] == 'Y') {
    // Read their posted value
    $opt_key_val = $_POST[$data_key_name];
    $opt_token_val = $_POST[$data_token_name];
    $opt_endpoint_val = $_POST[$data_endpoint_name];
    $opt_sso_endpoint_val = $_POST[$data_sso_endpoint_name];
    $opt_calculator_url_val = $_POST[$data_calculator_name];
    $opt_customer_benefit_url_val = $_POST[$data_customer_benefit_name];
    $opt_primary_color_val = $_POST[$data_primary_color_name];
    $opt_headline_val = $_POST[$data_headline_name];
    $opt_mgis_chat_val = $_POST[$data_mgis_chat_name];
    $opt_mgis_input_floating_label_val = $_POST[$data_mgis_input_floating_label];
    $opt_mgis_input_undeline_val = $_POST[$data_mgis_input_undeline];
    $opt_mgis_enable_promotion_code_val = $_POST[$data_mgis_enable_promotion_code];
    // link1
    $opt_link1_name_val = $_POST[$data_link1_name];
    $opt_link1_url_val = $_POST[$data_link1_url];
    $opt_link1_show_val = $_POST[$data_link1_show];
    // link2
    $opt_link2_name_val = $_POST[$data_link2_name];
    $opt_link2_url_val = $_POST[$data_link2_url];
    $opt_link2_show_val = $_POST[$data_link2_show];
    // link3
    $opt_link3_name_val = $_POST[$data_link3_name];
    $opt_link3_url_val = $_POST[$data_link3_url];
    $opt_link3_show_val = $_POST[$data_link3_show];
    // link4
    $opt_link4_name_val = $_POST[$data_link4_name];
    $opt_link4_url_val = $_POST[$data_link4_url];
    $opt_link4_show_val = $_POST[$data_link4_show];

    if (array_key_exists($data_mgis_nav_name, $_POST)) {
      $opt_mgis_nav_val = TRUE;
    }
    else {
      $opt_mgis_nav_val = FALSE;
    }

    // Save the posted value in the database
    update_option($opt_key_name, $opt_key_val);
    update_option($opt_token_name, $opt_token_val);
    update_option($opt_endpoint_name, $opt_endpoint_val);
    update_option($opt_sso_endpoint_name, $opt_sso_endpoint_val);
    update_option($opt_calculator_url_name, $opt_calculator_url_val);
    update_option($opt_customer_benefit_url_name, $opt_customer_benefit_url_val);
    update_option($opt_primary_color_name, $opt_primary_color_val);
    update_option($opt_headline_name, $opt_headline_val);
    update_option($opt_mgis_nav_name, $opt_mgis_nav_val);
    update_option($opt_mgis_chat_name, $opt_mgis_chat_val);
    update_option($opt_mgis_input_floating_label_name, $opt_mgis_input_floating_label_val);
    update_option($opt_mgis_input_undeline_name, $opt_mgis_input_undeline_val);
    update_option($opt_mgis_enable_promotion_code_name, $opt_mgis_enable_promotion_code_val);
    // link1
    update_option($opt_link1_name, $opt_link1_name_val);
    update_option($opt_link1_url, $opt_link1_url_val);
    update_option($opt_link1_show, $opt_link1_show_val);
    // link2
    update_option($opt_link2_name, $opt_link2_name_val);
    update_option($opt_link2_url, $opt_link2_url_val);
    update_option($opt_link2_show, $opt_link2_show_val);
    // link3
    update_option($opt_link3_name, $opt_link3_name_val);
    update_option($opt_link3_url, $opt_link3_url_val);
    update_option($opt_link3_show, $opt_link3_show_val);
    // link4
    update_option($opt_link4_name, $opt_link4_name_val);
    update_option($opt_link4_url, $opt_link4_url_val);
    update_option($opt_link4_show, $opt_link4_show_val);

    // Put a "settings saved" message on the screen
    ?>
      <div class="updated"><p>
          <strong><?php _e('Daten wurden gespeichert.', 'mgis-kundenportal'); ?></strong>
      </p></div><?php
  }

  // Now display the settings editing screen

  echo '<div class="wrap">';

  // header

  echo "<h2>" . __('MGIS Plugin Einstellungen', 'mgis-kundenportal') . "</h2>";

  // settings form
  ?>

    <form name="form1" method="post" action="">
        <input type="hidden" name="<?php echo $hidden_field_name; ?>"
               value="Y">

        <p><?php _e("Lizenz Schlüssel:", 'mgis-kundenportal'); ?>
            <input type="text" name="<?php echo $data_key_name; ?>"
                   value="<?php echo $opt_key_val; ?>" size="20">
        </p>
        <hr/>

        <p><?php _e("Mgis Token:", 'mgis-kundenportal'); ?>
            <input type="text" name="<?php echo $data_token_name; ?>"
                   value="<?php echo $opt_token_val; ?>" size="20">
        </p>
        <hr/>

        <p><?php _e("Mgis Endpoint:", 'mgis-kundenportal'); ?>
            <input type="text" name="<?php echo $data_endpoint_name; ?>"
                   value="<?php echo $opt_endpoint_val; ?>" size="20">
        </p>
        <hr/>

        <p><?php _e("Mgis SSO Endpoint:", 'mgis-kundenportal'); ?>
            <input type="text" name="<?php echo $data_sso_endpoint_name; ?>"
                   value="<?php echo $opt_sso_endpoint_val; ?>" size="20">
        </p>
        <hr/>

        <p><?php _e("Mgis Calculator:", 'mgis-kundenportal'); ?>
            <input type="text" name="<?php echo $data_calculator_name; ?>"
                   value="<?php echo $opt_calculator_url_val; ?>" size="20">
        </p>
        <hr/>

        <p><?php _e("Mgis Customer Benefit:", 'mgis-kundenportal'); ?>
            <input type="text" name="<?php echo $data_customer_benefit_name; ?>"
                   value="<?php echo $opt_customer_benefit_url_val; ?>" size="20">
        </p>
        <hr/>

        <p><?php _e("Mgis Primary Color:", 'mgis-kundenportal'); ?>
            <input type="text" name="<?php echo $data_primary_color_name; ?>"
                   value="<?php echo $opt_primary_color_val; ?>" size="20">
        </p>
        <hr/>

        <p><?php _e("Mgis Headline Portal:", 'mgis-kundenportal'); ?>
            <input type="text" name="<?php echo $data_headline_name; ?>"
                   value="<?php echo $opt_headline_val; ?>" size="20">
        </p>
        <hr/>

        <p><?php _e("Mgis Navigation anzeigen:", 'mgis-kundenportal'); ?>
            <input type="checkbox" name="<?php echo $data_mgis_nav_name; ?>"
                   value="1" <?php echo $opt_mgis_nav_val == TRUE ? "checked" : "" ?>>
        </p>
        <hr/>
        <p><?php _e("Mgis Chat anzeigen:", 'mgis-kundenportal'); ?>
            <input type="checkbox" name="<?php echo $data_mgis_chat_name; ?>"
                   value="1" <?php echo $opt_mgis_chat_val == TRUE ? "checked" : "" ?>>
        </p>
        <hr/>
        <p><?php _e("Input Underline anzeigen:", 'mgis-kundenportal'); ?>
            <input type="checkbox"
                   name="<?php echo $data_mgis_input_undeline; ?>"
                   value="1" <?php echo $opt_mgis_input_undeline_val == TRUE ? "checked" : "" ?>>
        </p>
        <hr/>
        <p><?php _e("Input Label Floating:", 'mgis-kundenportal'); ?>
            <input type="checkbox"
                   name="<?php echo $data_mgis_input_floating_label; ?>"
                   value="1" <?php echo $opt_mgis_input_floating_label_val == TRUE ? "checked" : "" ?>>
        </p>
        <hr/>
        <p><?php _e("Promotion-Code:", 'mgis-kundenportal'); ?>
            <input type="checkbox"
                   name="<?php echo $data_mgis_enable_promotion_code; ?>"
                   value="1" <?php echo $opt_mgis_enable_promotion_code_val == TRUE ? "checked" : "" ?>>
        </p>
        <hr/>

        <p><?php _e("Link1 name:", 'mgis-kundenportal'); ?>
            <input type="text" name="<?php echo $data_link1_name; ?>"
                   value="<?php echo $opt_link1_name_val; ?>" size="20">
        </p>
        <p><?php _e("Link1 url:", 'mgis-kundenportal'); ?>
            <input type="text" name="<?php echo $data_link1_url; ?>"
                   value="<?php echo $opt_link1_url_val; ?>" size="20">
        </p>
        <p><?php _e("Link1 show:", 'mgis-kundenportal'); ?>
            <input type="checkbox" name="<?php echo $data_link1_show; ?>"
                   value="1" <?php echo $opt_link1_show_val == TRUE ? "checked" : "" ?>>
        </p>
        <hr/>

        <p><?php _e("Link2 name:", 'mgis-kundenportal'); ?>
            <input type="text" name="<?php echo $data_link2_name; ?>"
                   value="<?php echo $opt_link2_name_val; ?>" size="20">
        </p>
        <p><?php _e("Link2 url:", 'mgis-kundenportal'); ?>
            <input type="text" name="<?php echo $data_link2_url; ?>"
                   value="<?php echo $opt_link2_url_val; ?>" size="20">
        </p>
        <p><?php _e("Link2 show:", 'mgis-kundenportal'); ?>
            <input type="checkbox" name="<?php echo $data_link2_show; ?>"
                   value="1" <?php echo $opt_link2_show_val == TRUE ? "checked" : "" ?>>
        </p>
        <hr/>

        <p><?php _e("Link3 name:", 'mgis-kundenportal'); ?>
            <input type="text" name="<?php echo $data_link3_name; ?>"
                   value="<?php echo $opt_link3_name_val; ?>" size="20">
        </p>
        <p><?php _e("Link3 url:", 'mgis-kundenportal'); ?>
            <input type="text" name="<?php echo $data_link3_url; ?>"
                   value="<?php echo $opt_link3_url_val; ?>" size="20">
        </p>
        <p><?php _e("Link3 show:", 'mgis-kundenportal'); ?>
            <input type="checkbox" name="<?php echo $data_link3_show; ?>"
                   value="1" <?php echo $opt_link3_show_val == TRUE ? "checked" : "" ?>>
        </p>
        <hr/>

        <p><?php _e("Link4 name:", 'mgis-kundenportal'); ?>
            <input type="text" name="<?php echo $data_link4_name; ?>"
                   value="<?php echo $opt_link4_name_val; ?>" size="20">
        </p>
        <p><?php _e("Link4 url:", 'mgis-kundenportal'); ?>
            <input type="text" name="<?php echo $data_link4_url; ?>"
                   value="<?php echo $opt_link4_url_val; ?>" size="20">
        </p>
        <p><?php _e("Link4 show:", 'mgis-kundenportal'); ?>
            <input type="checkbox" name="<?php echo $data_link4_show; ?>"
                   value="1" <?php echo $opt_link4_show_val == TRUE ? "checked" : "" ?>>
        </p>
        <hr/>

        <p class="submit">
            <input type="submit" name="Submit" class="button-primary"
                   value="<?php esc_attr_e('Speichern') ?>"/>
        </p>

    </form>

    <div>
        <p> Um das Mgis Kundenportal auf einer Ihrer Seite einzubinden,
            verdenden Sie folgenden short_code "[mgis-client-app]".

        </p>
        <p>
            Folgende Url Struktur haben wir in unserem Kundenportal<br/>
        <ul>
            <li>#/ - home</li>
            <li>#/sign-in Login</li>
            <li>#/register - Registrierung</li>
            <li>#/contracts - Verträge</li>
            <li>#/contracts/[0-9] - Vertrag</li>
            <li>#/contracts/[0-9]/documents - Dokumente zu einem Vertrag</li>
            <li>#/contracts/[0-9]/documents/[0-9] - ein Dokument</li>
            <li>#/contracts/[0-9]/edit - Vertrag ändern</li>
            <li>#/contracts/[0-9]/damage - Schadensbericht</li>
            <li>#/create-contract - Vertrag anlegen</li>
            <li>#/expert - Experte (Makler)</li>
            <li>#/settings - Einstellungen</li>
            <li>#/settings/language - Sprache ändern</li>
            <li>#/settings/change-password - Passwort ändern</li>
            <li>#/user-profile - Benutzerprofil</li>
            <li>#/logout - Abmelden</li>
        </ul>
        </p>
    </div>

  <?php

}

class ClientApp {
  static function mgis_configuration_menu() {
    add_menu_page('MGIS Kundenportal', 'MGIS Kundenportal', 'manage_options', 'mgis', 'mgis_plugin_options', plugin_dir_url(__FILE__) . '/images/icon.png', 20);
    add_options_page('MGIS Kundenportal', 'MGIS Kundenportal', 'manage_options', 'my-unique-identifier', 'mgis_plugin_options');
  }


  static function mgis_container() {
    return '<div class="container">
          <div id="app">Loading...</div>
        </div>
        ';
  }

  static function mgis_set_head() {
    $baseUrl = plugin_dir_url(__FILE__) . 'public/';

    ?>
      <base href='<?php echo $baseUrl ?>' />
      <script>
          window.Wl = {};
          window.mgis_api_route = '<?php echo get_rest_url(); ?>mgis/v1/md';
          window.mgis_sso_route = '<?php echo get_rest_url(); ?>mgis/v1/sso';
          window.mgis_home_url = '<?php echo get_page_link(); ?>#welcome';
      </script>
    <?php
  }

  static function register_main_scripts() {
    wp_enqueue_script('bundle', plugin_dir_url(__FILE__) . 'public/bootstrap.js', [], '@@VERSION', TRUE);
  }

  static function mgis_call_sso($attr = [], $content = NULL) {
    $method = $attr->get_method();
    $url_params = $attr->get_url_params();
    $body_params = $attr->get_body_params();
    $query_params = $attr->get_query_params();
    $body = $attr->get_body();
    $json = $attr->get_json_params();
    $authorization_header = $attr->get_header('Wl-Authorization');

    $endpoint = get_option('mgis_sso_endpoint') . $url_params['path'];

    $headers = [
      "accept" => "*/*",
      "accept-encoding" => "gzip, deflate, br",
      "accept-language" => "de-DE,de;q=0.8,en-US;q=0.6,en;q=0.4,fr;q=0.2,sv;q=0.2,it;q=0.2",
      "cache-control" => " no-cache",
      "connection" => " keep-alive",
      "content-type" => " ".$attr->get_header('content-type'),
    ];

    if ($authorization_header) {
      $headers['authorization'] = $authorization_header;
    }

    $curl = curl_init();
    curl_setopt_array($curl, [
      CURLOPT_URL            => $endpoint,
      CURLOPT_RETURNTRANSFER => TRUE,
      CURLOPT_ENCODING       => "",
      CURLOPT_MAXREDIRS      => 10,
      CURLOPT_TIMEOUT        => 30,
      CURLOPT_HEADER         => 0,
      CURLOPT_HTTP_VERSION   => CURL_HTTP_VERSION_1_1,
      CURLOPT_CUSTOMREQUEST  => $method,
      CURLOPT_POSTFIELDS     => ($json ?: $body),
      CURLOPT_HTTPHEADER     => $headers,
    ]);

    $response = curl_exec($curl);
    $err = curl_error($curl);
    $header_data = curl_getinfo($curl);
    $http_status = curl_getinfo($curl, CURLINFO_HTTP_CODE);

    curl_close($curl);
    http_response_code($http_status);
    echo $response;
    exit();
  }


  static function mgis_call_api($attr = [], $content = NULL) {
    $method = $attr->get_method();
    $url_params = $attr->get_url_params();
    $body_params = $attr->get_body_params();
    $query_params = $attr->get_query_params();
    $body = $attr->get_body();
    $json = $attr->get_json_params();
    $authorization_header = $attr->get_header('Wl-Authorization');

    $endpoint = get_option('mgis_endpoint') . $url_params['path'] . "?" . http_build_query($query_params);

    $headers = [
      "accept: ".$attr->get_header('accept'),
      "accept-encoding: gzip, deflate, br",
      "accept-language: de-DE,de;q=0.8,en-US;q=0.6,en;q=0.4,fr;q=0.2,sv;q=0.2,it;q=0.2",
      "cache-control: no-cache",
      "connection: keep-alive",
      "content-type: ".$attr->get_header('content-type'),

      "user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_12_6) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/60.0.3112.113 Safari/537.36",
      "x-requested-with: XMLHttpRequest",
    ];

    if ($authorization_header) {
      array_push($headers, "authorization: ".$authorization_header);
    }

    $curl = curl_init();
    curl_setopt_array($curl, [
      CURLOPT_URL            => $endpoint,
      CURLOPT_RETURNTRANSFER => TRUE,
      CURLOPT_ENCODING       => "",
      CURLOPT_MAXREDIRS      => 10,
      CURLOPT_TIMEOUT        => 30,
      CURLOPT_HEADER         => 0,
      CURLOPT_HTTP_VERSION   => CURL_HTTP_VERSION_1_1,
      CURLOPT_CUSTOMREQUEST  => $method,
      CURLOPT_POSTFIELDS     => $body,
      CURLOPT_HTTPHEADER     => $headers,
    ]);

    $response = curl_exec($curl);
    $err = curl_error($curl);
    $header_data = curl_getinfo($curl);

    $http_status = curl_getinfo($curl, CURLINFO_HTTP_CODE);

    curl_close($curl);
    http_response_code($http_status);
    header('Content-Type: '.$header_data['content_type']);

    echo $response;
    exit();
  }

  static function mgis_register_routes() {
    register_rest_route('mgis/v1/md', '(?<path>[^\s?]+)', [
      'methods'  => ['GET', 'POST', 'PUT', 'DELETE', 'PATCH'],
      'callback' => ['ClientApp', 'mgis_call_api'],
      'permission_callback' => '__return_true'
    ]);

    register_rest_route('mgis/v1/sso', '(?<path>[^\s?]+)', [
      'methods'  => ['GET', 'POST', 'PUT', 'DELETE', 'PATCH'],
      'callback' => ['ClientApp', 'mgis_call_sso'],
      'permission_callback' => '__return_true'
    ]);
  }
}

function resetEnqueues() {
  global $wp_styles;
  global $wp_scripts;
  $wp_styles = new WP_Styles();
  $wp_scripts = new WP_Scripts();
  do_action('wp_enqueue_scripts_clean');
}

function stripEnqueues() {
  remove_all_actions('wp_enqueue_scripts');
  add_action('wp_enqueue_scripts', 'resetEnqueues', 999999);
}


function mgis_wp_template() {
  //  remove_action( 'wp_footer', 'us_pass_header_settings_to_js');
  if (is_page(pages_with_shortcode('mgis-client-app'))) {

    add_filter('show_admin_bar', '__return_false');

    // Reset wp_footer
    remove_all_actions('wp_footer');
    add_action('wp_footer', 'wp_print_footer_scripts', 20);

    // Remove ALL actions to strip 3rd party plugins and unwanted WP functions
    remove_all_actions('wp_head');
    remove_all_actions('wp_print_styles');
    remove_all_actions('wp_print_head_scripts');

    // Add back WP native actions that we need
    add_action('wp_head', 'wp_enqueue_scripts', 1);
    add_action('wp_head', 'wp_print_styles', 8);
    add_action('wp_head', 'wp_print_head_scripts', 9);
    add_action('wp_head', '_wp_render_title_tag', 1);

    // Strip all scripts and styles
    add_action('wp_head', 'stripEnqueues', -1);

    // Add main scripts
    add_action('wp_enqueue_scripts_clean', [
      'ClientApp',
      'register_main_scripts',
    ]);

    // Add mgis_ajax_endpoint to the window object as inline javascript
    add_action('wp_head', ['ClientApp', 'mgis_set_head'], 101);
  }
}

add_action('template_redirect', 'mgis_wp_template', 99999);

add_shortcode('mgis-client-app', ['ClientApp', 'mgis_container']);
add_action('wp_ajax_call_api', ['ClientApp', 'call_api']);
add_action('wp_ajax_nopriv_call_api', ['ClientApp', 'call_api']);
add_action('admin_menu', ['ClientApp', 'mgis_configuration_menu']);

add_action('rest_api_init', ['ClientApp', 'mgis_register_routes']);
?>
