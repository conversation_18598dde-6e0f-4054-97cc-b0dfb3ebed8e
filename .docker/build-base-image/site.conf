server {
	listen 80 default_server;
	listen [::]:80 default_server;


	root /code;

	# Add index.php to the list if you are using PHP
	index index.html index.htm index.nginx-debian.html;

	server_name _;

	location / {
		# First attempt to serve request as file, then
		# as directory, then fall back to displaying a 404.
		try_files $uri $uri/ =404;
	}

	location /nginx-health {
      return 200 "healthy\n";
    }

}
