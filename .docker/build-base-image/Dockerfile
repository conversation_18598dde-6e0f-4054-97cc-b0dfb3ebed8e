FROM openjdk:8-jre-slim

ENV <PERSON>NCHA /opt/Sencha/Cmd/repo/extract/ext/*******

RUN apt-get update && apt-get install -y --no-install-recommends \
        curl \
        unzip \
		libfreetype6 \
		fontconfig \
	&& rm -rf /var/lib/apt/lists/*

# Installing SenchaCmd
RUN curl --silent https://trials.sencha.com/cmd/7.5.1/no-jre/SenchaCmd-********-linux-amd64.sh.zip -o /tmp/sencha.zip && \
    unzip /tmp/sencha.zip -d /tmp  && \
    unlink /tmp/sencha.zip  && \
    chmod o+x /tmp/SenchaCmd-********-linux-amd64.sh && \
    /tmp/SenchaCmd-********-linux-amd64.sh -q -dir /opt/Sencha/Cmd/******** && \
    unlink /tmp/SenchaCmd-********-linux-amd64.sh

RUN mkdir -p /opt/Sencha/Cmd/repo/extract/ext/********

COPY .docker/build-base-image/ext/ext-7.5.1 /opt/Sencha/Cmd/repo/extract/ext/*******

ENV PATH="/opt/Sencha/Cmd:${PATH}"

ENTRYPOINT ["tail", "-f", "/dev/null"]
# ##################################################################################################################################

