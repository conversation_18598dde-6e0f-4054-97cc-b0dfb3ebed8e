##################################################################################################################################
FROM mgiscommonacr.azurecr.io/images/mvpe-sencha as sencha-build

WORKDIR /code
COPY . /code

ENV PATH="/opt/Sencha/Cmd:${PATH}"

RUN /opt/Sencha/Cmd/sencha app upgrade --full $SENCHA

#ENTRYPOINT ["tail", "-f", "/dev/null"]

RUN OPENSSL_CONF=/dev/null /opt/Sencha/Cmd/sencha app build --destination /code/build/production production

# ##################################################################################################################################

FROM nginx:1.21

RUN set -x \
    && apt-get update -qy \
    && apt-get install --no-install-recommends -qfy gettext-base \
    && apt-get clean


COPY --from=sencha-build \
    /code/build/production \
    /code

WORKDIR /code


COPY ./resources/js/global.config.js.tmpl /code/resources/js/global.config.js.tmpl
COPY ./.docker/production/site.conf /etc/nginx/conf.d/default.conf
COPY ./.docker/production/nginx.conf /etc/nginx/nginx.conf
COPY ./.docker/production/docker-entrypoint /usr/local/bin/docker-entrypoint

RUN chmod -R +x /usr/local/bin/docker-entrypoint

STOPSIGNAL SIGQUIT

CMD ["/usr/local/bin/docker-entrypoint/entrypoint.sh"]

