##################################################################################################################################
FROM mgisinfrastructureacr1.azurecr.io/production/images/client-whitelabel-sencha as sencha-build

WORKDIR /code
COPY . /code
COPY ./.docker/local/sencha.vmoptions /opt/Sencha/Cmd/7.5.1.20/sencha.vmoptions

ENV PATH="/opt/Sencha/Cmd:${PATH}"

RUN sencha app upgrade --full $SENCHA

#ENTRYPOINT ["tail", "-f", "/dev/null"]
# ##################################################################################################################################

FROM nginx:1.21

ENV SENCHA /opt/Sencha/Cmd/repo/extract/ext/7.5.1.20
ENV PATH="/opt/Sencha/Cmd:${PATH}"

RUN set -x \
    && apt-get update -qy \
    && apt-get install --no-install-recommends -qfy \
    gettext-base \
    openjdk-11-jre


COPY --from=sencha-build \
      $SENCHA \
      $SENCHA

COPY --from=sencha-build \
      /opt/Sencha/Cmd \
      /opt/Sencha/Cmd


COPY --from=sencha-build \
    /code \
    /code

WORKDIR /code

COPY ./.docker/local/site.conf /etc/nginx/conf.d/default.conf

COPY ./.docker/local/docker-entrypoint /usr/local/bin/docker-entrypoint
RUN chmod -R +x /usr/local/bin/docker-entrypoint

ENTRYPOINT ["/usr/local/bin/docker-entrypoint/entrypoint.sh"]
