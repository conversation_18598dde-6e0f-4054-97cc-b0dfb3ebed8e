version: '3'
services:
  client-whitelabel:
    env_file:
      - ../../.env.local
    build:
      context: ../../
      dockerfile: ./.docker/local/Dockerfile
      args:
        SSH_KEY: $SSH_KEY
    container_name: client-whitelabel
    ports:
      - ${NGINX_PORT}:80
    restart: unless-stopped
    volumes:
      - ../../:/code:cached
      - /ext/
  #     - ./ext-6.2.1/:/home/<USER>/ext-6.2.1:delegated
