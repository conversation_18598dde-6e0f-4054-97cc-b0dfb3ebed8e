services:
  app:
    build:
      context: ../../
      dockerfile: .docker/local/Dockerfile
    container_name: client-mvp-dev
    ports:
      - '3000:3000'
    env_file:
      - ../../.env.local
    environment:
      # Development configuration
      - NODE_ENV=development
      - NEXT_TELEMETRY_DISABLED=1
      - HOSTNAME=0.0.0.0
      - PORT=3000
      # Public API endpoints
      - SSO_URL=https://sso.mobilversichert-dev.de
      - API_BASE_URL=https://b2c-middleware.mobilversichert-dev.de
      - API_BASE_URL_NEW=https://b2c-api.mobilversichert-dev.de
    volumes:
      # Mount source code for hot reload
      - ../../src:/app/src
      - ../../public:/app/public
      - ../../package.json:/app/package.json:ro
      - ../../next.config.mjs:/app/next.config.mjs:ro
      - ../../tailwind.config.js:/app/tailwind.config.js:ro
      - ../../tsconfig.json:/app/tsconfig.json:ro
      # Exclude node_modules only (let .next be writable)
      - /app/node_modules
    restart: unless-stopped
    develop:
      watch:
        # Sync source code changes (hot reload)
        - action: sync
          path: ../../src
          target: /app/src
        # Sync public assets
        - action: sync
          path: ../../public
          target: /app/public
        # Restart when dependencies change
        - action: restart
          path: ../../package.json
        - action: restart
          path: ../../pnpm-lock.yaml
        # Restart when config changes
        - action: restart
          path: ../../next.config.mjs
        - action: restart
          path: ../../tailwind.config.js
        - action: restart
          path: ../../tsconfig.json
        # Restart when environment changes
        - action: restart
          path: ../../.env.local
