Ext.define('Wl.dom.Shadow', {
  override: 'Ext.dom.Shadow',

  mode: 'frame',
  offset: 12,

  beforeShow: function () {
    var me = this,
      style = me.el.dom.style,
      shim = me.shim

    if (Ext.supports.CSS3BoxShadow) {
      style[me.boxShadowProperty] = '0 0 ' + (me.offset + 2) + 'px rgba(0, 66, 117, 0.1)'
    } else {
      style.filter =
        'progid:DXImageTransform.Microsoft.alpha(opacity=' +
        me.opacity +
        ') progid:DXImageTransform.Microsoft.Blur(pixelradius=' +
        me.offset +
        ')'
    }

    // if we are showing a shadow, and we already have a visible shim, we need to
    // realign the shim to ensure that it includes the size of target and shadow els
    if (shim) {
      shim.realign()
    }
  },
})
