Ext.define('Wl.overrides.Text', {
  override: 'Ext.form.field.Text',

  suffix: null,

  initComponent: function () {
    this.updateLabelSparator(this.allowBlank)
    if (this.suffix) {
      const triggers = this.getTriggers()

      this.setTriggers(
        Ext.apply(triggers, {
          suffix: Ext.create('Ext.form.trigger.Trigger', {
            cls: 'x-form-text-suffix',
            weight: -99,
          }),
        })
      )
    }

    this.callParent(arguments)
  },

  updateLabelSparator: function (allowBlank) {
    if (!allowBlank) {
      this.labelSeparator = '<font color=red> * </font>'
    } else {
      this.labelSeparator = null
    }
  },

  setFieldLabel: function (value) {
    if (this.translateFieldLabel) {
      if (arguments && arguments[0] && value && value.indexOf('_') > -1) {
        arguments[0] = translate(value)
      }
    }
    this.callParent(arguments)
  },

  setAllowBlank: function (value) {
    this.allowBlank = value
    this.updateLabelSparator(value)
    this.setFieldLabel(this.fieldLabel)
    this.validate()
  },

  onRender: function () {
    const me = this
    me.callParent()

    if (me.suffix) {
      me.getTriggers().suffix.getEl().dom.innerHTML = me.suffix
    }
  },
})
