{"name": "ext", "namespace": "Ext", "format": "1", "version": "*******", "compatVersion": "7.5.0", "creator": "<PERSON><PERSON>", "summary": "Ext JS", "detailedDescription": "Sencha Ext JS JavaScript Framework", "type": "framework", "signatures": [{"name": "<PERSON><PERSON>", "uuid": "1d6b0d9c-3333-4e65-885f-5b07a1fc3198", "created": "2022-02-04T16:59:06Z", "algorithm": "SHA1withRSA", "nonce": "Ljq1/dZC9OA=", "signature": "csCZoPYiF/wuL7AmHWQPgMnqD1+oauEArNiAnsbu3gCQHCgZBi1A6H1C9Yb8Zw8T/bgGjX1p+EchlHN3zwAyI2sXFTAFPais1L6vfhj103A4Kc9u2IgkayWtveXQ1CTDy7EvZhxCDTzWm2EFX5p2xQXo1IAEegSZEr1m76GK19/qFvTm5d+YSKxF0BFIQRk8nc2varduVZI0Wg0Z3VBO9Bu/ljr1NrB3k3KxhxkArTfoQ19G65tGXs2VpV5jCsdvyQFO5SO4I7kuxFNdoZqJyY4UQzRUqNqvBe3DXpQNJs1icf+4OlDkL5xA47Vjwbsl3R30Ix7VDUw3+8RqHgn3rg=="}], "output": "${package.dir}/build", "build": {"dir": "${package.output.base}"}, "sass": {"fashion": true, "namespace": "Ext", "etc": "${package.dir}/sass/etc/all.scss", "var": "${package.dir}/sass/var", "src": "${package.dir}/sass/src"}, "classpath": "${package.dir}/src", "overrides": "${package.dir}/overrides", "resource": {"paths": ["resources"]}, "example": {"path": ["examples", "templates"], "apps": ["admin-dashboard", "executive-dashboard", "feed-viewer", "kitchensink", "calendar", "classic/desktop", "classic/ticket-app", "classic/portal", "classic/simple-tasks", "classic/responsive-app", "classic/aria", "modern/addressbook", "modern/blackberry", "modern/energy", "modern/stockapp", "modern/states", "modern/geocongress", "modern/oreilly"]}, "subpkgs": {"dir": "${package.dir}", "packages": ["classic/classic", "modern/modern", "packages/charts", "packages/amf", "packages/soap", "packages/ux", "packages/google", "packages/legacy", "packages/font-awesome", "packages/font-pictos", "packages/font-ext"]}, "language": {"js": {"input": {"version": "ES5"}}}, "properties": {"skip.sass": 1, "skip.slice": 1}}