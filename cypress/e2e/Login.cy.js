describe('Login', () => {
  it('allows to login with proper credentials', () => {
    cy.visit('/#login')

    cy.get('.wl-login', { timeout: 10000 }).should('exist')

    cy.getCmp('username').type('<EMAIL>')
    cy.getCmp('password').type('fAQy_Q49Fg')
    cy.getCmp('loginBtn').click()

    cy.url().should('include', '/#contracts/list')
  })

  it('fails to login with invalid credentials', () => {
    cy.visit('/#login')

    cy.get('.wl-login', { timeout: 10000 }).should('exist')

    cy.getCmp('username').type('<EMAIL>')
    cy.getCmp('password').type('Invalid123')
    cy.getCmp('loginBtn').click()

    cy.get('.wl-toast-error').should('be.visible')
    cy.get('.x-mask').should('not.be.visible')

    cy.url().should('not.include', '/#contracts/list')
  })
})
