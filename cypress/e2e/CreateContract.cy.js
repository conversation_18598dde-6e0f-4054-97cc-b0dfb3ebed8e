describe('Create contract', () => {
  beforeEach(() => {
    cy.login()
    cy.visit('/#contracts/create')
  })

  it('has provider and product field disabled if category is not set', () => {
    cy.getCmp('providerField').should('have.class', 'x-item-disabled')
    cy.getCmp('productField').should('have.class', 'x-item-disabled')
  })

  it("won't allow to create contract if required fields are not set", () => {
    cy.get('.contract-create').within(() => {
      cy.getCmp('saveContractButton').click()

      cy.getCmp('categoryField').should('have.class', 'x-form-invalid')
      cy.getCmp('insuranceNumberField').should('have.class', 'x-form-invalid')
      cy.getCmp('costField').should('have.class', 'x-form-invalid')
      cy.getCmp('paymentPeriodField').should('have.class', 'x-form-invalid')
      cy.getCmp('startDateField').should('have.class', 'x-form-invalid')
      cy.getCmp('endDateField').should('have.class', 'x-form-invalid')
    })
  })

  it('allows to create contract if required fields are set', () => {
    cy.get('.contract-create').within(() => {
      cy.getCmp('categoryField')
        .click()
        .invoke('attr', 'id')
        .then((id) => {
          cy.get(`#${id}-picker`, { withinSubject: null }).should('be.visible')
          cy.get(`#${id}-picker li`, { withinSubject: null }).first().click()
        })

      cy.getCmp('providerField').should('not.have.class', 'x-item-disabled')

      cy.getCmp('providerField')
        .click()
        .invoke('attr', 'id')
        .then((id) => {
          cy.get(`#${id}-picker`, { withinSubject: null }).should('be.visible')
          cy.get(`#${id}-picker li`, { withinSubject: null }).first().click()
        })

      cy.getCmp('productField').should('not.have.class', 'x-item-disabled')

      cy.getCmp('productField')
        .click()
        .invoke('attr', 'id')
        .then((id) => {
          cy.get(`#${id}-picker`, { withinSubject: null }).should('be.visible')
          cy.get(`#${id}-picker li`, { withinSubject: null }).first().click()
        })

      cy.getCmp('insuranceNumberField').type('123456789')
      cy.getCmp('costField').type('123')
      cy.getCmp('paymentPeriodField').click()

      cy.getCmp('startDateField')
        .click()
        .invoke('attr', 'id')
        .then((id) => {
          cy.get(`#${id}-picker`, { withinSubject: null }).should('be.visible')
          cy.get(`#${id}-picker td`, { withinSubject: null }).eq(9).click()
        })

      cy.getCmp('endDateField')
        .click()
        .invoke('attr', 'id')
        .then((id) => {
          cy.get(`#${id}-picker`, { withinSubject: null }).should('be.visible')
          cy.get(`#${id}-picker td`, { withinSubject: null }).eq(12).click()
        })
    })
  })
})
