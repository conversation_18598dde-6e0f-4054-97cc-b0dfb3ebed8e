Cypress.Commands.addQuery('getCmp', function (testId, options) {
  const getFn = cy.now('get', '[data-testid="' + testId + '"]', options)
  return function (subject) {
    return getFn(subject)
  }
})

Cypress.Commands.add('login', (username, password) => {
  username = username || '<EMAIL>'

  cy.session(
    username,
    () => {
      cy.visit('/#login')

      cy.get('.wl-login', { timeout: 20000 }).should('exist')

      cy.getCmp('username').type(username)
      cy.getCmp('password').type(password || 'fi7NzHE2SA')
      cy.getCmp('loginBtn').click()

      cy.url().should('not.include', '/#login')
    },
    {
      validate: () => {
        cy.window().then((win) => {
          expect(win.localStorage.getItem('accessToken')).to.exist
        })
      },
    }
  )
})
