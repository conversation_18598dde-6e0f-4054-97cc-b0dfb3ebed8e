
// Colors
$dark-color: dynamic(#142A3A);
$blue-color: dynamic(#3966B2);
$carolina-blue-color: dynamic(#4896DA);
$turquoise-color: #3BCBBF;
$white-color: #FFFFFF;

$light-background-color: dynamic(#F7F8F9);
$dark-background-color: dynamic(#0B1A25);

$gray50-color: dynamic(#E0E0E0);
$gray100-color: dynamic(#C3C9D3);
$gray200-color: dynamic(#8A94A5);
$gray300-color: dynamic(#4E5D79);

$danger-color: dynamic(#9A2921);
$warning-color: dynamic(#FFAD0D);
$success-color: dynamic(#76B729);

$shimmer-background-color: dynamic(#F5F6F9);
$shimmer-highlight-color: dynamic(#E2E6ED);
$shimmer-gradient: dynamic(linear-gradient(97.57deg, $shimmer-highlight-color 37.97%, $shimmer-background-color 56.97%, $shimmer-highlight-color 79.3%));
$lightblue-gradient: dynamic(linear-gradient(90deg, #4896DA 0%, #3966B2 100%));
$turquoise-gradient: dynamic(linear-gradient(90deg, #3BCBBF 0%, #28A398 100%));

// Base
$enable-font-awesome: dynamic(true);
$enable-font-icons: dynamic(true);
$font-family: 'Mulish';
$font-size: 14px;
$font-icon-font-family: dynamic('iconsax', 'Font Awesome 5 Free');
$base-color: $dark-color;
$base-gradient: 'none';

// Panel
$body-background-color: #F7F8F9;
$panel-body-background-color: #F7F8F9;

// Buttons>
$button-default-color: #FFFFFF;
// Button.scss ignores $ignore-frame-padding setting in 7.5.1 and forces frame anyway
// this is why I set border radius directly in CSS and disable it in SCSS
$button-small-border-radius: 0;
$button-medium-border-radius: 0;
$button-large-border-radius: 0;
$button-small-border-width: 0;
$button-medium-border-width: 0;
$button-large-border-width: 0;
$button-medium-padding: 12px;

.x-btn-default-small, .x-btn-default-medium, .x-btn-default-large {
	border-radius: 40px;
}

@mixin extjs-button-link-ui() {

	@include extjs-button-small-ui( 
		"link",

		$color: $blue-color,
		$color-over: $blue-color,
		$color-focus: $blue-color,
		$color-pressed: $blue-color,
		$color-focus-over: $blue-color,
		$color-focus-pressed: $blue-color,
		$color-disabled: $gray100-color,
	);
}

.x-btn-link-small {
	@include extjs-button-link-ui();
}


// Forms
$form-field-height: dynamic(54px);
$form-field-background-color: #FFFFFF;
$form-field-border-color: $form-field-background-color;
$form-field-invalid-background-color: $form-field-background-color;
$form-field-invalid-border-color: $danger-color;
$form-field-focus-border-color: $turquoise-color;
$form-text-field-border-radius: 10px;
$form-label-font-size: 10px;
$form-error-msg-font-size: 10px;
$form-error-icon-height: 11px;
$form-error-under-padding: 4px 2px 2px 0;
$form-error-under-icon-spacing: 2px;
$form-trigger-background-color: none;
$form-trigger-width: 40px;
$form-trigger-glyph-color: $gray200-color;
$form-checkbox-label-spacing: 8px;
$form-checkbox-label-line-height: 19px;

$form-trigger-glyph: "\ff90" 16px $font-icon-font-family;