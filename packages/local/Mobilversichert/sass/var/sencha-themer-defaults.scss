$body_background_color: #fff;
$enable-font-icons: true;

$tag_field_color: dynamic($form_text_field_color);

// links
$link_color: dynamic(none);
$link_text_decoration: dynamic(none);

$link_visited_color: dynamic($link_color);
$link_visited_text_decoration: dynamic($link_text_decoration);

$link_hover_color: dynamic($link_color);
$link_hover_text_decoration: dynamic(underline);

$link_active_color: dynamic($link_color);
$link_active_text_decoration: dynamic($link_hover_text_decoration);

$panel_body_background_color: $body_background_color;
$treelist_toolstrip_background_color: contrast_color_by($body_background_color, 3);
$color: contrast_color_by($body_background_color, 100);
$neutral_color: contrast_color_by($body_background_color, 12);
$scroll_indicator_background_color: contrast_color_by($body_background_color, 100);
$loadmask_background_color: $body_background_color;
$loadmask_msg_background_color: contrast_color_by($body_background_color, 10);
$loadmask_msg_inner_color: contrast_color_by($body_background_color, 60);
$progress_text_color_front: contrast_color_by($body_background_color, 60);
$progress_text_color_back: contrast_color_by($body_background_color, 60);
$button_default_color: $body_background_color;
$button_toolbar_color: contrast_color_by($body_background_color, 60);
$tool_glyph_color: $body_background_color;
$splitter_active_background_color: contrast_color_by($body_background_color, 29);
$splitter_focus_outline_color: $body_background_color;
$splitter_collapse_tool_border_color: $body_background_color;
$toolbar_background_color: $body_background_color;
$toolbar_separator_highlight_color: $body_background_color;
$statusproxy_background_color: $body_background_color;
$panel_tool_focus_outline_color: $body_background_color;
$panel_header_inner_border_color: $body_background_color;
$panel_header_color: $body_background_color;
$panel_frame_background_color: $body_background_color;
$panel_frame_header_inner_border_color: $body_background_color;
$btn_group_background_color: $body_background_color;
$btn_group_inner_border_color: $body_background_color;
$panel_light_header_color: contrast_color_by($body_background_color, 60);
$window_inner_border_color: $body_background_color;
$window_background_color: $body_background_color;
$window_body_background_color: $body_background_color;
$window_body_color: contrast_color_by($body_background_color, 100);
$window_header_inner_border_color: $body_background_color;
$form_field_empty_color: contrast_color_by($body_background_color, 50);
$form_field_background_color: $body_background_color;
$form_field_invalid_background_color: $body_background_color;
$messagebox_info_glyph_color: contrast_color_by($body_background_color, 50);
$messagebox_question_glyph_color: contrast_color_by($body_background_color, 50);
$boundlist_background_color: $body_background_color;
$datepicker_background_color: $body_background_color;
$datepicker_item_disabled_background_color: contrast_color_by($body_background_color, 7);
$tip_body_color: contrast_color_by($body_background_color, 100);
$tip_error_inner_border_color: $body_background_color;
$colorpicker_background_color: $body_background_color;
$tag_field_item_selected_color: $body_background_color;
$grid_row_cell_background_color: $body_background_color;
$grid_row_cell_border_color: contrast_color_by($body_background_color, 7);
$grid_empty_color: contrast_color_by($body_background_color, 50);
$grid_resize_marker_background_color: contrast_color_by($body_background_color, 94);
$menu_glyph_color: contrast_color_by($body_background_color, 50);
$grid_column_header_color: contrast_color_by($body_background_color, 60);
$grid_grouped_title_color: contrast_color_by($body_background_color, 60);
$menu_background_color: $body_background_color;
$menu_text_color: contrast_color_by($body_background_color, 100);
$menu_separator_background_color: $body_background_color;
$accordion_header_color: contrast_color_by($body_background_color, 60);
$accordion_background_color: $body_background_color;
$slider_thumb_background_color: $body_background_color;
$tab_color: $body_background_color;
$tabbar_scroller_glyph_color: $body_background_color;
$include_slicer_gradient: $base_gradient;
$loadmask_msg_background_gradient: $base_gradient;
$progress_bar_background_gradient: $base_gradient;
$button_default_background_gradient: $base_gradient;
$button_default_background_gradient_over: $base_gradient;
$button_default_background_gradient_focus: $base_gradient;
$button_default_background_gradient_focus_over: $base_gradient;
$button_default_background_gradient_disabled: $base_gradient;
$button_toolbar_background_gradient: $base_gradient;
$button_toolbar_background_gradient_over: $base_gradient;
$button_toolbar_background_gradient_focus: $base_gradient;
$button_toolbar_background_gradient_focus_over: $base_gradient;
$button_toolbar_background_gradient_disabled: $base_gradient;
$button_grid_cell_background_gradient: $base_gradient;
$button_grid_cell_background_gradient_over: $base_gradient;
$button_grid_cell_background_gradient_focus: $base_gradient;
$button_grid_cell_background_gradient_focus_over: $base_gradient;
$button_grid_cell_background_gradient_disabled: $base_gradient;
$toolbar_background_gradient: $base_gradient;
$panel_header_background_gradient: $base_gradient;
$datepicker_header_background_gradient: $base_gradient;
$datepicker_column_header_background_gradient: $base_gradient;
$datepicker_footer_background_gradient: $base_gradient;
$tip_background_gradient: $base_gradient;
$grid_cell_special_background_gradient: $base_gradient;
$grid_header_background_gradient: $base_gradient;
$grid_header_over_background_gradient: $base_gradient;
$grid_header_sorted_background_gradient: $base_gradient;
$menu_item_background_gradient: $base_gradient;
$tab_background_gradient: $base_gradient;
$tabbar_background_gradient: $base_gradient;
$button_default_background_gradient_pressed: reverse_gradient_for($base_gradient);
$button_default_background_gradient_focus_pressed: reverse_gradient_for($base_gradient);
$button_toolbar_background_gradient_pressed: reverse_gradient_for($base_gradient);
$button_toolbar_background_gradient_focus_pressed: reverse_gradient_for($base_gradient);
$button_grid_cell_background_gradient_pressed: reverse_gradient_for($base_gradient);
$button_grid_cell_background_gradient_focus_pressed: reverse_gradient_for($base_gradient);