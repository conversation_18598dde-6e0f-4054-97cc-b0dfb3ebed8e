# Updated: 09.04.2024-15:26:51
# MGIS Pipeline Javascript 1.1
# Author: <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>
# Created: Jan. 2024
image: atlassian/default-image:3

definitions:
  services:
    docker:
      memory: 3072 # Memory in MB -  allocate 3GB (3072MB) of memory to docker service
  image: &mgis-image
    name: mgiscommonacr.azurecr.io/bitbucket-pipelines-base-image:1.0.0
    username: $MGIS_ACR_USER
    password: $MGIS_ACR_PASSWORD
  after-script: &notify-build
    - |
      if [ $BITBUCKET_EXIT_CODE = 0 ]; then
      curl -H "Content-Type:application/json" -d "{'text':'Build from "$BITBUCKET_BRANCH" Branch was successful! Good Work :-) '}" $TEAMS_NOTIFICATION
      else
      if [ $BITBUCKET_EXIT_CODE = 1 ]; then
      curl -H "Content-Type:application/json" -d "{'text':'Build from "$BITBUCKET_BRANCH" Branch was not successful! Find the Bug :-( '}" $TEAMS_NOTIFICATION
      fi
  steps:
    - step: &build-and-push-container
        name: Build and Push
        script:
          - echo $MGIS_ACR_PASSWORD | docker login $MGIS_ACR_HOST -u $MGIS_ACR_USER --password-stdin
          - IMAGE_NAME="$MGIS_ACR_HOST/$BITBUCKET_REPO_SLUG:${BITBUCKET_COMMIT::7}"
          - docker build . --file .docker/production/Dockerfile --tag ${IMAGE_NAME}
          - docker push ${IMAGE_NAME}
        after-script: *notify-build
        services:
          - docker
        caches:
          - docker
    - step: &create-deployment
        name: Create New-Infra Deployment Entry
        image: *mgis-image
        script:
          - git clone https://x-token-auth:$<EMAIL>/mgis/$BITBUCKET_REPO_SLUG-deployment.git
          - DESTINATION=$BITBUCKET_DEPLOYMENT_ENVIRONMENT
          - cd $BITBUCKET_REPO_SLUG-deployment
          - git checkout main
          - git config user.email $CICD_EMAIL && git config user.name Bitbucket-Deployment
          - sed -i '/^RELEASE_DATE/d' ./environments/${DESTINATION}/configs/config.env
          - sed -i '/^RELEASE_NAME/d' ./environments/${DESTINATION}/configs/config.env
          - echo "RELEASE_NAME=${BITBUCKET_COMMIT::7}" >> ./environments/${DESTINATION}/configs/config.env
          - echo "RELEASE_DATE=$(date +'%d.%m.%Y-%H:%M:%S')" >> ./environments/${DESTINATION}/configs/config.env
          - cd ./environments/${DESTINATION}
          - kustomize edit set image $MGIS_ACR_HOST/$BITBUCKET_REPO_SLUG=$MGIS_ACR_HOST/$BITBUCKET_REPO_SLUG:${BITBUCKET_COMMIT::7}
          - git add -A
          - git commit -a -m "New ${BITBUCKET_DEPLOYMENT_ENVIRONMENT} deployment from commit ${BITBUCKET_COMMIT::7}"
          - git push

pipelines:
  default:
    - step:
        name: Build from Branch
        script:
          - echo $MGIS_ACR_PASSWORD | docker login $MGIS_ACR_HOST -u $MGIS_ACR_USER --password-stdin
          - docker build . --file .docker/production/Dockerfile
        after-script: *notify-build
        services:
          - docker
        caches:
          - docker
  branches:
    develop:
      - step: *build-and-push-container
      - step:
          <<: *create-deployment
          deployment: dev
          trigger: automatic
    staging:
      - step: *build-and-push-container
      - step:
          <<: *create-deployment
          deployment: stg
          trigger: manual
    ## To handle old and new Repos, we listen to the Main and Master Branch
    master:
      - step: *build-and-push-container
      - step:
          <<: *create-deployment
          deployment: prd
          trigger: manual
    main:
      - step: *build-and-push-container
      - step:
          <<: *create-deployment
          deployment: prd
          trigger: manual