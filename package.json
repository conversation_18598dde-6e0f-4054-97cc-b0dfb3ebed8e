{"scripts": {"watch": "OPENSSL_CONF=/dev/null sencha app watch -po 1842 2>&1 | grep --color=always -v 'Nashorn'", "clean": "rm -rf build && rm -rf *.log", "build:production": "OPENSSL_CONF=/dev/null bash -c 'sencha app build production && sed -i '' -e \"s/@@COMMIT_HASH/${1:-$(git rev-parse HEAD)}/g\" ./build/production/Wl/app.js' 0", "build:testing": "OPENSSL_CONF=/dev/null sencha app build testing", "build:wp-plugin": "bash -c 'npm run build:production ${1:-0.0.0-SNAPSHOT} && rsync -av wp-plugin/ build/mgis-client-whitelabel --delete && sed -i '' -e \"s/@@VERSION/${1:-0.0.0-SNAPSHOT}/g\" ./build/mgis-client-whitelabel/mgis-client.php && rsync -av ./build/production/Wl/ ./build/mgis-client-whitelabel/public && cd build && zip -FSr ../mgis-client-whitelabel-${1:-0.0.0-SNAPSHOT}.zip ./mgis-client-whitelabel' 0", "serve:testing": "http-server build/testing/Wl -c-1 -g -p 1842 --silent", "test:e2e:watch": "npm run build:testing && concurrently --kill-others \"start-server-and-test serve:testing http://mvp-local.mobilversichert-dev.de:1842 test:e2e:runner:interactive\" \"nodemon --watch app --watch overrides --exec 'npm run build:testing'\"", "test:e2e:runner:headless": "cypress run --headless -b firefox", "test:e2e:runner:interactive": "cypress open -b firefox", "test:e2e": "npm run build:testing && start-server-and-test serve:testing http://mvp-local.mobilversichert-dev.de:1842 test:e2e:runner:headless", "test:tdd": "karma start karma.conf.js", "prettify": "prettier --write .", "start:server": "http-server build/production/Mvpe -p 4200 -o -a mvpe-local.mobilversichert-dev.de", "start:local": "npm run build && start:server"}, "devDependencies": {"concurrently": "^8.0.1", "cypress": "^12.11.0", "eslint": "^8.57.0", "http-server": "^14.1.1", "jasmine-ajax": "^4.0.0", "jasmine-core": "^4.6.0", "jasmine-expect-count": "^1.1.2", "karma": "^6.4.2", "karma-chrome-launcher": "^3.2.0", "karma-coverage": "^2.2.0", "karma-firefox-launcher": "^2.1.2", "karma-jasmine": "^5.1.0", "nodemon": "^2.0.22", "prettier": "^3.2.5", "puppeteer": "^20.1.2", "start-server-and-test": "^2.0.0", "wait-on": "^7.0.1"}}