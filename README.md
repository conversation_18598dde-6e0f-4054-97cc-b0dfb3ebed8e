# MobilVersichert Client MVP

## Project Structure

```
client-mvp/
├── public/            # Static assets
├── src/               # Source code
│   ├── app/           # Next.js app router pages
│   ├── components/    # Reusable UI components
│   ├── constants/     # Application constants
│   ├── hooks/         # Custom React hooks
│   ├── i18n/          # Internationalization files
│   ├── lib/           # Utility functions and libraries
│   ├── modules/       # Feature-specific modules
│   ├── providers/     # React context providers
│   └── types/         # TypeScript type definitions
├── .env.example       # Example environment variables
├── .env.local         # Local environment variables (not committed)
├── next.config.mjs     # Next.js configuration
├── package.json       # Project dependencies and scripts
└── tsconfig.json      # TypeScript configuration
```

## Getting Started

### Prerequisites

- Node.js 18.x or later
- npm package manager

### Installation

1. Clone the repository:

   ```bash
   <NAME_EMAIL>:mgis/client-mvp.git
   cd client-mvp
   ```

2. Install dependencies:

   ```bash
   npm install
   ```

3. Set up environment variables:
   - Copy `.env.example` to `.env.local`
   - Update the values in `.env.local` with your specific configuration

### Development Scripts

The project includes the following npm scripts:

#### Development Server

- **`npm run dev`**: Starts the development server at [http://localhost:3000](http://localhost:3000) using Turbopack for faster builds

- **`npm run dev:host`**: Starts the server with a custom hostname (`client-mvp-local.mobilversichert-dev.de:3000`) for testing domain-specific features

  **Setup for custom hostname:**

  1. Add to hosts file: `127.0.0.1 client-mvp-local.mobilversichert-dev.de`
     - On macOS/Linux: Edit `/etc/hosts`
     - On Windows: Edit `C:\Windows\System32\drivers\etc\hosts`
  2. Set in `.env.local`: `APP_URL=http://client-mvp-local.mobilversichert-dev.de`

  **Benefits:**

  - Required for certain authentication flows that depend on specific domains
  - Enables proper cookie handling with domain-specific settings
  - Provides closer simulation of production environment

#### Other Scripts

- `npm run build`: Creates a production build
- `npm run start`: Starts the production server
- `npm run lint`: Runs ESLint to check code quality

## Technologies

This project uses:

- **Next.js 15**: React framework with App Router
- **React 19**: UI library
- **TypeScript**: Type-safe JavaScript
- **Tailwind CSS**: Utility-first CSS framework
- **Radix UI**: Accessible component primitives
- **shadcn/ui**: Component library built on Radix UI and Tailwind CSS
- **React Query**: Data fetching and state management
- **Zod**: Schema validation
- **Jose**: JWT encryption and decryption for custom authentication
- **next-intl**: Internationalization

## Authentication

The project implements a custom JWT-based authentication system:

- **Token Management**: Uses access and refresh tokens stored in encrypted HTTP-only cookies
- **Route Protection**: Utilizes Next.js middleware to protect routes and redirect unauthenticated users
- **SSO Integration**: Communicates with an external SSO service for authentication operations
- **Session Encryption**: Secures session data using the Jose library with AES-128-CBC-HS256 algorithm
- **Automatic Token Refresh**: Implements token refresh mechanism to maintain user sessions

## Contributing

1. Create a feature branch from the main branch
2. Make your changes
3. Submit a pull request
