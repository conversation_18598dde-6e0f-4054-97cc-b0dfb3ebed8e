# vim: set tabstop=8 softtabstop=8 noexpandtab:

pwd := $(shell dirname $(realpath $(firstword $(MAKEFILE_LIST))))
dc := docker-compose -f .docker/local/docker-compose.yml -p mvpe --env-file $(pwd)/.env.local
#dc := docker-compose -f .docker/production/docker-compose.yml -p mvp --env-file $(pwd)/.env.local
dcbuild := docker-compose -f .docker/build-base-image/docker-compose.yml -p client-whitelabel-sencha --env-file $(pwd)/.env.local

branch ?= develop
update:
	git fetch --all
	git reset --hard origin/$(branch)
	make restart

setup: build up

_build:
	$(dc) build

up:
	$(dc) up -d

up-show:
	$(dc) up

down:
	$(dc) down --remove-orphans

rebuild: down _build up
restart: down up

cli:
	$(dc) exec mvpe bash

ps:
	$(dc) ps

ps-image:
	$(dcbuild) ps

up-image:
	$(dcbuild) up

cli-image:
	$(dcbuild) exec client-whitelabel-sencha bash

git-hash:
	git rev-parse HEAD

build-main-image:
	$(dcbuild) build
	docker tag mgis/client-whitelabel-sencha mgisinfrastructureacr1.azurecr.io/production/images/client-whitelabel-sencha:0.0.1
	docker tag mgis/client-whitelabel-sencha mgisinfrastructureacr1.azurecr.io/production/images/client-whitelabel-sencha:latest
	docker push mgisinfrastructureacr1.azurecr.io/production/images/client-whitelabel-sencha:0.0.1
	docker push mgisinfrastructureacr1.azurecr.io/production/images/client-whitelabel-sencha:latest
