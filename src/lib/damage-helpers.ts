/**
 * Helper functions for damage-related operations
 * Mirrors the formulas from ExtJS DamagesDetailsModel
 */

import type { Damage, DamageStatus, DamageType, DamageEntity, DamageReason } from '@/modules/damages/types/damage-types'
import { DAMAGE_TYPES, DAMAGE_ENTITIES, DAMAGE_STATUSES } from '@/modules/damages/types/damage-types'

/**
 * Get status in lowercase format
 * Mirrors: statusLowercase formula from ExtJS
 */
export function getStatusLowercase(status: DamageStatus | undefined): string {
  return (status || '').toLowerCase()
}

/**
 * Get translated entity value
 * Mirrors: entityTranslated formula from ExtJS
 */
export function getEntityTranslated(entity: DamageEntity | undefined): string {
  if (!entity) return ''
  return DAMAGE_ENTITIES[entity] || entity
}

/**
 * Get translated reason value
 * Mirrors: reasonTranslated formula from ExtJS
 */
export function getReasonTranslated(reason: DamageReason | undefined): string {
  if (!reason) return ''
  // For now, return the reason as-is since we don't have the full translation mapping
  // In the future, this could be enhanced with proper translation keys
  return reason.replace(/_/g, ' ').toLowerCase().replace(/\b\w/g, l => l.toUpperCase())
}

/**
 * Get translated type value
 * Mirrors: typeTranslated formula from ExtJS
 */
export function getTypeTranslated(type: DamageType | undefined): string {
  if (!type) return ''
  return DAMAGE_TYPES[type] || type
}

/**
 * Get translated status name
 * Mirrors: statusName formula from ExtJS
 */
export function getStatusName(status: DamageStatus): string {
  return DAMAGE_STATUSES[status] || status
}

/**
 * Get status color for UI badges
 * Enhanced version of the inline function in damage-details.tsx
 */
export function getStatusColor(status: DamageStatus): string {
  switch (status) {
    case 'ACTIVE':
    case 'OPEN':
      return 'bg-green-100 text-green-800 hover:bg-green-100'
    case 'PENDING':
      return 'bg-yellow-100 text-yellow-800 hover:bg-yellow-100'
    case 'CLOSED':
      return 'bg-gray-100 text-gray-800 hover:bg-gray-100'
    case 'REJECTED':
      return 'bg-red-100 text-red-800 hover:bg-red-100'
    default:
      return 'bg-gray-100 text-gray-800 hover:bg-gray-100'
  }
}

/**
 * Get damage display title
 * Creates a user-friendly title for the damage
 */
export function getDamageDisplayTitle(damage: Damage): string {
  if (damage.title) {
    return damage.title
  }
  
  // Fallback to damage ID with last 3 characters
  const shortId = damage.id ? damage.id.slice(-3) : '???'
  return `Damage ${shortId}`
}

/**
 * Get damage internal number or fallback
 * Returns internal number or last 8 characters of ID
 */
export function getDamageInternalNumber(damage: Damage): string {
  if (damage.internalNumber) {
    return damage.internalNumber
  }
  
  // Fallback to last 8 characters of ID
  return damage.id ? damage.id.slice(-8) : ''
}

/**
 * Format currency amount
 * Helper for displaying monetary values
 */
export function formatCurrencyAmount(amount: number | undefined, currency: string = 'EUR'): string {
  if (amount === undefined || amount === null) {
    return ''
  }
  
  return new Intl.NumberFormat('de-DE', {
    style: 'currency',
    currency: currency,
  }).format(amount)
}

/**
 * Get contract provider name
 * Helper for displaying contract information
 */
export function getContractProviderName(damage: Damage): string {
  // Try multiple possible sources for provider name
  return damage.contractProviderName || 
         damage.contract?.provider_name || 
         ''
}

/**
 * Get contract insurance number
 * Helper for displaying contract information
 */
export function getContractInsuranceNumber(damage: Damage): string {
  return damage.contractInsuranceNumber || 
         damage.contract?.insurance_number || 
         ''
}

/**
 * Check if damage has police report
 * Helper for conditional rendering
 */
export function hasPoliceReport(damage: Damage): boolean {
  return !!(damage.policeReportDate || damage.externalNumberPolice)
}

/**
 * Check if damage has provider report
 * Helper for conditional rendering
 */
export function hasProviderReport(damage: Damage): boolean {
  return !!(damage.providerReportDate || damage.externalNumberProvider)
}

/**
 * Get damage severity based on estimated amount
 * Helper for categorizing damage severity
 */
export function getDamageSeverity(estimatedAmount: number | undefined): 'low' | 'medium' | 'high' | 'unknown' {
  if (!estimatedAmount) return 'unknown'
  
  if (estimatedAmount < 1000) return 'low'
  if (estimatedAmount < 5000) return 'medium'
  return 'high'
}

/**
 * Get damage age in days
 * Helper for calculating how old a damage report is
 */
export function getDamageAgeInDays(damageDate: string | undefined): number | null {
  if (!damageDate) return null
  
  const damage = new Date(damageDate)
  const now = new Date()
  const diffTime = Math.abs(now.getTime() - damage.getTime())
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
  
  return diffDays
}

/**
 * Check if damage is recent (within last 7 days)
 * Helper for highlighting recent damages
 */
export function isDamageRecent(damageDate: string | undefined): boolean {
  const ageInDays = getDamageAgeInDays(damageDate)
  return ageInDays !== null && ageInDays <= 7
}
