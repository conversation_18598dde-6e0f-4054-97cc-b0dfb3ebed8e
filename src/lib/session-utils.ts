import { AuthSession } from '@/modules/auth/types/auth-types'

export function checkIsSessionExpired(session: AuthSession | null) {
  if (!session?.expiresAt) {
    return true
  }

  const now = Date.now()

  const isExpired = session.expiresAt - now <= 0
  // const expiresIn = Math.floor((session.expiresAt - now) / 1000 / 60) // minutes left

  // if (isExpired) {
  //   console.log('Session expired')
  // } else {
  //   console.log('Session expires in:', expiresIn, 'minutes')
  // }

  return isExpired
}
