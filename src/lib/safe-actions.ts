import { AuthSession } from '@/modules/auth/types/auth-types'
import { createMiddleware, createSafeActionClient, DEFAULT_SERVER_ERROR_MESSAGE } from 'next-safe-action'

import { ApiError } from './fetch-api'

// Create an auth middleware that requires a valid session
// For server actions, we'll use the getSession() from session.ts which handles refresh
const authMiddleware = createMiddleware<{
  ctx: { session?: AuthSession | null }
}>().define(async ({ next }) => {
  try {
    // Import getSession here to avoid circular dependency
    const { getSession } = await import('@/modules/auth/actions/session')

    const session = await getSession()

    if (!session?.accessToken) {
      throw new ApiError('Unauthorized: No valid session found')
    }

    return next({ ctx: { session } })
  } catch (error) {
    console.error('❌ [authMiddleware] Session validation failed:', error)
    throw new ApiError('Unauthorized: Session validation failed')
  }
})

// Create a logging middleware
export const loggingMiddleware = createMiddleware().define(async ({ next, clientInput, metadata }) => {
  // Log input data
  if (clientInput) {
    console.log('%c 📥 [Action] Input:', 'color: #3498db;', clientInput)
  }

  if (metadata) {
    console.log('%c 📋 [Action] Metadata:', 'color: #3498db;', metadata)
  }

  // Execute the action
  const result = await next()

  // if (result.data) {
  //   console.log('%c 📤 [Action] Result:', 'color: #2ecc71;', result.data)
  // }

  if (result.validationErrors) {
    console.log('%c ⚠️ [Action] Validation errors:', 'color: #f39c12; font-weight: bold;', result.validationErrors)
  }

  if (result.serverError) {
    console.log('%c ❌ [Action] Server error:', 'color: #e74c3c; font-weight: bold;', result.serverError)
  }

  return result
})

// Create the base action client
export const action = createSafeActionClient({
  handleServerError: (error) => {
    console.error('%c ❌ [Action Error]', 'color: #e74c3c; font-weight: bold;', error)

    if (error instanceof ApiError) {
      return error.message
    }

    if (error instanceof Error) {
      // In development, return the actual error message
      if (process.env.NODE_ENV === 'development') {
        return error.message
      }
    }

    return DEFAULT_SERVER_ERROR_MESSAGE
  },
}).use(loggingMiddleware)

// Create an authenticated action client
export const authenticatedAction = action.use(authMiddleware)
