import { z } from 'zod'

const DocumentUploadTypeEnum = z.enum(['AVATAR', 'DOCUMENT', 'PICTURE'] as const)
const DocumentOwnerTypeEnum = z.enum(['CLIENT', 'CONTRACT', 'DAMAGE'] as const)

export type DocumentUploadType = z.infer<typeof DocumentUploadTypeEnum>
export type DocumentOwnerType = z.infer<typeof DocumentOwnerTypeEnum>

// TypeScript types derived from the arrays

export const DocumentUploadSchema = z.object({
  file: z.object({
    type: z.string(),
    name: z.string(),
  }),
  name: z.string(),
  image: z.string(),
  type: DocumentUploadTypeEnum,
  owner_type: DocumentOwnerTypeEnum,
  owner_id: z.string(),
})

export type DocumentInputs = z.infer<typeof DocumentUploadSchema>
