'use server'

import { getAppConfig } from '@/app-config'
import { getSession } from '@/modules/auth/actions/session'
import qs from 'qs'

import { fetchApi } from '@/lib/fetch-api'

export async function downloadFile(documentId: string) {
  const config = getAppConfig()
  const session = await getSession()

  if (!config.apiBaseUrl || !session?.accessToken) {
    throw new Error('API base URL is not defined or no valid session available')
  }
  const queryParams: Record<string, string | number> = {
    document_id: documentId,
    access_token: session.accessToken,
    download: 1,
  }

  const queryString = qs.stringify(queryParams, {
    addQueryPrefix: true,
    encode: false,
  })

  const downloadUrl = `${config.apiBaseUrl}/b2c/file?${queryString}`

  const response = await fetchApi(downloadUrl, {})
  if (response.error) {
    return {
      error: response.error,
    }
  }

  return response.data
}
