import { getAppConfig } from '@/app-config'
import qs from 'qs'

/**
 * Image dimensions type based on ExtJS implementation patterns
 * These sizes match the ExtJS project's image sizing strategy
 */
export type ImageDimensions =
  | {
      width: number
      height: number
    }
  | undefined // Profile images

interface GetImageProps {
  documentId: string
  accessToken: string
  width?: number
  height?: number
  dimensions?: ImageDimensions
  download?: boolean
}

/**
 * Image size constants based on ExtJS implementation patterns
 * These dimensions match the ExtJS project's image sizing strategy
 */
export const IMAGE_SIZES = {
  // Small thumbnails for lists and compact views
  THUMBNAIL: { width: 100, height: 100 },
  // Medium size for cards and profile images
  MEDIUM: { width: 260, height: 260 },
  // Large size for detailed views
  LARGE: { width: 500, height: 500 },
  // Profile images
  PROFILE: undefined,
} as const satisfies Record<string, ImageDimensions>

/**
 * Generate image URL with proper sizing
 * Uses separate width and height parameters to match the actual API format
 *
 * @param documentId - The document ID to fetch
 * @param accessToken - Authentication token
 * @param width - Image width (takes precedence over dimensions)
 * @param height - Image height (takes precedence over dimensions)
 * @param dimensions - Predefined dimensions object (used if width/height not provided)
 * @param download - Whether to force download
 * @returns Complete image URL with appropriate w and h parameters
 */
export function getImageURL({
  documentId,
  accessToken,
  width,
  height,
  dimensions = IMAGE_SIZES.MEDIUM,
  download = false,
}: GetImageProps) {
  const config = getAppConfig()

  if (!config.apiBaseUrl) {
    throw new Error('API base URL is not defined')
  }

  // Build query parameters using qs library for better URL handling
  const queryParams: Record<string, string | number> = {
    document_id: documentId,
    access_token: accessToken,
  }

  // Determine final width and height values
  // Priority: direct width/height params > dimensions object > no sizing (profile images)
  const finalWidth = width ?? dimensions?.width
  const finalHeight = height ?? dimensions?.height

  // Add width and height parameters if provided (for profile images, both are undefined)
  if (finalWidth !== undefined) {
    queryParams.w = finalWidth
  }
  if (finalHeight !== undefined) {
    queryParams.h = finalHeight
  }

  // Add download parameter if requested
  if (download) {
    queryParams.download = download ? 1 : 0
  }

  // Generate clean URL using qs library
  const queryString = qs.stringify(queryParams, {
    addQueryPrefix: true,
    encode: false, // Don't encode since these are safe parameters
  })

  // TODO: Use new API URL when available
  const imageUrl = `${config.apiBaseUrl}/b2c/file${queryString}`

  return imageUrl
}
