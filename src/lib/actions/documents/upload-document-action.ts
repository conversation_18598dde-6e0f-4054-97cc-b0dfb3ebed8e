'use server'

import 'server-only'

import { fetchApi } from '@/lib/fetch-api'
import { authenticatedAction } from '@/lib/safe-actions'

import { DocumentUploadSchema } from './documents-types'

export const uploadDocumentAction = authenticatedAction
  .schema(DocumentUploadSchema)
  .action(async ({ parsedInput, ctx: { session } }) => {
    // Upload image as avatar document
    const imageResponse = await fetchApi(
      '/documents_base64',
      {
        method: 'POST',
        body: JSON.stringify({
          owner_id: session?.user.id,
          owner_type: parsedInput.owner_type,
          type: parsedInput.type,
          image: parsedInput.image,
          name: parsedInput.name,
          file: {
            name: parsedInput.name,
            type: parsedInput.file.type,
          },
        }),
      },
      session
    )

    if (imageResponse.error) {
      throw new Error(imageResponse.error)
    }

    return imageResponse.data
  })
