import locationsData from '@/data/locations-de.json'

// Type definition for the location data structure
type LocationData = {
  c: string // city
  d: string // district
  z: number // zip code
}

export function getGermanZipCodes() {
  // Cast the imported data to the correct type
  const typedLocationsData = locationsData as LocationData[]

  // Extract unique zip codes and convert to the expected format
  const uniqueZipCodes = [...new Set(typedLocationsData.map((location) => location.z.toString()))]

  return uniqueZipCodes.map((zip) => ({
    value: zip,
    label: zip,
  }))
}

export function getCitiesForZipCode(zipCode: string) {
  // Cast the imported data to the correct type
  const typedLocationsData = locationsData as LocationData[]

  // Filter locations for the given zip code and map to city options
  const cityOptions = typedLocationsData
    .filter((location) => location.z.toString() === zipCode)
    .map((location) => ({
      value: location.c + ' - ' + location.d,
      label: location.c + ' - ' + location.d,
      zipCode: location.z,
    }))

  return cityOptions
}
