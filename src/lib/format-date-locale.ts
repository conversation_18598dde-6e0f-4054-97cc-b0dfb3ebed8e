import { getUserLocale } from '@/i18n/i18n-actions'
import type { Locale } from '@/i18n/i18n-libs'
import { format } from 'date-fns'
import { de, enGB } from 'date-fns/locale'

// Map your app locales to date-fns locales
const dateFnsLocales = {
  en: enGB,
  de: de,
} as const

// Server-side function (async)
export const formatDateLocale = async (
  date: Date | string | number,
  formatString: string = 'PPP' // Default: "April 29th, 2023" / "29. April 2023"
) => {
  const locale = (await getUserLocale()) as Locale
  const dateFnsLocale = dateFnsLocales[locale] || dateFnsLocales.en

  // Convert string or number to Date if needed
  const dateObj = typeof date === 'string' || typeof date === 'number' ? new Date(date) : date

  return format(dateObj, formatString, { locale: dateFnsLocale })
}

// Client-side function (synchronous) - for use in client components
export const formatDateLocaleClient = (
  date: Date | string | number,
  locale: Locale,
  formatString: string = 'PPP' // Default: "April 29th, 2023" / "29. April 2023"
) => {
  const dateFnsLocale = dateFnsLocales[locale] || dateFnsLocales.en

  // Convert string or number to Date if needed
  const dateObj = typeof date === 'string' || typeof date === 'number' ? new Date(date) : date

  return format(dateObj, formatString, { locale: dateFnsLocale })
}

// Server-side convenience functions for common date formats
export const formatDateShort = async (date: Date | string | number) => {
  return formatDateLocale(date, 'P') // "04/29/2023" / "29.04.2023"
}

export const formatDateMedium = async (date: Date | string | number) => {
  return formatDateLocale(date, 'PP') // "Apr 29, 2023" / "29. Apr. 2023"
}

export const formatDateLong = async (date: Date | string | number) => {
  return formatDateLocale(date, 'PPP') // "April 29th, 2023" / "29. April 2023"
}

export const formatDateFull = async (date: Date | string | number) => {
  return formatDateLocale(date, 'PPPP') // "Saturday, April 29th, 2023" / "Samstag, 29. April 2023"
}

export const formatDateTime = async (date: Date | string | number) => {
  return formatDateLocale(date, 'PPp') // "Apr 29, 2023 at 1:45 PM" / "29. Apr. 2023 um 13:45"
}

export const formatDateTimeFull = async (date: Date | string | number) => {
  return formatDateLocale(date, 'PPPp') // "April 29th, 2023 at 1:45 PM" / "29. April 2023 um 13:45"
}

// Client-side convenience functions for common date formats
export const formatDateShortClient = (date: Date | string | number, locale: Locale) => {
  return formatDateLocaleClient(date, locale, 'P') // "04/29/2023" / "29.04.2023"
}

export const formatDateMediumClient = (date: Date | string | number, locale: Locale) => {
  return formatDateLocaleClient(date, locale, 'PP') // "Apr 29, 2023" / "29. Apr. 2023"
}

export const formatDateLongClient = (date: Date | string | number, locale: Locale) => {
  return formatDateLocaleClient(date, locale, 'PPP') // "April 29th, 2023" / "29. April 2023"
}

export const formatDateFullClient = (date: Date | string | number, locale: Locale) => {
  return formatDateLocaleClient(date, locale, 'PPPP') // "Saturday, April 29th, 2023" / "Samstag, 29. April 2023"
}

export const formatDateTimeClient = (date: Date | string | number, locale: Locale) => {
  return formatDateLocaleClient(date, locale, 'PPp') // "Apr 29, 2023 at 1:45 PM" / "29. Apr. 2023 um 13:45"
}

export const formatDateTimeFullClient = (date: Date | string | number, locale: Locale) => {
  return formatDateLocaleClient(date, locale, 'PPPp') // "April 29th, 2023 at 1:45 PM" / "29. April 2023 um 13:45"
}
