import { UserProfile } from '@/modules/auth/types/auth-types'
import { Expert } from '@/modules/expert/types/expert-types'
import { getTranslations } from 'next-intl/server'

import { getImageURL } from '@/lib/actions/documents/get-image'
import { formatDateShort } from '@/lib/format-date-locale'

interface ReplaceAcceptanceRequestWithValuesArgs {
  agencies: {
    name: string
    legalForm: string
    street: string
    streetNumber: string
    zip: string
    city: string
    phone: string
    imgId: {
      tag: 'img'
      id: string | number
      isProtected?: boolean
    }
  }
  clients: {
    salutation: string
    firstName: string
    lastName: string
    street: string
    streetNum: string
    zip: string
    city: string
    birthdate: string
    email: string
    phone: string
  }
  texts: {
    appealing_salutation: string
    today: string
  }
}

interface MatchedValueBase {
  source: keyof ReplaceAcceptanceRequestWithValuesArgs
  field: string
}

interface ImageMatchedValue extends MatchedValueBase {
  width: number
}

type MatchedValue = MatchedValueBase | ImageMatchedValue

const isImageMarker = (matchedItem: MatchedValue): matchedItem is ImageMatchedValue => {
  return matchedItem.field === 'imgId'
}

function replaceAcceptanceRequestWithValues(
  acceptanceRequest: string,
  args: ReplaceAcceptanceRequestWithValuesArgs,
  accessToken: string
): string {
  let textContent = acceptanceRequest
  const matched_json_regex = /(\{|\[).+?(\}|\])/gm
  const matchedItems = textContent ? textContent.match(matched_json_regex) : null

  if (Array.isArray(matchedItems)) {
    matchedItems.forEach(function (matchedItem) {
      try {
        const matchedItemJson = JSON.parse(matchedItem) as MatchedValue

        const recordToUse = args && args[matchedItemJson.source] ? args[matchedItemJson.source] : null

        let valuesToLoad = recordToUse ? (recordToUse as any)[matchedItemJson.field] : ''

        if (typeof valuesToLoad !== 'string') {
          if (valuesToLoad.tag === 'img' && isImageMarker(matchedItemJson)) {
            const imageLink = getImageURL({ documentId: valuesToLoad.id, accessToken })
            if (imageLink) {
              const imageParser = `<img src="${imageLink}"  alt="agency_logo" width="${matchedItemJson.width}px" id=${valuesToLoad.id} />`
              valuesToLoad = imageParser || ''
            }
          } else {
            valuesToLoad = ''
          }
        }
        textContent = textContent.replace(matchedItem, valuesToLoad)
      } catch {}
    })
  }

  return textContent
}

const germanSalutationMap = {
  MISTER: 'LOGIN.SIGNUP.SALUTATION_FIELD.OPTION.MISTER',
  MISSIS: 'LOGIN.SIGNUP.SALUTATION_FIELD.OPTION.MISSIS',
  COMPANY: 'LOGIN.SIGNUP.SALUTATION_FIELD.OPTION.COMPANY',
  UNKNOWN: 'LOGIN.SIGNUP.SALUTATION_FIELD.OPTION.UNKNOWN',
} as const

export async function getProcessedAgreementTemplate({
  userProfile,
  agent,
  accessToken,
}: {
  userProfile?: UserProfile
  agent?: Expert
  accessToken?: string
}) {
  const t = await getTranslations({ locale: 'de' })

  if (agent && userProfile && accessToken) {
    const args: ReplaceAcceptanceRequestWithValuesArgs = {
      agencies: {
        name: agent.agencyName,
        legalForm: agent.agencyLegalForm,
        street: agent.agencyStreet,
        streetNumber: agent.agencyStreetNumber,
        zip: agent.agencyZip,
        city: agent.agencyCity,
        imgId: { tag: 'img', id: agent.agencyImgId, isProtected: true },
        phone: agent.phone || '',
      },
      texts: {
        today: (await formatDateShort(new Date())) as string,
        appealing_salutation: `${t('GLOBAL.DEAR')} ${userProfile.salutation}`,
      },
      clients: {
        salutation: userProfile.salutation
          ? t(germanSalutationMap[userProfile.salutation as keyof typeof germanSalutationMap])
          : '',
        firstName: userProfile.firstName!,
        lastName: userProfile.lastName!,
        street: userProfile.street!,
        streetNum: userProfile.streetNum!,
        zip: userProfile.zip!,
        city: userProfile.city!,
        email: userProfile.email || '',
        phone: userProfile.phone || '',
        birthdate: userProfile.birthdate ? (await formatDateShort(new Date(userProfile.birthdate))) || '' : '',
      },
    }

    return replaceAcceptanceRequestWithValues(agent.acceptance_request, args, accessToken)
  }
}
