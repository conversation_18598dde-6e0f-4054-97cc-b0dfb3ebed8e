import { clsx, type ClassValue } from 'clsx'
import { twMerge } from 'tailwind-merge'

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

/**
 * Format currency amount
 * Helper for displaying monetary values
 */
export function formatCurrency(amount: number, currency: string = 'EUR'): string {
  if (amount === undefined || amount === null || isNaN(amount)) {
    return ''
  }

  return new Intl.NumberFormat('de-DE', {
    style: 'currency',
    currency: currency,
  }).format(amount)
}

export function safeParseDate(dateString: string | undefined | null): Date | undefined {
  // Handle null, undefined, empty, or whitespace-only strings
  if (!dateString || typeof dateString !== 'string' || dateString.trim() === '') {
    return undefined
  }

  // Normalize the input by trimming whitespace
  const trimmedDate = dateString.trim()

  // Check for common database placeholder/invalid dates
  const invalidDatePatterns = [
    // MySQL default invalid dates
    '0000-00-00',
    '0000-00-00 00:00:00',

    // Common epoch/placeholder dates that are often used as "invalid" markers
    '1900-01-01',
    '1900-01-01 00:00:00',
    '1970-01-01',
    '1970-01-01 00:00:00',
    '1899-12-30', // Excel/OLE Automation epoch
    '1899-12-30 00:00:00',

    // Common "invalid" text values
    'null',
    'undefined',
    'invalid',
    'n/a',
    'na',
    'tbd',
    'pending',
    'unknown',
  ]

  if (invalidDatePatterns.includes(trimmedDate.toLowerCase())) {
    return undefined
  }

  // Check for obviously malformed patterns
  const malformedPatterns = [
    /^--/, // Starts with double dash
    /^\d{4}$/, // Just a year
    /^\d{4}-\d{2}$/, // Just year-month
    /^\d{1,2}[-/]\d{1,2}[-/]\d{2}$/, // Two-digit year (ambiguous)
  ]

  if (malformedPatterns.some((pattern) => pattern.test(trimmedDate))) {
    return undefined
  }

  // Attempt to parse the date
  const date = new Date(trimmedDate)

  // Check if the date is invalid (NaN)
  if (isNaN(date.getTime())) {
    return undefined
  }

  // Additional validation for edge cases
  const year = date.getFullYear()

  // Reject dates that are too old or too far in the future
  // (likely indicates data corruption or placeholder values)
  if (year < 1000 || year > 3000) {
    return undefined
  }

  // Validate that the parsed date matches the input for common formats
  // This catches cases like "2023-02-30" which JavaScript converts to "2023-03-02"
  if (trimmedDate.match(/^\d{4}-\d{2}-\d{2}$/)) {
    const [inputYear, inputMonth, inputDay] = trimmedDate.split('-').map(Number)
    if (date.getFullYear() !== inputYear || date.getMonth() + 1 !== inputMonth || date.getDate() !== inputDay) {
      return undefined
    }
  }

  // If we get here, the date appears to be valid
  return date
}
