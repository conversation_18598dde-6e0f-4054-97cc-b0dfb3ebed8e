import { getAppConfig } from '@/app-config'
import { AuthSession } from '@/modules/auth/types/auth-types'

// Define a custom error class for API errors
export class ApiError extends Error {
  public details?: Record<string, string[]>

  constructor(message: string, details?: Record<string, string[]>) {
    super(message)
    this.name = 'ApiError'
    this.details = details
  }
}

// Helper function to create detailed error messages
function createDetailedError(message: string, details?: Record<string, string[]>): ApiError {
  if (details) {
    // Create a more user-friendly error message with validation details
    const validationErrors = Object.entries(details)
      .map(([field, errors]) => `${field}: ${errors?.join(', ')}`)
      .join('; ')

    return new ApiError(`${message}. Validation errors: ${validationErrors}`, details)
  }

  return new ApiError(message, details)
}

// Define a type for API responses
export interface ApiResponse<T = unknown> {
  success: boolean
  data?: T
  error?: string
  details?: Record<string, string[]>
}

// Helper function to format API responses
export async function formatResponse<T>(
  data: T | null = null,
  error?: string,
  details?: Record<string, string[]>
): Promise<ApiResponse<T>> {
  if (error) {
    return {
      success: false,
      error,
      details,
    }
  }

  return {
    success: true,
    data: data as T,
  }
}

// Helper function to handle API errors
export async function handleApiError(error: unknown): Promise<ApiResponse> {
  console.error('%c ❌ [API Error]', 'color: #e74c3c; font-weight: bold;', error)

  let errorMessage: string
  let errorDetails: Record<string, string[]> | undefined

  if (error instanceof Error) {
    errorMessage = error.message

    // Check if it's our custom ApiError with details
    if (error instanceof ApiError && error.details) {
      errorDetails = error.details
    }

    // Log stack trace in development
    if (process.env.NODE_ENV === 'development' && error.stack) {
      console.error('%c 📚 [Stack Trace]', 'color: #e74c3c;', error.stack)
    }
  } else if (typeof error === 'string') {
    errorMessage = error
  } else {
    errorMessage = 'An unexpected error occurred'
  }

  return formatResponse(null, errorMessage, errorDetails)
}

// Helper function to create a full URL
export async function createApiUrl(path: string, newApi: boolean): Promise<string> {
  let url = ''
  if (path.startsWith('http')) {
    url = path
  } else {
    const config = getAppConfig()
    url = newApi ? `${config.apiBaseUrlNew}/b2c${path}` : `${config.apiBaseUrl}/b2c${path}`
  }

  return url
}

// Helper function to escape shell arguments for cURL command
function escapeShellArg(arg: string): string {
  // Replace single quotes with '\'' and wrap in single quotes
  return `'${arg.replace(/'/g, "'\\''")}'`
}

function logCurlCommand(url: string, method: string, headers: Record<string, string>, body?: string): void {
  const curlParts: string[] = ['curl -v']

  // Add method
  curlParts.push(`-X ${method.toUpperCase()}`)

  // Add URL
  curlParts.push(escapeShellArg(url))

  // Add headers
  Object.entries(headers).forEach(([key, value]) => {
    curlParts.push(`-H ${escapeShellArg(`${key}: ${value}`)}`)
  })

  // Add body
  if (body && ['POST', 'PUT', 'PATCH'].includes(method.toUpperCase())) {
    curlParts.push(`--data-raw ${escapeShellArg(body)}`)
  }

  const command = curlParts.join(' \\\n  ')

  // Log to a collapsed group for cleanliness
  console.groupCollapsed('%c📋 cURL Command', 'color: #007bff; font-weight: bold;')
  console.log(
    `%c${command}`,
    'background: #2d2d2d; color: #fcfcfc; font-family: monospace; padding: 10px; border-radius: 4px; display: block; white-space: pre-wrap;'
  )
  console.groupEnd()
}

/**
 * Main API fetch function with optional cURL logging
 *
 * @param path - API endpoint path
 * @param options - Fetch options with additional properties:
 *   - newApi: boolean - Use new API base URL
 *   - logCurl: boolean - Log equivalent cURL command to console for debugging
 * @param session - Authentication session
 * @returns Promise<ApiResponse<T>>
 *
 */
// eslint-disable-next-line @typescript-eslint/no-explicit-any
export async function fetchApi<T = any>(
  path: string,
  options: RequestInit & { newApi?: boolean; logCurl?: boolean } = {},
  session: AuthSession | null = null
): Promise<ApiResponse<T>> {
  try {
    const url = await createApiUrl(path, options.newApi || false)

    const startTime = performance.now()

    // Prepare headers
    const headers: Record<string, string> = {
      ...((options.headers as Record<string, string>) || {}),
      ...(session?.accessToken ? { Authorization: `Bearer ${session.accessToken}` } : {}),
    }

    // Only set Content-Type if not already provided
    if (!headers['Content-Type']) {
      headers['Content-Type'] = 'application/json'
    }

    // Log cURL command if requested
    if (options.logCurl) {
      const method = options.method || 'GET'
      const bodyString = options.body ? String(options.body) : undefined
      logCurlCommand(url, method, headers, bodyString)
    }

    const res = await fetch(url, {
      ...options,
      headers,
    })

    const endTime = performance.now()
    const responseTime = endTime - startTime

    if (!res.ok) {
      let errorMessage: string
      let errorDetails: Record<string, string[]> | undefined

      try {
        const errorData = await res.json()

        // Extract message
        errorMessage = errorData?.message || errorData?.error || `HTTP ${res.status}`

        // Extract validation details if present
        if (errorData?.detail && typeof errorData.detail === 'object') {
          errorDetails = errorData.detail

          // Log detailed validation errors for debugging
          console.error('%c 🔍 [Validation Details]', 'color: #e74c3c; font-weight: bold;', errorDetails)
        }
      } catch {
        errorMessage = `HTTP ${res.status}`
      }

      console.error(
        '%c ❌ [fetchApi] %s failed with status %c%s%c: %o (in %c%s%c ms)',
        'color: #e74c3c; font-weight: bold;',
        path,
        'color: #e74c3c; font-weight: bold;',
        res.status,
        'color: #e74c3c;',
        errorMessage,
        'color: #e74c3c; font-weight: bold;',
        responseTime.toFixed(2),
        'color: #e74c3c;'
      )
      console.error('%c 📚 [Stack Trace]', 'color: #e74c3c;', new Error().stack)
      // Log cURL command for failed requests
      const method = options.method || 'GET'
      const bodyString = options.body ? String(options.body) : undefined
      logCurlCommand(url, method, headers, bodyString)

      return formatResponse<T>(null as T, errorMessage, errorDetails)
    }

    // Handle empty responses (like 204 No Content)
    let data: T | null = null

    // Check if response has content to parse
    const contentLength = res.headers.get('content-length')
    const contentType = res.headers.get('content-type')

    if (res.status !== 204 && contentLength !== '0' && contentType?.includes('application/json')) {
      try {
        data = await res.json()
      } catch (jsonError: unknown) {
        console.warn(
          '%c ⚠️ [fetchApi] Failed to parse JSON response for %s, treating as empty response',
          'color: #f39c12; font-weight: bold;',
          path
        )
        console.error(jsonError)
        data = null
      }
    }

    return formatResponse<T>(data as T)
  } catch (error) {
    return handleApiError(error) as Promise<ApiResponse<T>>
  }
}

// Export the helper function for backward compatibility
export { createDetailedError }

/**
 * Simplified API function that throws ApiError automatically
 * This eliminates the need for manual error checking in action files
 *
 * @param path - API endpoint path
 * @param options - Fetch options with additional properties:
 *   - newApi: boolean - Use new API base URL
 *   - logCurl: boolean - Log equivalent cURL command to console for debugging
 * @param session - Authentication session
 * @returns Promise<T> - Returns data directly or throws ApiError
 * @throws {ApiError} When the API request fails
 *
 * @example
 * // Will throw ApiError if request fails
 * const user = await fetchApiOrThrow('/users/123', { logCurl: true })
 */
// eslint-disable-next-line @typescript-eslint/no-explicit-any
export async function fetchApiOrThrow<T = any>(
  path: string,
  options: RequestInit & { newApi?: boolean; logCurl?: boolean } = {},
  session: AuthSession | null = null
): Promise<T> {
  const response = await fetchApi<T>(path, options, session)

  if (response.error) {
    throw createDetailedError(response.error, response.details)
  }

  return response.data as T
}
