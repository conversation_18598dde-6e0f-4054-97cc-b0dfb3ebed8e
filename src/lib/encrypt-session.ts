'use server'

import { getAppConfig } from '@/app-config'
import { AuthSession } from '@/modules/auth/types/auth-types'
import { EncryptJWT, jwtDecrypt } from 'jose'
import { cookies as getCookies } from 'next/headers'

/**
 * Generate a 256-bit (32 bytes) key from any string input
 * Uses Web Crypto API SHA-256 hash to ensure consistent key length regardless of input
 * Compatible with Edge Runtime
 */
async function generateSecretKey(input: string): Promise<Uint8Array> {
  const encoder = new TextEncoder()
  const data = encoder.encode(input)
  const hashBuffer = await crypto.subtle.digest('SHA-256', data)
  return new Uint8Array(hashBuffer)
}

/**
 * Get the secret key for encryption/decryption
 * Generates a consistent 256-bit key from AUTH_SECRET environment variable
 */
async function getSecret(): Promise<Uint8Array> {
  if (!process.env.AUTH_SECRET) {
    throw new Error('AUTH_SECRET environment variable is not defined')
  }
  return generateSecret<PERSON>ey(process.env.AUTH_SECRET)
}

export async function encryptSession({ accessToken, refreshToken, expiresAt, user }: AuthSession) {
  const secret = await getSecret()
  const jwt = await new EncryptJWT({ accessToken, refreshToken, expiresAt, user })
    .setProtectedHeader({ alg: 'dir', enc: 'A128CBC-HS256' })
    .setIssuedAt()
    .setExpirationTime('7d')
    .encrypt(secret)

  return jwt
}

export async function decryptSession(token: string): Promise<AuthSession | null> {
  if (!token) return null

  try {
    const secret = await getSecret()
    const { payload } = await jwtDecrypt(token, secret)
    return payload as unknown as AuthSession
  } catch (err) {
    console.error('Failed to decrypt session:', err)
    return null
  }
}

export async function decryptSessionWithoutCatch(token: string): Promise<AuthSession | null> {
  if (!token) return null

  const secret = await getSecret()
  const { payload } = await jwtDecrypt(token, secret)
  return payload as unknown as AuthSession
}

export async function setEncryptedSessionCookie({
  accessToken,
  refreshToken,
  expiresIn = 0,
  user,
  expiresAt,
}: AuthSession) {
  const encrypted = await encryptSession({ accessToken, refreshToken, expiresAt, expiresIn, user })

  const appConfig = getAppConfig()
  const cookies = await getCookies()
  cookies.set('session', encrypted, {
    httpOnly: appConfig.environment === 'production',
    secure: appConfig.environment === 'production',
    sameSite: 'lax',
    maxAge: 60 * 60 * 24 * 7, // 7 days in seconds
    path: '/',
  })
}
