import { getSession } from '@/modules/auth/actions/session'
import { AuthSession } from '@/modules/auth/types/auth-types'

import { ApiResponse, fetchApi, handleApiError } from './fetch-api'

export async function fetchAuthenticatedApi<T extends object | null>(
  path: string,
  options: RequestInit & { newApi?: boolean; logCurl?: boolean } = {}
): Promise<ApiResponse<T & { session: AuthSession }>> {
  try {
    const session = await getSession()

    if (!session) {
      throw new Error('No valid session available - authentication required')
    }

    const response = await fetchApi<T>(path, options, session)

    if (response.error) {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      return response as ApiResponse<any>
    }

    const combinedData = {
      ...(response.data || {}),
      session,
    }

    return {
      ...response,
      data: combinedData as T & { session: AuthSession },
    }
  } catch (error) {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    return handleApiError(error) as Promise<ApiResponse<any>>
  }
}
