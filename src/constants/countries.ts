import timezoneToCountryData from '../data/timezone-to-country.json'
import countriesData from '../data/countries.json'

/**
 * Maps timezone identifiers to ISO 3166-1 alpha-2 country codes
 * Organized alphabetically by timezone for better maintainability
 * Data is loaded from JSON file to optimize bundle size
 */
const TIMEZONE_TO_COUNTRY_MAP = timezoneToCountryData as Record<string, string>

/**
 * Country data interface based on the original structure
 */
export interface CountryData {
  code: string
  status: string
  de: string
  en: string
  fr: string
  nationality_de: string
  nationality_en: string
  nationality_fr: string
  currency: string
  currency_symbol: string
  currency_name_en: string
  currency_name_de: string
  currency_name_fr: string
  phone_prefix: string
  flag?: string
  dial: string
}

/**
 * Countries data array loaded from JSON file to optimize bundle size
 * Contains comprehensive country information including names in multiple languages,
 * currency details, phone prefixes, and flag emojis
 */
export const COUNTRIES_DATA = countriesData as CountryData[]

/**
 * Get country code from timezone
 * @param timezone - IANA timezone identifier (e.g., 'Europe/Belgrade')
 * @param fallback - Default country code if timezone not found (default: 'DE')
 * @returns ISO 3166-1 alpha-2 country code
 */
export function getCountryFromTimezone(timezone?: string, fallback: string = 'DE'): string {
  if (!timezone) {
    try {
      timezone = Intl.DateTimeFormat().resolvedOptions().timeZone
    } catch {
      return fallback
    }
  }

  return TIMEZONE_TO_COUNTRY_MAP[timezone] || fallback
}

/**
 * Get current user's country code based on their timezone
 * @param fallback - Default country code if detection fails (default: 'DE')
 * @returns ISO 3166-1 alpha-2 country code
 */
export function getCurrentCountryCode(fallback: string = 'DE'): string {
  return getCountryFromTimezone(undefined, fallback)
}

/**
 * Check if a timezone is supported
 * @param timezone - IANA timezone identifier
 * @returns boolean indicating if timezone is in the mapping
 */
export function isTimezoneSupported(timezone: string): boolean {
  return timezone in TIMEZONE_TO_COUNTRY_MAP
}

/**
 * Get all supported timezones
 * @returns Array of all supported timezone identifiers
 */
export function getSupportedTimezones(): string[] {
  return Object.keys(TIMEZONE_TO_COUNTRY_MAP)
}

/**
 * Get all countries that have timezone mappings
 * @returns Array of unique country codes
 */
export function getSupportedCountries(): string[] {
  return [...new Set(Object.values(TIMEZONE_TO_COUNTRY_MAP))].sort()
}
