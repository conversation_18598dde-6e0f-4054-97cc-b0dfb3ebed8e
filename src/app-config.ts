export const getAppConfig = () => ({
  environment: process.env.APP_ENV || process.env.NODE_ENV || 'development',
  isDevelopment: process.env.NODE_ENV === 'development',
  isProduction: process.env.NODE_ENV === 'production',
  isTest: process.env.NODE_ENV === 'test',
  isDebug: process.env.NEXT_DEBUG === 'true' || process.env.NODE_ENV === 'development',

  apiBaseUrl: process.env.API_BASE_URL || '',
  apiBaseUrlNew: process.env.API_BASE_URL_NEW || '',

  appUrl: process.env.APP_URL || '',

  ssoUrl: process.env.SSO_URL || '',
  ssoClientId: process.env.SSO_CLIENT_ID || '',
  homeUrl: process.env.HOME_URL || '',

  customerBenefitsUrl: process.env.CUSTOMER_BENEFITS_URL || '',
  termsOfUseFileUrl:
    process.env.TERMS_OF_USE_FILE_URL || 'https://mobilversichert.de/wp-content/uploads/Nutzungsbedingungen_App.pdf',
  impressumFileUrl: process.env.IMPRESSUM_FILE_URL || 'https://mobilversichert.de/wp-content/uploads/Impressum_App.pdf',
  dataProtectionFileUrl:
    process.env.DATA_PROTECTION_FILE_URL ||
    'https://mobilversichert.de/wp-content/uploads/Datenschutzerklärung_App.pdf',
  mvpUrl: process.env.MVP_URL || '',
})
