import { getRequestConfig } from 'next-intl/server'

import { fetchTranslations, getUserLocale } from './i18n-actions'

export default getRequestConfig(async () => {
  // Provide a static locale, fetch a user setting,
  // read from `cookies()`, `headers()`, etc.
  const locale = await getUserLocale()

  const messages = await fetchTranslations(locale)

  return {
    locale,
    messages: messages ?? undefined,
    timeZone: 'Europe/Berlin',
  }
})
