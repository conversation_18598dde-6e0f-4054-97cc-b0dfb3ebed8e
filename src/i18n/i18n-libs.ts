import { set } from 'lodash'
import * as RPNInput from 'react-phone-number-input'

export const USER_LOCALE_COOKIE_NAME = 'user_locale'

export type Locale = (typeof locales)[number]

export const locales = ['en', 'de'] as const

export const localesOptions: { label: string; value: Locale; country: RPNInput.Country }[] = [
  {
    label: 'English',
    value: 'en',
    country: 'GB',
  },
  {
    label: 'German',
    value: 'de',
    country: 'DE',
  },
]
export const defaultLocale: Locale = 'en'

export const transformTranslations = (translations: Record<string, string>) => {
  const transformed = Object.entries(translations).reduce((acc, [key, value]) => set(acc, key, value), {})
  return transformed
}
