'use client'

import { AlertTriangle, RefreshCw } from 'lucide-react'
import { useTranslations } from 'next-intl'

import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'

interface ErrorBoundaryProps {
  error: string
  reset?: boolean
  title?: string
  description?: string
}

export function ErrorBoundary({ error, reset = true, title, description }: ErrorBoundaryProps) {
  const t = useTranslations()

  const handleReset = () => {
    window.location.reload()
  }

  return (
    <div className="flex items-center justify-center min-h-[400px] p-4">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-red-100">
            <AlertTriangle className="h-6 w-6 text-red-600" />
          </div>
          <CardTitle className="text-lg font-semibold text-gray-900">
            {title || t('COMMON.ERROR.TITLE', { defaultValue: 'Something went wrong' })}
          </CardTitle>
          <CardDescription>
            {description ||
              t('COMMON.ERROR.DESCRIPTION', {
                defaultValue: 'An error occurred while loading this page.',
              })}
          </CardDescription>
        </CardHeader>
        <CardContent className="text-center space-y-4">
          <p className="text-sm text-gray-600 bg-gray-50 p-3 rounded-md font-mono">{error}</p>
          {reset && (
            <Button onClick={handleReset} variant="outline" className="w-full">
              <RefreshCw className="mr-2 h-4 w-4" />
              {t('COMMON.ERROR.RETRY', { defaultValue: 'Try again' })}
            </Button>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
