import React from 'react'

import Image from 'next/image'

interface ImagePreviewProps {
  src: string
  alt?: string
  className?: string
}

export function ImagePreview({ src, alt = 'Preview image', className }: ImagePreviewProps) {
  return (
    <div className={className || 'relative w-full h-80 flex items-center justify-center'}>
      <Image
        src={src}
        alt={alt}
        fill
        className="object-contain rounded-lg bg-gray-50"
        sizes="(max-width: 640px) 100vw, 640px"
        priority
      />
    </div>
  )
}
