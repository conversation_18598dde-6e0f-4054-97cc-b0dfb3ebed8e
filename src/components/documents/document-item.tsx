'use client'

import { FileText } from 'lucide-react'
import Image from 'next/image'

import { DownloadFileButton } from '@/components/download-file-button'

import { DocumentDeleteModal } from './document-delete-modal'
import { DocumentPreviewModal } from './document-preview-modal'
import { BaseDocument } from './types'

interface DocumentItemProps<T extends BaseDocument> {
  document: T
  accessToken: string
  onDelete?: (document: T) => void
  isDeleting?: boolean
}

export function DocumentItem<T extends BaseDocument>({
  document,
  accessToken,
  onDelete,
  isDeleting = false,
}: DocumentItemProps<T>) {
  // Determine file type based on document extension
  const isImg = document.extension && ['jpg', 'jpeg', 'png', 'gif', 'webp'].includes(document.extension.toLowerCase())

  const formatDate = (dateString?: string) => {
    if (!dateString) return ''
    return new Date(dateString).toLocaleDateString('de-DE')
  }

  return (
    <div className="flex items-center justify-between p-4 bg-white rounded-lg border border-gray-200 shadow-sm hover:shadow-md transition-shadow">
      {/* Left side - Icon and document info */}
      <div className="flex items-center gap-4 flex-1 min-w-0">
        {/* File type icon */}
        <div className="flex-shrink-0 w-12 h-12 bg-gray-50 rounded-lg flex items-center justify-center">
          {isImg ? (
            <Image src={document.documentUrl} alt={document.name} width={48} height={48} className="rounded-lg" />
          ) : (
            <FileText className="w-6 h-6 text-[#6B7280]" />
          )}
        </div>

        {/* Document details */}
        <div className="flex-1 min-w-0">
          <h3 className="text-sm font-medium text-[#111827] truncate">{document.name || document.originalName}</h3>
          <p className="text-xs text-[#6B7280] mt-1">{formatDate(document.modified || document.created)}</p>
        </div>
      </div>

      {/* Right side - Actions */}
      <div className="flex-shrink-0 ml-4 flex items-center gap-2">
        <DownloadFileButton documentId={document.id.toString()} accessToken={accessToken} />
        <DocumentPreviewModal document={document} />
        {/* Optional delete button */}
        {onDelete && document.deleteable && (
          <DocumentDeleteModal document={document} onDelete={onDelete} isDeleting={isDeleting} />
        )}
      </div>
    </div>
  )
}
