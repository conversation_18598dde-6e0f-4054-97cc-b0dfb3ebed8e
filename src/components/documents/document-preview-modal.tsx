'use client'

import { EyeIcon } from 'lucide-react'
import { useTranslations } from 'next-intl'

import { getImageURL } from '@/lib/actions/documents/get-image'
import { Button } from '@/components/ui/button'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'

import { FallbackPreview } from './fallback-preview'
import { ImagePreview } from './image-preview'
import { PDFPreview } from './pdf-preview-ssr-safe'
import { BaseDocument } from './types'

interface DocumentPreviewModalProps<T extends BaseDocument> {
  document: T
  accessToken?: string // Za slike/avatar
}

export function DocumentPreviewModal<T extends BaseDocument>({ document, accessToken }: DocumentPreviewModalProps<T>) {
  const t = useTranslations()

  // Normalize ekstenziju
  const ext = document.extension?.toLowerCase() || ''

  // Centralizovana preview logika na osnovu ekstenzije
  let preview: React.ReactNode
  switch (ext) {
    case 'jpg':
    case 'jpeg':
    case 'png':
    case 'gif':
    case 'webp':
      preview = <ImagePreview src={document.documentUrl} alt={document.name} />
      break
    case 'pdf':
      preview = <PDFPreview fileUrl={document.documentUrl} />
      break
    // Dodaj još ekstenzija po potrebi (npr. docx, xlsx, txt, ...)
    default:
      preview = <FallbackPreview name={document.name} />
  }

  return (
    <Dialog>
      <DialogTrigger asChild>
        <Button variant="ghost" size="icon" className="text-blue-500 hover:text-blue-700 hover:bg-blue-50">
          <EyeIcon className="w-4 h-4" />
          <span className="sr-only">Preview document</span>
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-3xl">
        <DialogHeader>
          <DialogTitle className="text-xl font-semibold text-gray-900 mb-2">{document.name}</DialogTitle>
        </DialogHeader>

        <div className="flex flex-col items-center space-y-4">
          {preview}
          <div className="flex flex-row gap-3 items-center justify-center pt-4">
            {document.id && accessToken && (
              <a
                href={getImageURL({ documentId: document.id.toString(), accessToken, download: true })}
                target="_blank"
                rel="noopener noreferrer"
              >
                <Button className="px-8 py-2" variant="destructive">
                  {t('Download')}
                </Button>
              </a>
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
