'use client'

import { Trash2Icon } from 'lucide-react'
import { useTranslations } from 'next-intl'
import Image from 'next/image'
import { useIsMounted } from 'usehooks-ts'

import { Button } from '@/components/ui/button'
import { Dialog, DialogClose, DialogContent, DialogTitle, DialogTrigger } from '@/components/ui/dialog'

import { type BaseDocument } from './types'

interface DocumentDeleteModalProps<T extends BaseDocument> {
  document: T
  onDelete?: (document: T) => void
  isDeleting?: boolean
  triggerButton?: React.ReactNode
  // Customization options
  title?: string
  message?: string
  confirmText?: string
  cancelText?: string
}

/**
 * Reusable document delete modal component
 *
 * Example usage:
 *
 * // Basic usage with default trigger button
 * <DocumentDeleteModal
 *   document={document}
 *   onDelete={handleDelete}
 *   isDeleting={isDeleting}
 * />
 *
 * // Custom trigger button
 * <DocumentDeleteModal
 *   document={document}
 *   onDelete={handleDelete}
 *   triggerButton={
 *     <Button variant="destructive" size="sm">
 *       Remove Document
 *     </Button>
 *   }
 * />
 *
 * // Custom messages
 * <DocumentDeleteModal
 *   document={document}
 *   onDelete={handleDelete}
 *   title="Delete Contract Document?"
 *   message="This document will be permanently removed from the contract."
 *   confirmText="Delete Document"
 *   cancelText="Keep Document"
 * />
 */
export function DocumentDeleteModal<T extends BaseDocument>({
  document,
  onDelete,
  isDeleting = false,
  triggerButton,
  title,
  message,
  confirmText,
  cancelText,
}: DocumentDeleteModalProps<T>) {
  const t = useTranslations()
  const isMounted = useIsMounted()

  const handleDelete = () => {
    onDelete?.(document)
  }

  if (!isMounted()) return null

  // Default trigger button if none provided
  const defaultTriggerButton = (
    <Button variant="ghost" size="icon" className="text-red-500 hover:text-red-700 hover:bg-red-50">
      <Trash2Icon className="w-4 h-4" />
      <span className="sr-only">Delete document</span>
    </Button>
  )

  // Default texts with translations
  const modalTitle =
    title ||
    t('DOCUMENTS.DELETE.TITLE', {
      defaultValue: 'Are you sure you want to delete this file?',
    })

  const modalMessage =
    message ||
    t('DOCUMENTS.DELETE.MESSAGE', {
      defaultValue: 'will be permanently removed',
      fileName: document.name || document.originalName || 'this file',
    })

  const confirmButtonText =
    confirmText ||
    t('DOCUMENTS.DELETE.CONFIRM', {
      defaultValue: 'Delete',
    })

  const cancelButtonText =
    cancelText ||
    t('DOCUMENTS.DELETE.CANCEL', {
      defaultValue: 'Cancel',
    })

  return (
    <Dialog>
      <DialogTrigger asChild>{triggerButton || defaultTriggerButton}</DialogTrigger>
      <DialogContent className="sm:max-w-md">
        <div className="text-center space-y-4">
          {/* Warning Icon */}
          <div className="flex justify-center">
            <div className="relative size-40">
              <Image src="/images/error.png" alt="Delete warning" fill className="object-contain" />
            </div>
          </div>

          <DialogTitle className="text-xl font-semibold text-gray-900">{modalTitle}</DialogTitle>

          <div className="text-center space-y-2">
            <div className="text-gray-600">
              <span className="font-medium">{document.name || document.originalName || 'This file'}</span>{' '}
              {modalMessage}
            </div>
          </div>
        </div>

        <div className="flex flex-row gap-3 items-center justify-center pt-6">
          <DialogClose asChild>
            <Button
              type="button"
              variant="outline"
              className="px-8 py-2 border-gray-300 text-gray-700 hover:bg-gray-50"
              disabled={isDeleting}
            >
              {cancelButtonText}
            </Button>
          </DialogClose>

          <Button onClick={handleDelete} className="px-8 py-2" disabled={isDeleting} variant="destructive">
            {isDeleting ? (
              <>
                <span className="animate-spin mr-2">⏳</span>
                {t('COMMON.DELETING', { defaultValue: 'Deleting...' })}
              </>
            ) : (
              confirmButtonText
            )}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  )
}
