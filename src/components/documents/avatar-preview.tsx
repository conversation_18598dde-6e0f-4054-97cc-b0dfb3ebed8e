import React from 'react'

import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'

interface AvatarPreviewProps {
  src?: string
  initials?: string
  className?: string
}

export function AvatarPreview({ src, initials = 'AM', className }: AvatarPreviewProps) {
  return (
    <Avatar className={className || 'w-32 h-32 border-2 border-gray-200 shadow-lg'}>
      <AvatarImage src={src} alt="Avatar" className="object-cover" />
      <AvatarFallback className="text-2xl bg-blue-600 text-white">{initials}</AvatarFallback>
    </Avatar>
  )
}
