interface DocumentListSkeletonProps {
  count?: number
  className?: string
}

export function DocumentListSkeleton({ count = 3, className }: DocumentListSkeletonProps) {
  return (
    <div className={`flex flex-col gap-4 ${className || ''}`}>
      {Array.from({ length: count }).map((_, index) => (
        <div
          key={index}
          className="flex items-center justify-between p-4 bg-white rounded-lg border border-gray-200 animate-pulse"
        >
          <div className="flex items-center gap-4 flex-1">
            <div className="w-12 h-12 bg-gray-200 rounded-lg" />
            <div className="flex-1">
              <div className="h-4 bg-gray-200 rounded w-3/4 mb-2" />
              <div className="h-3 bg-gray-200 rounded w-1/2" />
            </div>
          </div>
          <div className="w-8 h-8 bg-gray-200 rounded" />
        </div>
      ))}
    </div>
  )
}
