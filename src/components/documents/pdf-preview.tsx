'use client'

import { useState } from 'react'

import { Document, Page, pdfjs } from 'react-pdf'

import { cn } from '@/lib/utils'

import 'react-pdf/dist/Page/AnnotationLayer.css'
import 'react-pdf/dist/Page/TextLayer.css'

import { Button } from '../ui/button'

interface PDFPreviewProps {
  fileUrl: string
  className?: string
}

pdfjs.GlobalWorkerOptions.workerSrc = `//unpkg.com/pdfjs-dist@${pdfjs.version}/build/pdf.worker.min.mjs`

//TODO Check ratio of the document

export function PDFPreview({ fileUrl, className }: PDFPreviewProps) {
  const [pageNumber, setPageNumber] = useState<number>(1)
  const [numPages, setNumPages] = useState<number>()

  const onDocumentLoadSuccess = ({ numPages }: { numPages: number }): void => {
    setNumPages(numPages)
  }

  return (
    <div className={cn('w-full  rounded-lg overflow-hidden', className)}>
      <Document
        file={fileUrl}
        loading={<div>Loading PDF...</div>}
        error={<div>Failed to load PDF</div>}
        onLoadSuccess={onDocumentLoadSuccess}
      >
        <Page pageNumber={pageNumber} width={600} />
      </Document>

      <div className="flex flex-col gap-3 items-center justify-between pt-4">
        {numPages && numPages > 1 && (
          <>
            <div className="flex flex-row gap-3 items-center justify-center">
              <Button variant="link" onClick={() => setPageNumber(pageNumber - 1)} disabled={pageNumber === 1}>
                Previous
              </Button>
              <Button variant="link" onClick={() => setPageNumber(pageNumber + 1)} disabled={pageNumber === numPages}>
                Next
              </Button>
            </div>
            <p className="text-sm text-gray-500">
              Page {pageNumber} of {numPages}
            </p>
          </>
        )}
      </div>
    </div>
  )
}
