import { DocumentUploadType } from '@/lib/actions/documents/documents-types'

/**
 * Generic document interface that can be extended for different document types
 *
 * Example usage:
 *
 * // For damage documents:
 * interface DamageDocument extends BaseDocument {
 *   damageId: string
 *   category: string
 * }
 *
 * // For profile documents:
 * interface ProfileDocument extends BaseDocument {
 *   userId: string
 *   documentType: 'passport' | 'id' | 'license'
 * }
 */
export interface BaseDocument {
  id: number | string
  name: string
  originalName?: string
  extension?: string
  modified?: string
  created?: string
  deleteable?: boolean | number
  // Optional fields that might exist in specific document types
  fileName?: string | null
  size?: number
  type?: DocumentUploadType
  status?: string
  documentUrl: string
}

/**
 * Configuration for empty state display
 */
export interface EmptyStateConfig {
  title: string
  message?: string
  pageTitle?: string
  actions?: React.ReactNode
}

/**
 * Props for DocumentList component
 */
export interface DocumentListProps<T extends BaseDocument> {
  documents: T[]
  accessToken: string
  isLoading?: boolean
  error?: string
  emptyStateConfig?: EmptyStateConfig
  onDelete?: (document: T) => void
  className?: string
}
