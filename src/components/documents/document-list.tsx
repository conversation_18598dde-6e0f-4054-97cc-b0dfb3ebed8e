'use client'

import { useTranslations } from 'next-intl'

import { EmptyState } from '@/components/empty-state'
import { ErrorBoundary } from '@/components/error-boundary'

import { DocumentItem } from './document-item'
import { DocumentListSkeleton } from './document-list-skeleton'
import { BaseDocument, type DocumentListProps } from './types'

/**
 * Reusable DocumentList component
 *
 * @param documents - Array of documents to display
 * @param accessToken - User's access token
 * @param isLoading - Whether the documents are still loading
 * @param error - Error message if documents fail to load
 * @param emptyStateConfig - Configuration for empty state
 * @param onDelete - Callback function to handle document deletion
 * @param className - Additional CSS classes for styling
 */

export function DocumentList<T extends BaseDocument>({
  documents,
  accessToken,
  isLoading = false,
  error,
  emptyStateConfig,
  onDelete,
  className,
}: DocumentListProps<T>) {
  const t = useTranslations()

  // Handle error state
  if (error) {
    return (
      <ErrorBoundary
        error={error}
        title={t('COMMON.ERROR.TITLE', { defaultValue: 'Failed to load documents' })}
        description={t('COMMON.ERROR.DESCRIPTION', {
          defaultValue: 'There was an error loading the documents. Please try again.',
        })}
      />
    )
  }

  // Handle loading state
  if (isLoading) {
    return <DocumentListSkeleton className={className} />
  }

  // Handle empty state
  if (!documents || documents.length === 0) {
    const defaultEmptyConfig = {
      title:
        emptyStateConfig?.title ||
        t('DAMAGES.DETAILS.DOCUMENTS_TAB.EMPTY_TEXT', { defaultValue: 'No documents found' }),
      message: emptyStateConfig?.message,
      pageTitle: '',
    }

    const config = { ...defaultEmptyConfig, ...emptyStateConfig }

    return (
      <EmptyState pageTitle={config.pageTitle} title={config.title} message={config.message} actions={config.actions} />
    )
  }

  // Render documents list
  return (
    <div className={`flex flex-col gap-4 ${className || ''}`}>
      {documents.map((document) => (
        <DocumentItem key={document.id} document={document} accessToken={accessToken} onDelete={onDelete} />
      ))}
    </div>
  )
}
