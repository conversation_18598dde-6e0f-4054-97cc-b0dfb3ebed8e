import React from 'react'

import { FileText } from 'lucide-react'

interface FallbackPreviewProps {
  name?: string
  className?: string
}

export function FallbackPreview({ name = 'This file', className }: FallbackPreviewProps) {
  return (
    <div className={className || 'flex flex-col items-center justify-center h-40 text-gray-500'}>
      <FileText className="w-12 h-12 mb-2" />
      <span className="font-medium">Preview not available for {name}</span>
    </div>
  )
}
