'use client'

import { useTransition } from 'react'

import { setUserLocale } from '@/i18n/i18n-actions'
import { Locale, localesOptions } from '@/i18n/i18n-libs'
import { useLocale, useTranslations } from 'next-intl'
import * as RPNInput from 'react-phone-number-input'

import { cn } from '@/lib/utils'

import { FlagComponent } from './ui/phone-input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from './ui/select'

type Props = {
  className?: string
  justFlag?: boolean
}

export default function LocaleSwitcher({ className, justFlag }: Props) {
  const t = useTranslations()
  const [isPending, startTransition] = useTransition()
  const locale = useLocale()

  const flag = localesOptions.find((option) => option.value === locale)?.country

  function onChange(value: string) {
    const locale = value as Locale
    startTransition(async () => {
      await setUserLocale(locale)
    })
  }

  return (
    <div className={cn('relative', className)}>
      <Select onValueChange={onChange} disabled={isPending} value={locale}>
        <SelectTrigger className="bg-background" hideChevron={justFlag}>
          {justFlag ? (
            <FlagComponent country={flag as RPNInput.Country} countryName={flag as RPNInput.Country} />
          ) : (
            <SelectValue placeholder="Select a language" />
          )}
        </SelectTrigger>
        <SelectContent>
          {localesOptions.map((locale) => (
            <SelectItem key={locale.value} value={locale.value}>
              <FlagComponent country={locale.country} countryName={locale.country} />
              {t(locale.label)}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
    </div>
  )
}
