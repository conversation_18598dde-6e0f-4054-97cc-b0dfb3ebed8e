'use client'

import { useMemo, useState } from 'react'

import { COUNTRIES_DATA } from '@/constants/countries'
import { Check, ChevronDown } from 'lucide-react'
import { useLocale, useTranslations } from 'next-intl'
import { FieldPath, FieldValues, useFormContext } from 'react-hook-form'

import { cn } from '@/lib/utils'
import { Button } from '@/components/ui/button'
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from '@/components/ui/command'
import { FormControl, FormField, FormItem, FormMessage } from '@/components/ui/form'
import { Label } from '@/components/ui/label'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'
import { Select, SelectContent, SelectItem, SelectTrigger } from '@/components/ui/select'

export interface SelectOption {
  value: string
  label: string
}

interface FormCountrySelectProps<TFieldValues extends FieldValues> {
  name: FieldPath<TFieldValues>
  label: string
  isRequired?: boolean
  placeholder?: string
  className?: string
  disabled?: boolean
  onChange?: (value: string) => void
  autoComplete?: boolean
  searchPlaceholder?: string
  emptyMessage?: string
  isNationality?: boolean
}

export const FormCountrySelect = <TFieldValues extends FieldValues>({
  name,
  label,
  isRequired = false,
  placeholder,
  className,
  disabled = false,
  onChange,
  autoComplete = true,
  searchPlaceholder,
  emptyMessage,
  isNationality = false,
}: FormCountrySelectProps<TFieldValues>) => {
  const { control } = useFormContext<TFieldValues>()
  const t = useTranslations()
  const locale = useLocale()
  const [open, setOpen] = useState(false)

  const options = useMemo(() => {
    return COUNTRIES_DATA.map((country) => {
      let label = ''
      if (isNationality) {
        const nationalityKey = `nationality_${locale}` as keyof typeof country
        label = country[nationalityKey] ?? ''
      } else {
        label = country[locale as keyof typeof country] ?? ''
      }
      const flag = country.flag ?? ''

      const fullLabel = flag ? flag + ' ' + label : label

      return {
        value: country.code,
        label: fullLabel,
      }
    })
  }, [locale, isNationality])

  return (
    <FormField
      name={name}
      control={control}
      render={({ field, fieldState }) => (
        <FormItem className={cn(className)}>
          <Label className="text-xs font-light ">
            {t(label)}
            {isRequired && <span className="text-destructive">*</span>}
          </Label>

          <FormControl>
            {autoComplete ? (
              // Autocomplete/Combobox Implementation
              <Popover open={open} onOpenChange={setOpen}>
                <PopoverTrigger asChild className="rounded-md">
                  <Button
                    variant="outline"
                    role="combobox"
                    aria-expanded={open}
                    disabled={disabled}
                    className={cn(
                      'w-full justify-between bg-background px-3 font-normal outline-offset-0 hover:bg-background focus-visible:border-ring focus-visible:outline-[3px] focus-visible:outline-ring/20',
                      fieldState.invalid && 'border-destructive ring-destructive/20 rounded-md'
                    )}
                  >
                    <span className={cn('truncate', !field.value && 'text-muted-foreground')}>
                      {field.value
                        ? t(options.find((option) => option.value === field.value)?.label || '')
                        : placeholder
                          ? t(placeholder)
                          : 'Select option...'}
                    </span>
                    <ChevronDown
                      size={16}
                      strokeWidth={2}
                      className="shrink-0 text-muted-foreground/80"
                      aria-hidden="true"
                    />
                  </Button>
                </PopoverTrigger>
                <PopoverContent
                  className="w-full min-w-[var(--radix-popper-anchor-width)] border-input p-0"
                  align="start"
                >
                  <Command>
                    <CommandInput placeholder={searchPlaceholder ? t(searchPlaceholder) : 'Search...'} />
                    <CommandList>
                      <CommandEmpty>{emptyMessage ? t(emptyMessage) : 'No option found.'}</CommandEmpty>
                      <CommandGroup>
                        {options.map((option) => (
                          <CommandItem
                            key={option.value}
                            value={option.label}
                            keywords={[option.label, option.value]}
                            onSelect={() => {
                              const newValue = option.value === field.value ? '' : option.value
                              if (onChange) {
                                onChange(newValue)
                              } else {
                                field.onChange(newValue)
                              }
                              setOpen(false)
                            }}
                          >
                            {t(option.label)}
                            {field.value === option.value && <Check size={16} strokeWidth={2} className="ml-auto" />}
                          </CommandItem>
                        ))}
                      </CommandGroup>
                    </CommandList>
                  </Command>
                </PopoverContent>
              </Popover>
            ) : (
              // Regular Select Implementation
              <Select
                disabled={disabled}
                value={field.value}
                onValueChange={(value) => {
                  if (onChange) {
                    onChange(value)
                  } else {
                    field.onChange(value)
                  }
                }}
                onOpenChange={() => !fieldState.isTouched && field.onBlur()}
              >
                <SelectTrigger
                  id={name}
                  className={cn('w-full', fieldState.invalid && 'border-destructive ring-destructive/20')}
                >
                  <span className={cn('truncate', !field.value && 'text-muted-foreground')}>
                    {field.value
                      ? t(options.find((option) => option.value === field.value)?.label || '')
                      : placeholder
                        ? t(placeholder)
                        : 'Select option...'}
                  </span>
                </SelectTrigger>
                <SelectContent>
                  {options.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {t(option.label)}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            )}
          </FormControl>

          <FormMessage />
        </FormItem>
      )}
    />
  )
}
