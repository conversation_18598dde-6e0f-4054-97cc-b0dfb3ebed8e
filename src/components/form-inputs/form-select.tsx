'use client'

import { use<PERSON><PERSON>back, useEffect, useMemo, useState } from 'react'

import { Check, ChevronDown } from 'lucide-react'
import { useTranslations } from 'next-intl'
import { FieldPath, FieldValues, useFormContext } from 'react-hook-form'
import { FixedSizeList as List } from 'react-window'

import { cn } from '@/lib/utils'
import { Button } from '@/components/ui/button'
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from '@/components/ui/command'
import { FormControl, FormField, FormItem, FormMessage } from '@/components/ui/form'
import { Label } from '@/components/ui/label'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'
import { Select, SelectContent, SelectItem, SelectTrigger } from '@/components/ui/select'

export interface SelectOption {
  value: string
  label: string
}

interface FormSelectProps<TFieldValues extends FieldValues> {
  name: FieldPath<TFieldValues>
  label: string
  options: SelectOption[]
  isRequired?: boolean
  placeholder?: string
  className?: string
  disabled?: boolean
  onChange?: (value: string) => void
  autoComplete?: boolean
  searchPlaceholder?: string
  emptyMessage?: string
  // Virtualization props for large lists
  enableVirtualization?: boolean
  virtualizationThreshold?: number
  itemHeight?: number
  maxHeight?: number
}

export const FormSelect = <TFieldValues extends FieldValues>({
  name,
  label,
  options,
  isRequired = false,
  placeholder,
  className,
  disabled = false,
  onChange,
  autoComplete = true,
  searchPlaceholder,
  emptyMessage,
  enableVirtualization = false,
  virtualizationThreshold = 1000,
  itemHeight = 35,
  maxHeight = 300,
}: FormSelectProps<TFieldValues>) => {
  const { control } = useFormContext<TFieldValues>()
  const t = useTranslations()
  const [open, setOpen] = useState(false)
  const [searchValue, setSearchValue] = useState('')

  const memoizedOptions = useMemo(() => {
    return options.map((option) => ({
      ...option,
      label: t(option.label),
    }))
  }, [options, t])

  // Determine if we should use virtualization
  const shouldVirtualize = enableVirtualization || memoizedOptions.length >= virtualizationThreshold

  // Filter options based on search value
  const filteredOptions = useMemo(() => {
    if (!searchValue.trim()) {
      return memoizedOptions
    }

    const searchLower = searchValue.toLowerCase()
    return memoizedOptions.filter(
      (option) => option.label.toLowerCase().includes(searchLower) || option.value.toLowerCase().includes(searchLower)
    )
  }, [memoizedOptions, searchValue])

  // Debounced search handler
  const handleSearchChange = useCallback((value: string) => {
    setSearchValue(value)
  }, [])

  // Reset search when dropdown closes
  useEffect(() => {
    if (!open) {
      setSearchValue('')
    }
  }, [open])

  return (
    <FormField
      name={name}
      control={control}
      render={({ field, fieldState }) => (
        <FormItem className={cn(className)}>
          <Label className="text-xs font-light ">
            {t(label)}
            {isRequired && <span className="text-destructive">*</span>}
          </Label>

          <FormControl>
            {autoComplete ? (
              // Autocomplete/Combobox Implementation
              <Popover open={open} onOpenChange={setOpen}>
                <PopoverTrigger asChild className="rounded-md">
                  <Button
                    variant="outline"
                    role="combobox"
                    aria-expanded={open}
                    disabled={disabled}
                    className={cn(
                      'w-full justify-between bg-background px-3 font-normal outline-offset-0 hover:bg-background focus-visible:border-ring focus-visible:outline-[3px] focus-visible:outline-ring/20',
                      fieldState.invalid && 'border-destructive ring-destructive/20 rounded-md'
                    )}
                  >
                    <span className={cn('truncate', !field.value && 'text-muted-foreground')}>
                      {field.value
                        ? memoizedOptions.find((option) => option.value === field.value)?.label
                        : placeholder
                          ? t(placeholder)
                          : 'Select option...'}
                    </span>
                    <ChevronDown
                      size={16}
                      strokeWidth={2}
                      className="shrink-0 text-muted-foreground/80"
                      aria-hidden="true"
                    />
                  </Button>
                </PopoverTrigger>
                <PopoverContent
                  className="w-full min-w-[var(--radix-popper-anchor-width)] border-input p-0"
                  align="start"
                >
                  {shouldVirtualize ? (
                    // Virtualized rendering for large lists - bypass Command to avoid double scroll
                    <div className="p-1">
                      <div className="flex  w-full rounded-md border border-input bg-background px-3 py-2 text-sm placeholder:text-muted-foreground  mb-2">
                        <input
                          placeholder={searchPlaceholder ? t(searchPlaceholder) : 'Search...'}
                          value={searchValue}
                          onChange={(e) => handleSearchChange(e.target.value)}
                          className="w-full bg-transparent outline-none placeholder:text-muted-foreground"
                        />
                      </div>
                      {filteredOptions.length === 0 ? (
                        <div className="py-6 text-center text-sm text-muted-foreground">
                          {emptyMessage ? t(emptyMessage) : 'No option found.'}
                        </div>
                      ) : (
                        <List
                          height={Math.min(filteredOptions.length * itemHeight, maxHeight)}
                          itemCount={filteredOptions.length}
                          itemSize={itemHeight}
                          width="100%"
                        >
                          {({ index, style }) => {
                            const option = filteredOptions[index]
                            if (!option) return null

                            return (
                              <div style={style} className="px-1">
                                <div
                                  className="relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none hover:bg-accent hover:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50"
                                  onClick={() => {
                                    const newValue = option.value === field.value ? '' : option.value
                                    if (onChange) {
                                      onChange(newValue)
                                    } else {
                                      field.onChange(newValue)
                                    }
                                    setOpen(false)
                                  }}
                                >
                                  {option.label}
                                  {field.value === option.value && (
                                    <Check size={16} strokeWidth={2} className="ml-auto" />
                                  )}
                                </div>
                              </div>
                            )
                          }}
                        </List>
                      )}
                    </div>
                  ) : (
                    // Regular rendering for small lists using Command
                    <Command shouldFilter={false}>
                      <CommandInput
                        placeholder={searchPlaceholder ? t(searchPlaceholder) : 'Search...'}
                        value={searchValue}
                        onValueChange={handleSearchChange}
                      />
                      <CommandList style={{ maxHeight: maxHeight }}>
                        <CommandEmpty>{emptyMessage ? t(emptyMessage) : 'No option found.'}</CommandEmpty>
                        <CommandGroup>
                          {filteredOptions.map((option) => {
                            return (
                              <CommandItem
                                key={option.value}
                                value={option.label}
                                keywords={[option.label, option.value]}
                                onSelect={() => {
                                  const newValue = option.value === field.value ? '' : option.value
                                  if (onChange) {
                                    onChange(newValue)
                                  } else {
                                    field.onChange(newValue)
                                  }
                                  setOpen(false)
                                }}
                              >
                                {option.label}
                                {field.value === option.value && (
                                  <Check size={16} strokeWidth={2} className="ml-auto" />
                                )}
                              </CommandItem>
                            )
                          })}
                        </CommandGroup>
                      </CommandList>
                    </Command>
                  )}
                </PopoverContent>
              </Popover>
            ) : (
              // Regular Select Implementation
              <Select
                disabled={disabled}
                value={field.value}
                name={name}
                onValueChange={(value) => {
                  if (onChange) {
                    onChange(value)
                  } else {
                    field.onChange(value)
                  }
                }}
                onOpenChange={() => !fieldState.isTouched && field.onBlur()}
              >
                <SelectTrigger
                  id={name}
                  className={cn('w-full', fieldState.invalid && 'border-destructive ring-destructive/20')}
                >
                  <span className={cn('truncate', !field.value && 'text-muted-foreground')}>
                    {field.value
                      ? memoizedOptions.find((option) => option.value === field.value)?.label
                      : placeholder
                        ? t(placeholder)
                        : 'Select option...'}
                  </span>
                </SelectTrigger>
                <SelectContent>
                  {memoizedOptions.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            )}
          </FormControl>

          <FormMessage />
        </FormItem>
      )}
    />
  )
}
