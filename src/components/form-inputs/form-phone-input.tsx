'use client'

import { useTranslations } from 'next-intl'
import { FieldPath, FieldValues, useFormContext } from 'react-hook-form'

import { cn } from '@/lib/utils'
import { FormControl, FormField, FormItem, FormMessage } from '@/components/ui/form'
import { Label } from '@/components/ui/label'
import { PhoneInput } from '@/components/ui/phone-input'

interface FormPhoneInputProps<TFieldValues extends FieldValues> {
  name: FieldPath<TFieldValues>
  label: string
  isRequired?: boolean
  placeholder?: string
  className?: string
}

export const FormPhoneInput = <TFieldValues extends FieldValues>({
  name,
  label,
  isRequired = false,
  placeholder,
  className,
}: FormPhoneInputProps<TFieldValues>) => {
  const { control } = useFormContext<TFieldValues>()
  const t = useTranslations()

  return (
    <FormField
      control={control}
      name={name}
      render={({ field }) => (
        <FormItem className={cn(className)}>
          <Label className="text-xs font-light">
            {t(label)}
            {isRequired && <span className="text-destructive">*</span>}
          </Label>

          <FormControl>
            <PhoneInput
              value={field.value}
              onChange={field.onChange}
              placeholder={placeholder ? t(placeholder) : ''}
              name={name}
            />
          </FormControl>

          <FormMessage />
        </FormItem>
      )}
    />
  )
}
