'use client'

import { useTranslations } from 'next-intl'
import { Control, FieldPath, FieldValues } from 'react-hook-form'

import { Checkbox } from '@/components/ui/checkbox'
import { FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form'

interface FormCheckboxProps<T extends FieldValues> {
  name: FieldPath<T>
  label: string
  control?: Control<T>
  disabled?: boolean
  description?: string
}

export function FormCheckbox<T extends FieldValues>({
  name,
  label,
  control,
  disabled = false,
  description,
}: FormCheckboxProps<T>) {
  const t = useTranslations()

  return (
    <FormField
      control={control}
      name={name}
      render={({ field }) => (
        <FormItem className="flex flex-row items-start space-x-3 space-y-0">
          <FormControl>
            <Checkbox checked={field.value} onCheckedChange={field.onChange} disabled={disabled} />
          </FormControl>
          <div className="space-y-1 leading-none">
            <FormLabel className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
              {t(label)}
            </FormLabel>
            {description && <p className="text-sm text-muted-foreground">{t(description)}</p>}
            <FormMessage />
          </div>
        </FormItem>
      )}
    />
  )
}
