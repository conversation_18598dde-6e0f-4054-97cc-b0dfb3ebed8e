'use client'

import React, { useEffect, useState } from 'react'

import type { Locale } from '@/i18n/i18n-libs'
import { de, enGB } from 'date-fns/locale'
import { Calendar as CalendarIcon } from 'lucide-react'
import { useLocale, useTranslations } from 'next-intl'
import { FieldPath, FieldValues, useFormContext } from 'react-hook-form'

import { formatDateLocaleClient } from '@/lib/format-date-locale'
import { cn } from '@/lib/utils'
import { Button } from '@/components/ui/button'
import { Calendar } from '@/components/ui/calendar'
import { FormControl, FormField, FormItem, FormMessage } from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'

// Map your app locales to date-fns locales for Calendar component
const dateFnsLocales = {
  en: enGB,
  de: de,
} as const

// Utility functions for 12-hour time format handling
function formatTimeFor12Hour(date: Date, includeSeconds: boolean = false): { time: string; period: 'AM' | 'PM' } {
  let hours = date.getHours()
  const minutes = date.getMinutes().toString().padStart(2, '0')
  const seconds = date.getSeconds().toString().padStart(2, '0')

  const period: 'AM' | 'PM' = hours >= 12 ? 'PM' : 'AM'

  // Convert to 12-hour format
  if (hours === 0)
    hours = 12 // midnight
  else if (hours > 12) hours = hours - 12

  const timeString = includeSeconds
    ? `${hours.toString().padStart(2, '0')}:${minutes}:${seconds}`
    : `${hours.toString().padStart(2, '0')}:${minutes}`

  return { time: timeString, period }
}

function parseTimeFrom12Hour(timeString: string, period: 'AM' | 'PM', baseDate?: Date): Date {
  const date = baseDate ? new Date(baseDate) : new Date()

  if (!timeString) return date

  const timeParts = timeString.split(':')
  let hours = parseInt(timeParts[0], 10) || 0
  const minutes = parseInt(timeParts[1], 10) || 0
  const seconds = parseInt(timeParts[2], 10) || 0

  // Convert from 12-hour to 24-hour format
  if (period === 'AM') {
    if (hours === 12) hours = 0 // midnight
  } else {
    // PM
    if (hours !== 12) hours = hours + 12
  }

  date.setHours(hours, minutes, seconds, 0)
  return date
}

function getCurrentTime(): Date {
  return new Date()
}

// Native TimePicker Component using HTML time input
interface TimePickerProps {
  date: Date | undefined
  setDate: (date: Date | undefined) => void
  includeSeconds?: boolean
}

function TimePicker({ date, setDate, includeSeconds = false }: TimePickerProps) {
  const t = useTranslations()

  // Initialize with current time if no date is provided
  const currentDate = date || getCurrentTime()
  const { time: timeValue, period } = formatTimeFor12Hour(currentDate, includeSeconds)

  const [selectedPeriod, setSelectedPeriod] = useState<'AM' | 'PM'>(period)
  const [timeInput, setTimeInput] = useState(timeValue)

  // Update local state when date prop changes
  useEffect(() => {
    if (date) {
      const { time, period: newPeriod } = formatTimeFor12Hour(date, includeSeconds)
      setTimeInput(time)
      setSelectedPeriod(newPeriod)
    }
  }, [date, includeSeconds])

  const handleTimeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newTimeValue = e.target.value
    setTimeInput(newTimeValue)

    if (!newTimeValue) return

    // If no date exists, create a new one with today's date
    const baseDate = date || new Date()
    const newDate = parseTimeFrom12Hour(newTimeValue, selectedPeriod, baseDate)
    setDate(newDate)
  }

  const handlePeriodChange = (newPeriod: 'AM' | 'PM') => {
    setSelectedPeriod(newPeriod)

    if (!timeInput) return

    // If no date exists, create a new one with today's date
    const baseDate = date || new Date()
    const newDate = parseTimeFrom12Hour(timeInput, newPeriod, baseDate)
    setDate(newDate)
  }

  // Set current time as default when component first loads and no date is set
  useEffect(() => {
    if (!date) {
      const now = getCurrentTime()
      setDate(now)
    }
  }, [date, setDate])

  return (
    <div className="flex flex-col gap-2">
      <Label className="text-xs">
        {t('GLOBAL.HOUR')} - {t('GLOBAL.MINUTES')}
      </Label>
      <div className="flex gap-2 items-center">
        <Input
          type="time"
          value={timeInput}
          onChange={handleTimeChange}
          step={includeSeconds ? '1' : '60'}
          className="flex-1 bg-background appearance-none [&::-webkit-calendar-picker-indicator]:hidden [&::-webkit-calendar-picker-indicator]:appearance-none"
        />
        <Select value={selectedPeriod} onValueChange={handlePeriodChange}>
          <SelectTrigger className="w-20">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="AM">AM</SelectItem>
            <SelectItem value="PM">PM</SelectItem>
          </SelectContent>
        </Select>
      </div>
    </div>
  )
}

// Month and Year Selects for Calendar
interface MonthYearSelectsProps {
  date: Date | undefined
  onMonthChange: (month: number) => void
  onYearChange: (year: number) => void
}

function MonthYearSelects({ date, onMonthChange, onYearChange }: MonthYearSelectsProps) {
  const currentDate = date || new Date()
  const currentMonth = currentDate.getMonth()
  const currentYear = currentDate.getFullYear()
  const locale = useLocale() as Locale
  const t = useTranslations()

  // Generate locale-aware month names
  const months = Array.from({ length: 12 }, (_, i) => {
    const monthDate = new Date(2024, i, 1) // Use any year, just need the month
    return formatDateLocaleClient(monthDate, locale, 'MMMM') // Full month name
  })

  // Generate years (from 1900 to 20 years after current year)
  const startYear = 1900
  const endYear = new Date().getFullYear() + 20
  const years = Array.from({ length: endYear - startYear + 1 }, (_, i) => startYear + i)

  return (
    <div className="flex gap-2 p-3 border-b border-border">
      <Select value={currentMonth.toString()} onValueChange={(value) => onMonthChange(parseInt(value))}>
        <SelectTrigger className="flex-1">
          <SelectValue placeholder={t('FORM.CALENDAR.MONTH_PLACEHOLDER', { defaultValue: 'Month' })} />
        </SelectTrigger>
        <SelectContent>
          {months.map((month, index) => (
            <SelectItem key={index} value={index.toString()}>
              {month}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>

      <Select value={currentYear.toString()} onValueChange={(value) => onYearChange(parseInt(value))}>
        <SelectTrigger className="flex-1">
          <SelectValue placeholder={t('FORM.CALENDAR.YEAR_PLACEHOLDER', { defaultValue: 'Year' })} />
        </SelectTrigger>
        <SelectContent>
          {years.map((year) => (
            <SelectItem key={year} value={year.toString()}>
              {year}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
    </div>
  )
}

// Main FormCalendar Component
interface FormCalendarProps<TFieldValues extends FieldValues> {
  name: FieldPath<TFieldValues>
  label: string
  isRequired?: boolean
  placeholder?: string
  className?: string
  disabled?: boolean
  includeTime?: boolean
  includeSeconds?: boolean
  dateOnly?: boolean
  formatString?: string
}

export const FormCalendar = <TFieldValues extends FieldValues>({
  name,
  label,
  isRequired = false,
  placeholder,
  className,
  disabled = false,
  includeTime = false,
  includeSeconds = false,
  dateOnly = false,
  formatString,
}: FormCalendarProps<TFieldValues>) => {
  const { control, watch } = useFormContext<TFieldValues>()
  const t = useTranslations()
  const locale = useLocale() as Locale

  // State to track the currently displayed month in the calendar
  const [displayedMonth, setDisplayedMonth] = useState<Date>(() => new Date())

  // Watch the field value to sync displayedMonth when it changes externally
  const fieldValue = watch(name)

  // Update displayedMonth when field value changes externally
  useEffect(() => {
    if (fieldValue) {
      const fieldDate = new Date(fieldValue as string)
      setDisplayedMonth(new Date(fieldDate.getFullYear(), fieldDate.getMonth(), 1))
    }
  }, [fieldValue])

  const getDisplayFormat = () => {
    if (formatString) return formatString
    if (dateOnly) return 'PPP'
    if (includeTime && includeSeconds) return 'PPP HH:mm:ss'
    if (includeTime) return 'PPP HH:mm'
    return 'PPP'
  }

  return (
    <FormField
      name={name}
      control={control}
      render={({ field, fieldState }) => (
        <FormItem className={cn(className)}>
          <Label className="text-xs font-light">
            {t(label)}
            {isRequired && <span className="text-destructive">*</span>}
          </Label>

          <FormControl>
            <Popover>
              <PopoverTrigger asChild className="rounded-md">
                <Button
                  variant="outline"
                  disabled={disabled}
                  className={cn(
                    'w-full justify-between text-left font-normal',
                    !field.value && 'text-muted-foreground',
                    fieldState.invalid && 'border-destructive ring-destructive/20'
                  )}
                >
                  {field.value ? (
                    formatDateLocaleClient(new Date(field.value), locale, getDisplayFormat())
                  ) : (
                    <span>{placeholder ? t(placeholder) : ''}</span>
                  )}
                  <CalendarIcon className=" h-4 w-4" />
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0" align="start">
                <MonthYearSelects
                  date={displayedMonth}
                  onMonthChange={(month) => {
                    // Update the displayed month state
                    const newDisplayedMonth = new Date(displayedMonth)
                    newDisplayedMonth.setMonth(month)
                    setDisplayedMonth(newDisplayedMonth)

                    // If there's a selected date, update it to maintain the day but change month
                    if (field.value) {
                      const newDate = new Date(field.value)
                      newDate.setMonth(month)
                      field.onChange(newDate)
                    }
                  }}
                  onYearChange={(year) => {
                    // Update the displayed month state
                    const newDisplayedMonth = new Date(displayedMonth)
                    newDisplayedMonth.setFullYear(year)
                    setDisplayedMonth(newDisplayedMonth)

                    // If there's a selected date, update it to maintain the day but change year
                    if (field.value) {
                      const newDate = new Date(field.value)
                      newDate.setFullYear(year)
                      field.onChange(newDate)
                    }
                  }}
                />
                <Calendar
                  mode="single"
                  month={displayedMonth}
                  locale={dateFnsLocales[locale] || dateFnsLocales.en}
                  onMonthChange={setDisplayedMonth}
                  selected={field.value ? new Date(field.value) : undefined}
                  onSelect={(date) => {
                    if (!date) {
                      field.onChange(undefined)
                      return
                    }

                    if (field.value && includeTime) {
                      // Preserve time when changing date
                      const existingDate = new Date(field.value)
                      date.setHours(existingDate.getHours())
                      date.setMinutes(existingDate.getMinutes())
                      date.setSeconds(existingDate.getSeconds())
                    }

                    field.onChange(date)
                    // Update displayed month to match the selected date
                    setDisplayedMonth(new Date(date.getFullYear(), date.getMonth(), 1))
                  }}
                  autoFocus
                />
                {includeTime && (
                  <div className="p-3 border-t border-border">
                    <TimePicker
                      date={field.value ? new Date(field.value) : undefined}
                      setDate={(newDate) => {
                        if (newDate) {
                          // If no previous date was selected, use today's date with the selected time
                          if (!field.value) {
                            const today = new Date()
                            newDate.setFullYear(today.getFullYear(), today.getMonth(), today.getDate())
                          }
                          field.onChange(newDate)
                        } else {
                          field.onChange(undefined)
                        }
                      }}
                      includeSeconds={includeSeconds}
                    />
                  </div>
                )}
              </PopoverContent>
            </Popover>
          </FormControl>

          <FormMessage />
        </FormItem>
      )}
    />
  )
}
