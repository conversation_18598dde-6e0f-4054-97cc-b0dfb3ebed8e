'use client'

import { useEffect, useState } from 'react'

import {
  detectCardType,
  formatCardNumber,
  formatExpiryDate,
} from '@/modules/data-management/components/steps/payment/card-detection.utils'
import { CardTypeIcon } from '@/modules/data-management/components/steps/payment/card-type-icon'
import { CardType } from '@/modules/data-management/types/payment-schema'
import { useTranslations } from 'next-intl'
import { FieldPath, FieldValues, useFormContext } from 'react-hook-form'

import { cn } from '@/lib/utils'
import { FormControl, FormDescription, FormField, FormItem, FormMessage } from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'

interface FormCardInputProps<TFieldValues extends FieldValues>
  extends Omit<React.ComponentProps<typeof Input>, 'name'> {
  name: FieldPath<TFieldValues>
  label: string
  isRequired?: boolean
  placeholder?: string
  inputType: 'cardNumber' | 'expiryDate'
  onCardTypeChange?: (cardType: CardType) => void
  description?: string
  className?: string
  inputClassName?: string
}

export const FormCardInput = <TFieldValues extends FieldValues>({
  name,
  label,
  isRequired,
  placeholder,
  inputType,
  onCardTypeChange,
  description,
  className,
  inputClassName,
  ...props
}: FormCardInputProps<TFieldValues>) => {
  const { control, setValue, watch } = useFormContext<TFieldValues>()
  const [cardType, setCardType] = useState<CardType>('unknown')
  const t = useTranslations()

  const fieldValue = watch(name) || ''

  useEffect(() => {
    if (inputType === 'cardNumber' && fieldValue) {
      const cleanValue = String(fieldValue).replace(/\D/g, '')
      const detectedType = detectCardType(cleanValue)
      setCardType(detectedType)
      onCardTypeChange?.(detectedType)
    }
  }, [fieldValue, inputType, onCardTypeChange])

  const handleInputChange = (value: string) => {
    let formattedValue = value

    if (inputType === 'cardNumber') {
      const cleanValue = value.replace(/\D/g, '')

      // Detect card type and set max length
      const detectedType = detectCardType(cleanValue)
      const maxLength = detectedType === 'amex' ? 15 : 16

      if (cleanValue.length <= maxLength) {
        formattedValue = cleanValue
        setCardType(detectedType)
        onCardTypeChange?.(detectedType)
      } else {
        return // Don't update if exceeds max length
      }
    } else if (inputType === 'expiryDate') {
      const cleanValue = value.replace(/\D/g, '')
      if (cleanValue.length <= 4) {
        formattedValue = formatExpiryDate(cleanValue)
      } else {
        return // Don't update if exceeds max length
      }
    }

    setValue(name, formattedValue as any, { shouldValidate: true })
  }

  const getDisplayValue = (value: string) => {
    if (inputType === 'cardNumber') {
      const cleanValue = String(value).replace(/\D/g, '')
      return formatCardNumber(cleanValue)
    }
    return value
  }

  const getSuffix = () => {
    if (inputType === 'cardNumber' && cardType !== 'unknown') {
      return (
        <div className="flex items-center gap-1">
          <CardTypeIcon cardType={cardType} className="w-6 h-4" />
        </div>
      )
    }
    return null
  }

  return (
    <FormField
      control={control}
      name={name}
      render={({ field, fieldState }) => (
        <FormItem className={cn(className)}>
          <Label className="text-xs font-light" htmlFor={name}>
            {t(label)}
            {isRequired && <span className="text-destructive">*</span>}
          </Label>
          <div className="relative">
            <FormControl>
              <Input
                {...props}
                id={name}
                value={getDisplayValue(field.value || '')}
                onChange={(e) => handleInputChange(e.target.value)}
                onBlur={field.onBlur}
                aria-invalid={!!fieldState.error}
                placeholder={placeholder ? t(placeholder) : ''}
                className={cn(
                  getSuffix() && 'pr-16', // Make room for card type icon
                  fieldState.error && 'border-red-500 focus-visible:ring-red-500',
                  inputClassName
                )}
                maxLength={inputType === 'cardNumber' ? 23 : inputType === 'expiryDate' ? 5 : undefined}
              />
            </FormControl>

            {/* Card type suffix */}
            {getSuffix() && (
              <div className="absolute inset-y-0 right-2 flex items-center justify-center">{getSuffix()}</div>
            )}
          </div>
          {/* Show description only when no error */}
          {!fieldState.error && description && <FormDescription>{description}</FormDescription>}

          {/* Show error message when there is an error */}
          <FormMessage />
        </FormItem>
      )}
    />
  )
}
