'use client'

import { useTranslations } from 'next-intl'
import { FieldPath, FieldValues, useFormContext } from 'react-hook-form'

import { cn } from '@/lib/utils'
import { FormControl, FormField, FormItem, FormMessage } from '@/components/ui/form'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'

interface FormTextareaProps<TFieldValues extends FieldValues>
  extends Omit<React.ComponentProps<typeof Textarea>, 'name'> {
  name: FieldPath<TFieldValues>
  label: string
  isRequired?: boolean
  placeholder?: string
  maxLength?: number
  showCounter?: boolean
}

export const FormTextarea = <TFieldValues extends FieldValues>({
  name,
  label,
  isRequired = false,
  placeholder,
  className,
  maxLength,
  showCounter = false,
  ...props
}: FormTextareaProps<TFieldValues>) => {
  const { control } = useFormContext<TFieldValues>()
  const t = useTranslations()

  return (
    <FormField
      control={control}
      name={name}
      render={({ field, fieldState }) => (
        <FormItem className={cn(className)}>
          <Label className="text-xs font-light ">
            {t(label)}
            {isRequired && <span className="text-destructive">*</span>}
          </Label>

          <FormControl>
            <Textarea
              {...field}
              {...props}
              id={name}
              aria-invalid={!!fieldState.error}
              placeholder={placeholder ? t(placeholder) : ''}
              maxLength={maxLength}
              className={cn(fieldState.invalid && 'border-destructive ring-destructive/20', className)}
            />
          </FormControl>

          {showCounter && maxLength && (
            <div className="flex justify-end">
              <span className="text-xs text-muted-foreground">
                {field.value?.length || 0}/{maxLength}
              </span>
            </div>
          )}

          <FormMessage />
        </FormItem>
      )}
    />
  )
}
