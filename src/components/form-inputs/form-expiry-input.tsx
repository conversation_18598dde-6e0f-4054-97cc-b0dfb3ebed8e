'use client'

import {
  formatExpiryDate,
  validateExpiryDate,
} from '@/modules/data-management/components/steps/payment/card-detection.utils'
import { Calendar, Check, X } from 'lucide-react'
import { useTranslations } from 'next-intl'
import { FieldPath, FieldValues, useFormContext } from 'react-hook-form'

import { cn } from '@/lib/utils'
import { FormControl, FormDescription, FormField, FormItem, FormMessage } from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'

interface FormExpiryInputProps<TFieldValues extends FieldValues>
  extends Omit<React.ComponentProps<typeof Input>, 'name'> {
  name: FieldPath<TFieldValues>
  label: string
  isRequired?: boolean
  description?: string
  className?: string
  inputClassName?: string
}

export const FormExpiryInput = <TFieldValues extends FieldValues>({
  name,
  label,
  isRequired,
  description,
  placeholder = 'MM/YY',
  className,
  inputClassName,
  ...props
}: FormExpiryInputProps<TFieldValues>) => {
  const { control } = useFormContext<TFieldValues>()
  const t = useTranslations()

  const getValidationIcon = (value: string, isValid: boolean) => {
    if (!value) {
      return <Calendar className="w-4 h-4 text-gray-400" />
    }

    if (isValid) {
      return <Check className="w-4 h-4 text-green-500" />
    }

    if (value.length >= 5) {
      return <X className="w-4 h-4 text-red-500" />
    }

    return <Calendar className="w-4 h-4 text-gray-400" />
  }

  return (
    <FormField
      control={control}
      name={name}
      render={({ field, fieldState }) => {
        const isValid = field.value ? validateExpiryDate(field.value) : false

        return (
          <FormItem className={cn(className)}>
            <Label className="text-xs font-light" htmlFor={name}>
              {t(label)}
              {isRequired && <span className="text-destructive">*</span>}
            </Label>

            <div className="relative">
              <FormControl>
                <Input
                  {...field}
                  {...props}
                  id={name}
                  value={field.value || ''}
                  onChange={(e) => {
                    const inputValue = e.target.value
                    const cleanValue = inputValue.replace(/\D/g, '')

                    if (cleanValue.length <= 4) {
                      const formatted = formatExpiryDate(cleanValue)
                      field.onChange(formatted)
                    }
                  }}
                  placeholder={placeholder}
                  maxLength={5}
                  aria-invalid={!!fieldState.error}
                  className={cn('pr-10', inputClassName)}
                />
              </FormControl>

              {/* Validation Icon */}
              <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                {getValidationIcon(field.value || '', isValid)}
              </div>
            </div>

            {/* Show description only when no error */}
            {!fieldState.error && (
              <FormDescription>{description ? t(description) : 'Enter month and year (MM/YY)'}</FormDescription>
            )}

            {/* Show error message when there is an error */}
            <FormMessage />
          </FormItem>
        )
      }}
    />
  )
}
