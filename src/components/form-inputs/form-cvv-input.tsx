'use client'

import { useState } from 'react'

import { CardType } from '@/modules/data-management/types/payment-schema'
import { Check, Eye, EyeOff, Shield } from 'lucide-react'
import { useTranslations } from 'next-intl'
import { FieldPath, FieldValues, useFormContext } from 'react-hook-form'

import { cn } from '@/lib/utils'
import { FormControl, FormDescription, FormField, FormItem, FormMessage } from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'

interface FormCVVInputProps<TFieldValues extends FieldValues> extends Omit<React.ComponentProps<typeof Input>, 'name'> {
  name: FieldPath<TFieldValues>
  label: string
  isRequired?: boolean
  cardType?: CardType
  description?: string
  className?: string
  inputClassName?: string
}

export const FormCVVInput = <TFieldValues extends FieldValues>({
  name,
  label,
  isRequired,
  cardType = 'unknown',
  description,
  className,
  inputClassName,
  ...props
}: FormCVVInputProps<TFieldValues>) => {
  const { control } = useFormContext<TFieldValues>()
  const [showValue, setShowValue] = useState(false)
  const t = useTranslations()

  const maxLength = cardType === 'amex' ? 4 : 3
  const placeholder = cardType === 'amex' ? '1234' : '123'

  const getDefaultDescription = () => {
    return cardType === 'amex' ? '4-digit code on front of card' : '3-digit code on back of card'
  }

  return (
    <FormField
      control={control}
      name={name}
      render={({ field, fieldState }) => {
        const isValid = field.value?.length === maxLength && /^\d+$/.test(field.value)

        return (
          <FormItem className={cn(className)}>
            <div className="flex items-center gap-2">
              <Label className="text-xs font-light" htmlFor={name}>
                {t(label)}
                {isRequired && <span className="text-destructive">*</span>}
              </Label>
            </div>

            <div className="relative">
              <FormControl>
                <Input
                  {...field}
                  {...props}
                  id={name}
                  type={showValue ? 'text' : 'password'}
                  value={field.value || ''}
                  onChange={(e) => {
                    const value = e.target.value.replace(/\D/g, '') // Only digits
                    if (value.length <= maxLength) {
                      field.onChange(value)
                    }
                  }}
                  placeholder={placeholder}
                  maxLength={maxLength}
                  aria-invalid={!!fieldState.error}
                  className={cn(
                    'pr-20', // Make room for icons

                    inputClassName
                  )}
                />
              </FormControl>

              {/* Icons */}
              <div className="absolute right-2 top-1/2 transform -translate-y-1/2 flex items-center gap-1">
                {/* Validation indicator */}
                {field.value && (
                  <div className="flex items-center">
                    {isValid ? (
                      <Check className="w-4 h-4 text-green-500" />
                    ) : (
                      <Shield className="w-4 h-4 text-gray-400" />
                    )}
                  </div>
                )}

                {/* Show/Hide toggle */}
                <button
                  type="button"
                  onClick={() => setShowValue(!showValue)}
                  className="text-gray-400 hover:text-gray-600 focus:outline-none"
                >
                  {showValue ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                </button>
              </div>
            </div>

            {/* Show description only when no error */}
            {!fieldState.error && (
              <FormDescription>{description ? t(description) : getDefaultDescription()}</FormDescription>
            )}

            {/* Show error message when there is an error */}
            <FormMessage />
          </FormItem>
        )
      }}
    />
  )
}
