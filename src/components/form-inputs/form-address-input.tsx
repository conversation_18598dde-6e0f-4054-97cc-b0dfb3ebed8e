'use client'

import { useMemo } from 'react'

import { useFormContext, useWatch, type FieldPath, type FieldValues } from 'react-hook-form'

import { getCitiesForZipCode, getGermanZipCodes } from '@/lib/actions/locations/get-zip-codes'

import { FormInput } from './form-input'
import { FormSelect } from './form-select'

interface FormAddressInputProps<TFieldValues extends FieldValues> {
  zipFieldName: FieldPath<TFieldValues>
  cityFieldName: FieldPath<TFieldValues>
  selectedCountry: string
  zipLabel: string
  cityLabel: string
  isRequired?: boolean
  className?: string
  disabled?: boolean
}

export const FormAddressInput = <TFieldValues extends FieldValues>({
  zipFieldName,
  cityFieldName,
  selectedCountry,
  zipLabel,
  cityLabel,
  isRequired = false,
  className,
  disabled = false,
}: FormAddressInputProps<TFieldValues>) => {
  const zipOptions = getGermanZipCodes()

  const { control, clearErrors, setValue } = useFormContext<TFieldValues>()

  const zipCode = useWatch({ control, name: zipFieldName })
  const isGermanAddress = selectedCountry === 'DE'

  // Determine if this is a German address

  // Memoized cities for current ZIP code (optimized with O(1) lookup)
  const cityOptions = useMemo(() => {
    return getCitiesForZipCode(zipCode)
  }, [zipCode])

  if (isGermanAddress) {
    // German address: Use cascading dropdowns
    return (
      <>
        <FormSelect<TFieldValues>
          name={zipFieldName}
          label={zipLabel}
          options={zipOptions}
          isRequired={isRequired}
          disabled={disabled}
          onChange={(value) => {
            setValue(zipFieldName, value as never) // Update ZIP code
            setValue(cityFieldName, '' as never) // Clear city
          }}
          placeholder="Select ZIP code..."
          searchPlaceholder="Search ZIP codes..."
          emptyMessage="No ZIP codes found"
          autoComplete={true}
          enableVirtualization={true}
          virtualizationThreshold={1000}
          itemHeight={35}
          maxHeight={300}
        />

        <FormSelect<TFieldValues>
          name={cityFieldName}
          label={cityLabel}
          options={cityOptions}
          isRequired={isRequired}
          disabled={disabled || !zipCode}
          placeholder={!zipCode ? 'Please select ZIP code first' : 'Select city/district...'}
          searchPlaceholder="Search cities..."
          emptyMessage={!zipCode ? 'Please select a ZIP code first' : 'No cities found'}
          autoComplete={true}
        />
      </>
    )
  }

  // International address: Use free-text inputs

  return (
    <>
      <FormInput<TFieldValues>
        name={zipFieldName}
        label={zipLabel}
        isRequired={isRequired}
        disabled={disabled}
        placeholder="Enter ZIP/postal code"
      />

      <FormInput<TFieldValues>
        name={cityFieldName}
        label={cityLabel}
        isRequired={isRequired}
        disabled={disabled}
        placeholder="Enter city name"
      />
    </>
  )
}
