'use client'

import React, { useEffect, useState } from 'react'

import { useTranslations } from 'next-intl'
import { FieldPath, FieldValues, useFormContext } from 'react-hook-form'

import { cn } from '@/lib/utils'
import { FormControl, FormField, FormItem, FormMessage } from '@/components/ui/form'
import { Label } from '@/components/ui/label'
import { MultiSelect } from '@/components/ui/multi-select'

export interface MultiSelectOption {
  value: string
  label: string
  icon?: React.ComponentType<{ className?: string }>
}

/**
 * Controlled wrapper for MultiSelect component to work with react-hook-form
 */
interface MultiSelectControlledProps {
  options: MultiSelectOption[]
  value: string[]
  onValueChange: (values: string[]) => void
  placeholder?: string
  variant?: 'default' | 'secondary' | 'destructive' | 'inverted'
  maxCount?: number
  animation?: number
  modalPopover?: boolean
  disabled?: boolean
  className?: string
}

const MultiSelectControlled: React.FC<MultiSelectControlledProps> = ({
  options,
  value,
  onValueChange,
  placeholder = 'Select options',
  variant = 'default',
  maxCount = 3,
  animation = 0,
  modalPopover = false,
  disabled = false,
  className,
}) => {
  const [internalValue, setInternalValue] = useState<string[]>(value || [])

  // Sync internal value with external value
  useEffect(() => {
    setInternalValue(value || [])
  }, [value])

  const handleValueChange = (newValues: string[]) => {
    setInternalValue(newValues)
    onValueChange(newValues)
  }

  return (
    <MultiSelect
      options={options}
      onValueChange={handleValueChange}
      defaultValue={internalValue}
      placeholder={placeholder}
      variant={variant}
      maxCount={maxCount}
      animation={animation}
      modalPopover={modalPopover}
      disabled={disabled}
      className={className}
    />
  )
}

interface FormMultiSelectProps<TFieldValues extends FieldValues> {
  name: FieldPath<TFieldValues>
  label: string
  options: MultiSelectOption[]
  isRequired?: boolean
  placeholder?: string
  className?: string
  disabled?: boolean
  onChange?: (value: string[]) => void
  maxCount?: number
  animation?: number
  modalPopover?: boolean
  variant?: 'default' | 'secondary' | 'destructive' | 'inverted'
}

export const FormMultiSelect = <TFieldValues extends FieldValues>({
  name,
  label,
  options,
  isRequired = false,
  placeholder,
  className,
  disabled = false,
  onChange,
  maxCount = 3,
  animation = 0,
  modalPopover = false,
  variant = 'default',
}: FormMultiSelectProps<TFieldValues>) => {
  const { control } = useFormContext<TFieldValues>()
  const t = useTranslations()

  // Transform options to include translated labels
  const translatedOptions = options.map((option) => ({
    ...option,
    label: t(option.label),
  }))

  return (
    <FormField
      name={name}
      control={control}
      render={({ field, fieldState }) => (
        <FormItem className={cn(className)}>
          <Label className="text-xs font-light">
            {t(label)}
            {isRequired && <span className="text-destructive">*</span>}
          </Label>

          <FormControl>
            <MultiSelectControlled
              options={translatedOptions}
              value={field.value || []}
              onValueChange={(values: string[]) => {
                if (onChange) {
                  onChange(values)
                } else {
                  field.onChange(values)
                }
              }}
              placeholder={placeholder ? t(placeholder) : 'Select options'}
              variant={variant}
              maxCount={maxCount}
              animation={animation}
              modalPopover={modalPopover}
              disabled={disabled}
              className={cn('w-full', fieldState.invalid && 'border-destructive ring-destructive/20')}
            />
          </FormControl>

          <FormMessage />
        </FormItem>
      )}
    />
  )
}

/**
 * Helper function to convert SelectOption to MultiSelectOption
 * This allows compatibility with existing option formats
 */
export function convertToMultiSelectOptions(options: Array<{ value: string; label: string }>): MultiSelectOption[] {
  return options.map((option) => ({
    value: option.value,
    label: option.label,
  }))
}

/**
 * Helper function to validate multi-select field values
 * Ensures the value is always an array
 */
export function validateMultiSelectValue(value: any): string[] {
  if (Array.isArray(value)) {
    return value.filter((v) => typeof v === 'string')
  }
  if (typeof value === 'string' && value.length > 0) {
    return [value]
  }
  return []
}

/**
 * Helper function to get default value for multi-select fields
 */
export function getMultiSelectDefaultValue(defaultValue?: any): string[] {
  return validateMultiSelectValue(defaultValue)
}
