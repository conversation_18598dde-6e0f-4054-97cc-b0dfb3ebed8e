'use client'

import { useRef, useState } from 'react'

import { FilePlus, Trash2, Upload, X } from 'lucide-react'
import { useTranslations } from 'next-intl'
import { FieldPath, FieldValues, useFormContext } from 'react-hook-form'

import { cn } from '@/lib/utils'
import { Button } from '@/components/ui/button'
import { FormControl, FormField, FormItem, FormMessage } from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'

interface FormUploadProps<TFieldValues extends FieldValues> {
  name: FieldPath<TFieldValues>
  label: string
  isRequired?: boolean
  description?: string
  accept?: string
  multiple?: boolean
  maxFiles?: number
  maxSize?: number // in MB
  className?: string
  disabled?: boolean
}

export const FormUpload = <TFieldValues extends FieldValues>({
  name,
  label,
  isRequired = false,
  description,
  accept = '.pdf,.jpg,.jpeg,.png',
  multiple = true,
  maxFiles = 10,
  maxSize = 10, // 10MB default
  className,
  disabled = false,
}: FormUploadProps<TFieldValues>) => {
  const { control } = useFormContext<TFieldValues>()
  const t = useTranslations()
  const inputRef = useRef<HTMLInputElement>(null)
  const [error, setError] = useState<string | null>(null)

  const handleFileChange = (
    e: React.ChangeEvent<HTMLInputElement>,
    currentFiles: File[],
    onChange: (files: File[]) => void
  ) => {
    if (!e.target.files?.length) return

    setError(null)
    const newFiles = Array.from(e.target.files)

    // Check file size
    const oversizedFiles = newFiles.filter((file) => file.size > maxSize * 1024 * 1024)
    if (oversizedFiles.length > 0) {
      setError(`File ${oversizedFiles[0].name} is too large. Maximum size is ${maxSize}MB.`)
      return
    }

    // Check max files
    if (currentFiles.length + newFiles.length > maxFiles) {
      setError(`Too many files. Maximum allowed is ${maxFiles} files.`)
      return
    }

    const updatedFiles = [...currentFiles, ...newFiles]
    onChange(updatedFiles)

    // Reset input value to allow selecting the same file again
    if (inputRef.current) {
      inputRef.current.value = ''
    }
  }

  const removeFile = (index: number, currentFiles: File[], onChange: (files: File[]) => void) => {
    const updatedFiles = [...currentFiles]
    updatedFiles.splice(index, 1)
    onChange(updatedFiles)
  }

  const clearFiles = (onChange: (files: File[]) => void) => {
    onChange([])
    if (inputRef.current) {
      inputRef.current.value = ''
    }
  }

  return (
    <FormField
      control={control}
      name={name}
      render={({ field, fieldState }) => (
        <FormItem className={cn(className)}>
          <Label htmlFor={name} className="text-xs font-light">
            {t(label)}
            {isRequired && <span className="text-destructive">*</span>}
          </Label>

          <FormControl>
            <div className="space-y-4">
              {/* Upload area */}
              <div
                className={cn(
                  'flex flex-col items-center justify-center w-full h-32 px-4 transition bg-background border-2 border-dashed rounded-md appearance-none cursor-pointer hover:border-muted-foreground focus:outline-none',
                  disabled ? 'cursor-not-allowed opacity-50' : 'border-muted hover:border-muted-foreground',
                  fieldState.invalid && 'border-destructive'
                )}
                onClick={() => !disabled && inputRef.current?.click()}
              >
                <div className="flex flex-col items-center space-y-2">
                  <Upload className="w-6 h-6 text-muted-foreground" />
                  <span className="font-medium text-muted-foreground">Click to upload or drag and drop</span>
                  <span className="text-xs text-muted-foreground">
                    {accept.replace(/,/g, ', ').replace(/\./g, '').toUpperCase()}
                  </span>
                </div>
                <Input
                  ref={inputRef}
                  type="file"
                  className="hidden"
                  multiple={multiple}
                  accept={accept}
                  id={name}
                  disabled={disabled}
                  onChange={(e) => handleFileChange(e, field.value || [], field.onChange)}
                />
              </div>

              {/* Error message */}
              {error && <p className="text-sm font-medium text-destructive">{error}</p>}

              {/* File list */}
              {field.value && field.value.length > 0 && (
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <h4 className="text-sm font-medium">Selected files ({field.value.length})</h4>
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      disabled={disabled}
                      onClick={() => clearFiles(field.onChange)}
                      className="h-8 px-2 text-destructive"
                    >
                      <Trash2 className="w-4 h-4 mr-1" />
                      Clear all
                    </Button>
                  </div>

                  <ul className="space-y-2 max-h-60 overflow-y-auto">
                    {field.value.map((file: File, index: number) => (
                      <li key={index} className="flex items-center justify-between p-2 bg-muted/50 rounded-md">
                        <div className="flex items-center overflow-hidden">
                          <FilePlus className="w-4 h-4 mr-2 flex-shrink-0 text-muted-foreground" />
                          <span className="text-sm truncate">{file.name}</span>
                          <span className="text-xs text-muted-foreground ml-2">
                            ({(file.size / 1024 / 1024).toFixed(2)} MB)
                          </span>
                        </div>
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          disabled={disabled}
                          onClick={() => removeFile(index, field.value || [], field.onChange)}
                          className="h-6 w-6 p-0 text-destructive"
                        >
                          <X className="w-4 h-4" />
                        </Button>
                      </li>
                    ))}
                  </ul>
                </div>
              )}

              {description && <p className="text-xs text-muted-foreground">{t(description)}</p>}
            </div>
          </FormControl>

          <FormMessage />
        </FormItem>
      )}
    />
  )
}
