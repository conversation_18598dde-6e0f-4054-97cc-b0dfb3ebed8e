import Image from 'next/image'

import { cn } from '@/lib/utils'

interface StatusMessageProps {
  title: string
  message: string | React.ReactNode
  imageSize?: number
  type?: 'success' | 'edit' | 'error'
}

const ratio = {
  success: 1.2,
  edit: 1,
  error: 1,
}
export const StatusMessage = ({ title, message, imageSize = 150, type = 'success' }: StatusMessageProps) => {
  const src = `/images/${type || 'edit'}.png`
  const imageWidth = imageSize * ratio[type]

  return (
    <div className="flex flex-col gap-4 items-center justify-center mb-4">
      <Image
        src={src}
        alt="Edit"
        className={cn(`w-[${imageWidth}px]`, `h-[${imageSize}px]`)}
        width={imageWidth}
        height={imageSize}
      />
      <h1 className="text-2xl font-semibold text-gray-900">{title}</h1>
      {typeof message === 'string' ? <p>{message}</p> : message}
    </div>
  )
}
