{"title": "Contract Form", "description": "Create or edit insurance contract", "submitButtonText": "CONTRACTS.EDIT_FORM.SUBMIT_BUTTON.TEXT", "steps": [{"id": "contract-form", "title": "Contract Information", "description": "Enter contract details", "columns": 1, "fields": [{"name": "category_id", "type": "select", "label": "CONTRACTS.EDIT_FORM.CATEGORY_FIELD.LABEL", "placeholder": "Select category", "isRequired": true, "autoComplete": true, "options": [], "validation": [{"type": "required", "message": "CONTRACTS.VALIDATION.CATEGORY_REQUIRED"}]}, {"name": "provider_id", "type": "select", "label": "CONTRACTS.EDIT_FORM.PROVIDER_FIELD.LABEL", "placeholder": "Select provider", "isRequired": true, "options": [], "showIf": {"field": "category_id", "operator": "isNotEmpty"}, "validation": [{"type": "required", "message": "CONTRACTS.VALIDATION.PROVIDER_REQUIRED"}]}, {"name": "product_id", "type": "select", "label": "CONTRACTS.EDIT_FORM.PRODUCT_FIELD.LABEL", "placeholder": "Select product", "isRequired": true, "options": [], "showIf": {"field": "provider_id", "operator": "isNotEmpty"}, "validation": [{"type": "required", "message": "CONTRACTS.VALIDATION.PRODUCT_REQUIRED"}]}, {"name": "insurance_number", "type": "input", "label": "CONTRACTS.EDIT_FORM.INSURANCE_NUMBER_FIELD.LABEL", "placeholder": "Enter insurance number", "isRequired": true, "validation": [{"type": "required", "message": "CONTRACTS.VALIDATION.INSURANCE_NUMBER_REQUIRED"}]}, {"name": "cost", "type": "input", "label": "CONTRACTS.EDIT_FORM.COST_FIELD.LABEL", "placeholder": "0.00", "isRequired": true, "suffix": "€", "validation": [{"type": "required", "message": "CONTRACTS.VALIDATION.COST_REQUIRED"}]}, {"name": "period", "type": "select", "label": "CONTRACTS.EDIT_FORM.PAYMENT_PERIOD_FIELD.LABEL", "placeholder": "Select payment period", "isRequired": true, "options": [{"value": "ONCE", "label": "CONTRACTS.EDIT_FORM.PAYMENT_PERIOD_FIELD.OPTION.ONCE"}, {"value": "WEEKLY", "label": "CONTRACTS.EDIT_FORM.PAYMENT_PERIOD_FIELD.OPTION.WEEKLY"}, {"value": "MONTHLY", "label": "CONTRACTS.EDIT_FORM.PAYMENT_PERIOD_FIELD.OPTION.MONTHLY"}, {"value": "QUARTERLY", "label": "CONTRACTS.EDIT_FORM.PAYMENT_PERIOD_FIELD.OPTION.QUARTERLY"}, {"value": "HALFYEARLY", "label": "CONTRACTS.EDIT_FORM.PAYMENT_PERIOD_FIELD.OPTION.HALFYEARLY"}, {"value": "YEARLY", "label": "CONTRACTS.EDIT_FORM.PAYMENT_PERIOD_FIELD.OPTION.YEARLY"}, {"value": "FREE", "label": "CONTRACTS.EDIT_FORM.PAYMENT_PERIOD_FIELD.OPTION.FREE"}], "validation": [{"type": "required", "message": "CONTRACTS.VALIDATION.PERIOD_REQUIRED"}]}, {"name": "start_date", "type": "date", "label": "CONTRACTS.EDIT_FORM.STARTDATE_FIELD.LABEL", "isRequired": true, "mode": "date", "validation": [{"type": "required", "message": "CONTRACTS.VALIDATION.START_DATE_REQUIRED"}]}, {"name": "end_date", "type": "date", "label": "CONTRACTS.EDIT_FORM.ENDDATE_FIELD.LABEL", "isRequired": true, "mode": "date", "validation": [{"type": "required", "message": "CONTRACTS.VALIDATION.END_DATE_REQUIRED"}]}, {"name": "info", "type": "textarea", "label": "CONTRACTS.EDIT_FORM.COMMENTS_FIELD.LABEL", "placeholder": "Enter additional information...", "isRequired": false, "maxLength": 1000, "showCounter": true}, {"name": "files", "type": "upload", "label": "Documents", "isRequired": false, "accept": ".pdf,.jpg,.jpeg,.png", "multiple": true, "maxFiles": 10, "maxSize": 10, "helperText": "Upload contract documents (PDF, JPG, PNG, max 10MB each)"}]}]}