{"title": "Contract Form", "description": "Create a new insurance contract", "submitButtonText": "Create Contract", "showStepper": false, "steps": [{"id": "contract-details", "title": "Contract Details", "description": "Enter the contract information", "columns": 1, "fields": [{"name": "category_id", "type": "select", "label": "Category", "placeholder": "Select category", "isRequired": true, "colSpan": 1, "options": [{"value": "auto", "label": "Auto Insurance"}, {"value": "home", "label": "Home Insurance"}, {"value": "health", "label": "Health Insurance"}], "validation": [{"type": "required", "message": "Category is required"}]}, {"name": "provider_id", "type": "select", "label": "Provider", "placeholder": "Select provider", "isRequired": true, "colSpan": 1, "options": [{"value": "allianz", "label": "Allianz"}, {"value": "axa", "label": "AXA"}], "showIf": {"field": "category_id", "operator": "isNotEmpty"}, "validation": [{"type": "required", "message": "Provider is required"}]}, {"name": "insurance_number", "type": "input", "label": "Insurance Number", "placeholder": "Enter insurance number", "isRequired": true, "colSpan": 1, "validation": [{"type": "required", "message": "Insurance number is required"}]}, {"name": "cost", "type": "input", "label": "Monthly Cost", "placeholder": "0.00", "isRequired": true, "colSpan": 1, "suffix": "€", "validation": [{"type": "required", "message": "Cost is required"}]}, {"name": "start_date", "type": "date", "label": "Start Date", "isRequired": true, "colSpan": 1, "mode": "date", "validation": [{"type": "required", "message": "Start date is required"}]}, {"name": "info", "type": "textarea", "label": "Additional Information", "placeholder": "Enter any additional information...", "isRequired": false, "colSpan": 1, "maxLength": 500, "showCounter": true}]}]}