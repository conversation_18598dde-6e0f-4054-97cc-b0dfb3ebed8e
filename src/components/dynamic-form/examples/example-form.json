{"title": "Complete Registration Form", "description": "A comprehensive multi-step form demonstrating all field types and features", "logoUrl": "/og-image.png", "submitButtonText": "Complete Registration", "nextButtonText": "Continue", "previousButtonText": "Go Back", "showStepNumbers": true, "showProgressBar": true, "allowStepNavigation": false, "validateOnStepChange": true, "steps": [{"id": "personal-info", "title": "Personal Information", "description": "Tell us about yourself", "columns": 2, "fields": [{"name": "firstName", "type": "input", "label": "First Name", "placeholder": "Enter your first name", "isRequired": true, "colSpan": 1, "validation": [{"type": "required", "message": "First name is required"}, {"type": "<PERSON><PERSON><PERSON><PERSON>", "value": 2, "message": "First name must be at least 2 characters"}]}, {"name": "lastName", "type": "input", "label": "Last Name", "placeholder": "Enter your last name", "isRequired": true, "colSpan": 1, "validation": [{"type": "required", "message": "Last name is required"}, {"type": "<PERSON><PERSON><PERSON><PERSON>", "value": 2, "message": "Last name must be at least 2 characters"}]}, {"name": "email", "type": "input", "label": "Email Address", "placeholder": "<EMAIL>", "isRequired": true, "colSpan": 2, "validation": [{"type": "required", "message": "Email address is required"}, {"type": "email", "message": "Please enter a valid email address"}]}, {"name": "phone", "type": "phone", "label": "Phone Number", "placeholder": "Enter your phone number", "isRequired": true, "colSpan": 1, "validation": [{"type": "required", "message": "Phone number is required"}]}, {"name": "birthDate", "type": "date", "label": "Date of Birth", "isRequired": false, "colSpan": 1, "mode": "date"}]}, {"id": "address-info", "title": "Address Information", "description": "Where can we reach you?", "columns": 2, "fields": [{"name": "country", "type": "country", "label": "Country", "placeholder": "Select your country", "isRequired": true, "colSpan": 2, "validation": [{"type": "required", "message": "Please select your country"}]}, {"name": "street", "type": "input", "label": "Street Name", "placeholder": "Enter street name", "isRequired": true, "colSpan": 1, "validation": [{"type": "required", "message": "Street name is required"}]}, {"name": "streetNumber", "type": "input", "label": "House Number", "placeholder": "123", "isRequired": true, "colSpan": 1, "validation": [{"type": "required", "message": "House number is required"}]}, {"name": "zipCode", "type": "input", "label": "ZIP Code", "placeholder": "12345", "isRequired": true, "colSpan": 1, "validation": [{"type": "required", "message": "ZIP code is required"}, {"type": "regex", "pattern": "^[0-9]{5}$", "message": "ZIP code must be exactly 5 digits"}]}, {"name": "city", "type": "input", "label": "City", "placeholder": "Enter your city", "isRequired": true, "colSpan": 1, "validation": [{"type": "required", "message": "City is required"}]}]}, {"id": "preferences", "title": "Preferences & Contact", "description": "How would you like us to communicate with you?", "columns": 1, "fields": [{"name": "interests", "type": "multiselect", "label": "Areas of Interest", "placeholder": "Select your interests", "isRequired": false, "colSpan": 1, "maxCount": 3, "options": [{"value": "technology", "label": "Technology & Software"}, {"value": "sports", "label": "Sports & Fitness"}, {"value": "music", "label": "Music & Arts"}, {"value": "travel", "label": "Travel & Adventure"}, {"value": "cooking", "label": "Cooking & Food"}, {"value": "reading", "label": "Books & Reading"}]}, {"name": "newsletter", "type": "checkbox", "label": "Subscribe to our newsletter for updates and tips", "isRequired": false, "colSpan": 1}, {"name": "contactMethod", "type": "select", "label": "Preferred Contact Method", "placeholder": "How should we contact you?", "isRequired": true, "colSpan": 1, "options": [{"value": "email", "label": "Email"}, {"value": "phone", "label": "Phone Call"}, {"value": "sms", "label": "Text Message (SMS)"}], "validation": [{"type": "required", "message": "Please select a contact method"}]}, {"name": "preferredTime", "type": "select", "label": "Best Time to Call", "placeholder": "When is the best time to reach you?", "isRequired": false, "colSpan": 1, "options": [{"value": "morning", "label": "Morning (9 AM - 12 PM)"}, {"value": "afternoon", "label": "Afternoon (12 PM - 5 PM)"}, {"value": "evening", "label": "Evening (5 PM - 8 PM)"}], "showIf": {"field": "contactMethod", "operator": "equals", "value": "phone"}}]}, {"id": "additional-info", "title": "Additional Information", "description": "Tell us more about your background and availability", "columns": 1, "fields": [{"name": "bio", "type": "textarea", "label": "Brief Bio", "placeholder": "Tell us a bit about yourself, your background, and what you're looking for...", "isRequired": false, "colSpan": 1, "maxLength": 500, "showCounter": true, "validation": [{"type": "max<PERSON><PERSON><PERSON>", "value": 500, "message": "Bio must be 500 characters or less"}]}, {"name": "hasExperience", "type": "checkbox", "label": "I have relevant professional experience", "isRequired": false, "colSpan": 1}, {"name": "experienceYears", "type": "input", "label": "Years of Experience", "placeholder": "Enter number of years", "isRequired": true, "colSpan": 1, "showIf": {"field": "hasExperience", "operator": "equals", "value": true}, "validation": [{"type": "required", "message": "Please enter your years of experience"}, {"type": "regex", "pattern": "^[0-9]+$", "message": "Please enter a valid number"}]}, {"name": "startDate", "type": "datetime", "label": "Preferred Start Date & Time", "isRequired": false, "colSpan": 1, "includeTime": true, "includeSeconds": false}]}, {"id": "documents", "title": "Documents & Agreement", "description": "Upload any supporting documents and review our terms", "columns": 1, "fields": [{"name": "profilePicture", "type": "upload", "label": "Profile Picture", "isRequired": false, "colSpan": 1, "accept": "image/*", "multiple": false, "maxFiles": 1, "maxSize": 5, "helperText": "Upload a profile picture (JPG, PNG, max 5MB)"}, {"name": "documents", "type": "upload", "label": "Supporting Documents", "isRequired": false, "colSpan": 1, "accept": ".pdf,.doc,.docx", "multiple": true, "maxFiles": 3, "maxSize": 10, "helperText": "Upload resume, certificates, or other relevant documents (PDF, DOC, DOCX, max 10MB each, up to 3 files)"}, {"name": "terms", "type": "checkbox", "label": "I agree to the Terms of Service and understand the conditions", "isRequired": true, "colSpan": 1, "validation": [{"type": "isTrue", "message": "You must agree to the Terms of Service to continue"}]}, {"name": "privacy", "type": "checkbox", "label": "I consent to the collection and processing of my personal data as described in the Privacy Policy", "isRequired": true, "colSpan": 1, "validation": [{"type": "isTrue", "message": "You must consent to our Privacy Policy to continue"}]}]}]}