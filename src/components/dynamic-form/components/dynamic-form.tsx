'use client'

import { use<PERSON><PERSON>back, useMemo, useState } from 'react'

import { zodResolver } from '@hookform/resolvers/zod'
import { useTranslations } from 'next-intl'
import { useForm } from 'react-hook-form'

import { cn } from '@/lib/utils'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Form } from '@/components/ui/form'

import { DynamicFormProps, FormData, FormStepperState } from '../types'
import { useConditionalFields } from '../utils/conditional-fields'
import {
  generateNestedDefaultValues,
  generateNestedFormSchema,
  generateStepSchema,
  validateFormData,
} from '../utils/dynamic-validation'
import { FieldRenderer } from './field-renderer'
import { StepNavigationButtons, StepperNavigation } from './stepper-navigation'

/**
 * DynamicForm - Main orchestrator component for both single-step and multi-step forms
 *
 * This component manages form state with react-hook-form, handles step navigation,
 * integrates dynamic Zod validation, manages conditional field display, and
 * coordinates between all sub-components. Automatically adapts UI for single vs multi-step forms.
 */
export function DynamicForm({
  config,
  callbacks,
  className,
  initialData = {},
  disabled = false,
  navigation: CustomNavigation,
}: DynamicFormProps) {
  const t = useTranslations()

  // Form stepper state
  const [state, setState] = useState<FormStepperState>({
    currentStep: 0,
    completedSteps: new Set(),
    stepValidation: {},
    isSubmitting: false,
    hasSubmitted: false,
  })

  // Generate default values for all fields using nested structure
  const defaultValues = useMemo(() => {
    return generateNestedDefaultValues(config.steps, initialData)
  }, [config.steps, initialData])

  // Generate form schema
  const formSchema = useMemo(() => {
    return generateNestedFormSchema(config.steps)
  }, [config.steps])

  // Initialize form
  const form = useForm<FormData>({
    resolver: zodResolver(formSchema),
    defaultValues,
    mode: 'onChange',
  })

  const formData = form.getValues()

  // Get current step configuration
  const currentStepConfig = config.steps[state.currentStep]
  const currentStepFields = currentStepConfig?.fields || []

  // Use conditional fields hook for current step with nested form data
  const { visibleFields } = useConditionalFields(currentStepFields, form, currentStepConfig.id)

  // Validate current step
  const validateCurrentStep = useCallback(async () => {
    const stepSchema = generateStepSchema(visibleFields)

    // Extract section data for validation
    const sectionData = formData[currentStepConfig.id] || {}
    const result = await validateFormData(sectionData, stepSchema)

    setState((prev) => ({
      ...prev,
      stepValidation: {
        ...prev.stepValidation,
        [state.currentStep]: {
          isValid: result.success,
          errors: result.errors || {},
        },
      },
    }))

    // Trigger form validation for current step fields using nested field names
    const fieldNames = visibleFields.map((field) => `${currentStepConfig.id}.${field.name}`)
    await form.trigger(fieldNames as string[])

    return result.success
  }, [visibleFields, form, state.currentStep, currentStepConfig.id, formData])

  // Navigation handlers
  const goToStep = useCallback(
    async (stepIndex: number) => {
      if (stepIndex < 0 || stepIndex >= config.steps.length) {
        return
      }

      // Validate current step before moving if validation is enabled
      if (config.validateOnStepChange && stepIndex > state.currentStep) {
        const isValid = await validateCurrentStep()
        if (!isValid) {
          return
        }
      }

      // Mark current step as completed if moving forward
      const newCompletedSteps = new Set(state.completedSteps)
      if (stepIndex > state.currentStep) {
        newCompletedSteps.add(state.currentStep)
      }

      setState((prev) => ({
        ...prev,
        currentStep: stepIndex,
        completedSteps: newCompletedSteps,
      }))

      // Call step change callback
      const direction = stepIndex > state.currentStep ? 'next' : 'previous'
      if (callbacks.onStepChange) {
        const currentSectionData = formData[currentStepConfig.id] || {}
        callbacks.onStepChange({
          currentStepData: currentSectionData,
          currentStepId: currentStepConfig.id,
          step: stepIndex,
          direction,
        })
      }
    },
    [
      formData,
      currentStepConfig.id,
      config.validateOnStepChange,
      config.steps.length,
      state.currentStep,
      state.completedSteps,
      validateCurrentStep,
      callbacks,
    ]
  )

  const nextStep = useCallback(() => {
    goToStep(state.currentStep + 1)
  }, [goToStep, state.currentStep])

  const previousStep = useCallback(() => {
    goToStep(state.currentStep - 1)
  }, [goToStep, state.currentStep])

  // Form submission
  const submitForm = useCallback(async () => {
    setState((prev) => ({ ...prev, isSubmitting: true }))

    try {
      // First trigger react-hook-form validation
      const isFormValid = await form.trigger()

      if (!isFormValid) {
        // Form validation failed, errors are already set by react-hook-form
        setState((prev) => ({ ...prev, isSubmitting: false }))
        return
      }

      const result = await validateFormData(formData, formSchema)

      if (!result.success) {
        // Set errors in react-hook-form state
        if (result.errors) {
          Object.entries(result.errors).forEach(([fieldName, messages]) => {
            form.setError(fieldName as keyof FormData, {
              type: 'manual',
              message: messages[0], // Use first error message
            })
          })
        }

        // Find first step with errors and navigate to it
        for (let i = 0; i < config.steps.length; i++) {
          const step = config.steps[i]
          const stepFields = step.fields
          const hasStepErrors = stepFields.some((field) => {
            const nestedFieldName = `${step.id}.${field.name}`
            return result.errors && result.errors[nestedFieldName]
          })

          if (hasStepErrors) {
            await goToStep(i)
            break
          }
        }
        setState((prev) => ({ ...prev, isSubmitting: false }))
        return
      }

      // Mark all steps as completed
      const allStepsCompleted = new Set(Array.from({ length: config.steps.length }, (_, i) => i))
      setState((prev) => ({
        ...prev,
        completedSteps: allStepsCompleted,
        hasSubmitted: true,
      }))

      // Call submission callback
      await callbacks.onSubmit(result.data)
    } catch (error) {
      console.error('Form submission error:', error)
    } finally {
      setState((prev) => ({ ...prev, isSubmitting: false }))
    }
  }, [form, formSchema, config.steps, goToStep, callbacks])

  // Grid layout class for current step
  const gridClass = useMemo(() => {
    const columns = currentStepConfig?.columns || 1
    const gridClasses: Record<number, string> = {
      1: 'grid-cols-1',
      2: 'grid-cols-1 md:grid-cols-2',
      3: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3',
      4: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-4',
      6: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6',
      12: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-12',
    }
    return gridClasses[columns] || 'grid-cols-1'
  }, [currentStepConfig?.columns])

  // Determine if stepper should be shown
  const shouldShowStepper = useMemo(() => {
    // If showStepper is explicitly set, use that value
    if (config.showStepper !== undefined) {
      return config.showStepper
    }
    // Otherwise, show stepper only for multi-step forms
    return config.steps.length > 1
  }, [config.showStepper, config.steps.length])

  // Determine if this is effectively a single-step form (no stepper shown)
  const isSingleStepForm = !shouldShowStepper

  console.log('STATE', state)

  return (
    <div className={cn('w-full  mx-auto', className)}>
      <Form {...form}>
        <form className="space-y-6">
          {/* Stepper Navigation - only show for multi-step forms or when explicitly enabled */}
          {shouldShowStepper && (
            <StepperNavigation
              steps={config.steps}
              currentStep={state.currentStep}
              completedSteps={state.completedSteps}
              onStepChange={goToStep}
              allowStepNavigation={config.allowStepNavigation || false}
              showStepNumbers={config.showStepNumbers !== false}
            />
          )}

          {/* Current Step Content */}
          <Card>
            <CardHeader>
              {/* Show title in card header only when stepper is hidden to avoid duplication */}
              <CardTitle>{t(currentStepConfig?.title || '')}</CardTitle>
              {/* Always show description if available */}
              {currentStepConfig?.description && (
                <p className="text-sm text-muted-foreground">{t(currentStepConfig.description)}</p>
              )}
            </CardHeader>
            <CardContent>
              <div className={cn('grid gap-4', gridClass)}>
                {visibleFields.map((field) => {
                  return (
                    <FieldRenderer
                      key={field.name}
                      field={field}
                      form={form}
                      sectionId={currentStepConfig.id}
                      disabled={disabled || state.isSubmitting}
                    />
                  )
                })}
              </div>
            </CardContent>
          </Card>

          {/* Navigation Buttons - custom or default */}
          {CustomNavigation ? (
            <CustomNavigation
              currentStep={state.currentStep}
              totalSteps={config.steps.length}
              isLastStep={state.currentStep === config.steps.length - 1}
              isSubmitting={state.isSubmitting}
              nextStepDisabled={state.currentStep === config.steps.length - 1}
              submitButtonDisabled={false}
              previousStepDisabled={state.currentStep === 0}
              showSubmitButton={false}
              isFirstStep={state.currentStep === 0}
              onNext={nextStep}
              onPrevious={previousStep}
              onSubmit={submitForm}
              nextButtonText={config.nextButtonText}
              previousButtonText={config.previousButtonText}
              submitButtonText={config.submitButtonText}
            />
          ) : config.showNavigationButtons !== false ? (
            <StepNavigationButtons
              currentStep={state.currentStep}
              totalSteps={config.steps.length}
              onPrevious={previousStep}
              onNext={nextStep}
              onSubmit={submitForm}
              isSubmitting={state.isSubmitting}
              canGoNext={true} // TODO: Add step validation check
              canGoPrevious={state.currentStep > 0}
              nextButtonText={config.nextButtonText}
              previousButtonText={config.previousButtonText}
              submitButtonText={config.submitButtonText}
              isSingleStepForm={isSingleStepForm}
            />
          ) : null}
        </form>
      </Form>
    </div>
  )
}
