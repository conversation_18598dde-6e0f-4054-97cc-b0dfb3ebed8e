'use client'

import React from 'react'

import { Check, ChevronRight } from 'lucide-react'
import { useTranslations } from 'next-intl'

import { cn } from '@/lib/utils'
import { Button } from '@/components/ui/button'

import { StepperNavigationProps } from '../types'

/**
 * StepperNavigation component for multi-step forms
 *
 * Provides step indicators, progress tracking, and navigation controls
 * following the existing UI patterns from the project.
 */
export function StepperNavigation({
  steps,
  currentStep,
  completedSteps,
  onStepChange,
  allowStepNavigation,
  showStepNumbers,
  className,
}: StepperNavigationProps) {
  return (
    <div className={cn('w-full', className)}>
      {/* Step Indicators */}
      <div className="flex items-center justify-between mb-6">
        {steps.map((step, index) => {
          const isCompleted = completedSteps.has(index)
          const isCurrent = index === currentStep
          const isClickable = allowStepNavigation && (isCompleted || index <= currentStep)

          return (
            <React.Fragment key={step.id}>
              {/* Step Indicator */}
              <div className="flex flex-col items-center">
                <Button
                  variant="ghost"
                  size="sm"
                  className={cn(
                    'w-10 h-10 rounded-full border-2 p-0 transition-all duration-200 dark:border-muted-foreground disabled:opacity-100',
                    {
                      'border-primary bg-primary text-primary-foreground': isCurrent,
                      'border-muted-foreground bg-background text-muted-foreground': !isCurrent && !isCompleted,
                      'hover:border-primary hover:bg-primary/10': isClickable && !isCurrent,
                      'cursor-pointer': isClickable,
                      'cursor-default': !isClickable,
                    }
                  )}
                  onClick={() => isClickable && onStepChange(index)}
                  disabled={!isClickable}
                >
                  {isCompleted ? (
                    <Check className="w-4 h-4" />
                  ) : showStepNumbers ? (
                    <span className="text-sm font-medium">{index + 1}</span>
                  ) : (
                    <div className="w-2 h-2 rounded-full bg-current" />
                  )}
                </Button>
              </div>

              {/* Connector Line */}
              {index < steps.length - 1 && (
                <div className="flex-1 mx-2">
                  <div
                    className={cn('h-0.5 transition-all duration-300', {
                      'bg-primary': completedSteps.has(index),
                      'bg-muted': !completedSteps.has(index),
                    })}
                  />
                </div>
              )}
            </React.Fragment>
          )
        })}
      </div>
    </div>
  )
}

/**
 * Compact version of stepper navigation for smaller screens
 */
export function CompactStepperNavigation({
  steps,
  currentStep,
  className,
}: Omit<StepperNavigationProps, 'onStepChange' | 'allowStepNavigation' | 'showStepNumbers'>) {
  return (
    <div className={cn('w-full', className)}>
      {/* Current Step Info */}
      <div className="text-center mb-4">
        <h2 className="text-lg font-semibold mb-1">{steps[currentStep]?.title}</h2>
        {steps[currentStep]?.description && (
          <p className="text-sm text-muted-foreground">{steps[currentStep].description}</p>
        )}
      </div>
    </div>
  )
}

/**
 * Step navigation buttons component
 */
export function StepNavigationButtons({
  currentStep,
  totalSteps,
  onPrevious,
  onNext,
  onSubmit,
  isSubmitting = false,
  canGoNext = true,
  canGoPrevious = true,
  nextButtonText = 'Next',
  previousButtonText = 'Previous',
  submitButtonText = 'Submit',
  isSingleStepForm = false,
  className,
}: {
  currentStep: number
  totalSteps: number
  onPrevious: () => void
  onNext: () => void
  onSubmit: () => void
  isSubmitting?: boolean
  canGoNext?: boolean
  canGoPrevious?: boolean
  nextButtonText?: string
  previousButtonText?: string
  submitButtonText?: string
  isSingleStepForm?: boolean
  className?: string
}) {
  const t = useTranslations()
  const isLastStep = currentStep === totalSteps - 1
  const isFirstStep = currentStep === 0

  return (
    <div
      className={cn(
        'flex items-center pt-6',
        {
          'justify-between border-t': !isSingleStepForm,
          'justify-end': isSingleStepForm,
        },
        className
      )}
    >
      {/* Previous Button - hide completely for single-step forms */}
      {!isSingleStepForm && (
        <Button
          type="button"
          variant="outline"
          onClick={onPrevious}
          disabled={isFirstStep || !canGoPrevious || isSubmitting}
          className="min-w-24"
        >
          {t(previousButtonText)}
        </Button>
      )}

      {/* Next/Submit Button */}
      {isLastStep ? (
        <Button
          type="button"
          onClick={onSubmit}
          disabled={!canGoNext || isSubmitting}
          isLoading={isSubmitting}
          loadingText={t('FORM_STEPPER.SUBMITTING')}
          className={cn('min-w-24', {
            'w-full md:w-auto': isSingleStepForm,
          })}
        >
          {t(submitButtonText)}
        </Button>
      ) : (
        <Button type="button" onClick={onNext} disabled={!canGoNext || isSubmitting} className="min-w-24">
          {t(nextButtonText)}
          <ChevronRight className="w-4 h-4 ml-1" />
        </Button>
      )}
    </div>
  )
}

/**
 * Mobile-friendly step navigation
 */
export function MobileStepNavigation({
  steps,
  currentStep,
  completedSteps,
  className,
}: Pick<StepperNavigationProps, 'steps' | 'currentStep' | 'completedSteps' | 'className'>) {
  const totalSteps = steps.length
  const progressPercentage = ((currentStep + 1) / totalSteps) * 100

  return (
    <div className={cn('w-full', className)}>
      {/* Mobile Progress Dots */}
      <div className="flex justify-center items-center space-x-2 mb-4">
        {steps.map((_, index) => {
          const isCompleted = completedSteps.has(index)
          const isCurrent = index === currentStep

          return (
            <div
              key={index}
              className={cn('w-2 h-2 rounded-full transition-all duration-200', {
                'bg-primary': isCurrent || isCompleted,
                'bg-muted': !isCurrent && !isCompleted,
                'scale-125': isCurrent,
              })}
            />
          )
        })}
      </div>

      {/* Progress Bar */}
      <div className="w-full bg-muted rounded-full h-1 mb-4">
        <div
          className="bg-primary h-1 rounded-full transition-all duration-300 ease-in-out"
          style={{ width: `${progressPercentage}%` }}
        />
      </div>

      {/* Current Step Info */}
      <div className="text-center">
        <p className="text-xs text-muted-foreground mb-1">
          Step {currentStep + 1} of {totalSteps}
        </p>
        <h3 className="text-sm font-medium">{steps[currentStep]?.title}</h3>
      </div>
    </div>
  )
}
