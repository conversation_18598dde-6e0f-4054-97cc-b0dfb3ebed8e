'use client'

import { useMemo } from 'react'

import { UseFormReturn, useWatch } from 'react-hook-form'

import { ComplexCondition, Condition, FieldConfig, FormData } from '../types'

/**
 * Conditional field display logic for form stepper
 *
 * Handles field visibility based on other field values with support for
 * various condition types and complex boolean logic.
 */

/**
 * Evaluate a single condition against form data
 */
export function evaluateCondition(condition: Condition, formData: FormData): boolean {
  const fieldValue = formData[condition.field]
  const conditionValue = condition.value

  switch (condition.operator) {
    case 'equals':
      return fieldValue === conditionValue

    case 'notEquals':
      return fieldValue !== conditionValue

    case 'contains':
      if (typeof fieldValue === 'string' && typeof conditionValue === 'string') {
        return fieldValue.includes(conditionValue)
      }
      if (Array.isArray(fieldValue) && conditionValue !== undefined) {
        return fieldValue.includes(conditionValue)
      }
      return false

    case 'notContains':
      if (typeof fieldValue === 'string' && typeof conditionValue === 'string') {
        return !fieldValue.includes(conditionValue)
      }
      if (Array.isArray(fieldValue) && conditionValue !== undefined) {
        return !fieldValue.includes(conditionValue)
      }
      return true

    case 'greaterThan':
      if (typeof fieldValue === 'number' && typeof conditionValue === 'number') {
        return fieldValue > conditionValue
      }
      if (fieldValue instanceof Date && conditionValue instanceof Date) {
        return fieldValue > conditionValue
      }
      return false

    case 'lessThan':
      if (typeof fieldValue === 'number' && typeof conditionValue === 'number') {
        return fieldValue < conditionValue
      }
      if (fieldValue instanceof Date && conditionValue instanceof Date) {
        return fieldValue < conditionValue
      }
      return false

    case 'greaterThanOrEqual':
      if (typeof fieldValue === 'number' && typeof conditionValue === 'number') {
        return fieldValue >= conditionValue
      }
      if (fieldValue instanceof Date && conditionValue instanceof Date) {
        return fieldValue >= conditionValue
      }
      return false

    case 'lessThanOrEqual':
      if (typeof fieldValue === 'number' && typeof conditionValue === 'number') {
        return fieldValue <= conditionValue
      }
      if (fieldValue instanceof Date && conditionValue instanceof Date) {
        return fieldValue <= conditionValue
      }
      return false

    case 'isEmpty':
      if (fieldValue === null || fieldValue === undefined) {
        return true
      }
      if (typeof fieldValue === 'string') {
        return fieldValue.trim() === ''
      }
      if (Array.isArray(fieldValue)) {
        return fieldValue.length === 0
      }
      return false

    case 'isNotEmpty':
      if (fieldValue === null || fieldValue === undefined) {
        return false
      }
      if (typeof fieldValue === 'string') {
        return fieldValue.trim() !== ''
      }
      if (Array.isArray(fieldValue)) {
        return fieldValue.length > 0
      }
      return true

    default:
      console.warn(`Unknown condition operator: ${condition.operator}`)
      return false
  }
}

/**
 * Evaluate a complex condition with logical operators
 */
export function evaluateComplexCondition(condition: ComplexCondition, formData: FormData): boolean {
  const { operator, conditions } = condition

  if (conditions.length === 0) {
    return true
  }

  const results = conditions.map((cond) => {
    if ('operator' in cond && 'conditions' in cond) {
      // It's a complex condition
      return evaluateComplexCondition(cond, formData)
    } else {
      // It's a simple condition
      return evaluateCondition(cond, formData)
    }
  })

  switch (operator) {
    case 'and':
      return results.every(Boolean)

    case 'or':
      return results.some(Boolean)

    default:
      console.warn(`Unknown logical operator: ${operator}`)
      return false
  }
}

/**
 * Check if a field should be visible based on its showIf condition
 */
export function isFieldVisible(field: FieldConfig, formData: FormData): boolean {
  if (!field.showIf) {
    return true
  }

  if ('operator' in field.showIf && 'conditions' in field.showIf) {
    // It's a complex condition
    return evaluateComplexCondition(field.showIf, formData)
  } else {
    // It's a simple condition
    return evaluateCondition(field.showIf, formData)
  }
}

/**
 * Filter visible fields from a list of fields
 */
export function getVisibleFields(fields: FieldConfig[], formData: FormData): FieldConfig[] {
  return fields.filter((field) => isFieldVisible(field, formData))
}

/**
 * Get all field dependencies for a given field
 */
export function getFieldDependencies(field: FieldConfig): string[] {
  const dependencies: string[] = []

  if (!field.showIf) {
    return dependencies
  }

  function extractDependencies(condition: Condition | ComplexCondition) {
    if ('operator' in condition && 'conditions' in condition) {
      // Complex condition
      condition.conditions.forEach(extractDependencies)
    } else {
      // Simple condition
      dependencies.push(condition.field)
    }
  }

  extractDependencies(field.showIf)
  return [...new Set(dependencies)] // Remove duplicates
}

/**
 * Get all dependencies for a list of fields
 */
export function getAllFieldDependencies(fields: FieldConfig[]): Record<string, string[]> {
  const dependencies: Record<string, string[]> = {}

  for (const field of fields) {
    dependencies[field.name] = getFieldDependencies(field)
  }

  return dependencies
}

/**
 * React hook for conditional field visibility
 */
export function useConditionalFields<TFieldValues extends FormData = FormData>(
  fields: FieldConfig[],
  form: UseFormReturn<TFieldValues>,
  sectionId?: string
) {
  // Get all field names that have dependencies
  const dependentFieldNames = useMemo(() => {
    const names = new Set<string>()
    fields.forEach((field) => {
      getFieldDependencies(field).forEach((dep) => names.add(dep))
    })
    return Array.from(names)
  }, [fields])

  // Watch all dependent fields - use nested paths if sectionId is provided
  const watchedFieldNames = useMemo(() => {
    if (sectionId) {
      return dependentFieldNames.map((name) => `${sectionId}.${name}`)
    }
    return dependentFieldNames
  }, [dependentFieldNames, sectionId])

  const watchedValues = useWatch({
    control: form.control,
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    name: watchedFieldNames as any,
  })

  // Create form data object for evaluation
  const formData = useMemo(() => {
    const data: FormData = {}

    if (sectionId) {
      // For nested structure, extract values from the section
      const allFormValues = form.getValues()
      const sectionData = allFormValues[sectionId] || {}

      dependentFieldNames.forEach((name) => {
        data[name] = sectionData[name]
      })
    } else {
      // For flat structure (backward compatibility)
      dependentFieldNames.forEach((name, index) => {
        data[name] = Array.isArray(watchedValues) ? watchedValues[index] : watchedValues
      })
    }

    return data
  }, [dependentFieldNames, watchedValues, sectionId, form])

  // Calculate visible fields
  const visibleFields = useMemo(() => {
    return getVisibleFields(fields, formData)
  }, [fields, formData])

  // Helper functions
  const isFieldVisibleByName = useMemo(() => {
    return (fieldName: string) => {
      const field = fields.find((f) => f.name === fieldName)
      if (!field) return false
      return isFieldVisible(field, formData)
    }
  }, [fields, formData])

  const evaluateConditionMemo = useMemo(() => {
    return (condition: Condition | ComplexCondition) => {
      if ('operator' in condition && 'conditions' in condition) {
        return evaluateComplexCondition(condition, formData)
      } else {
        return evaluateCondition(condition, formData)
      }
    }
  }, [formData])

  return {
    visibleFields,
    isFieldVisible: isFieldVisibleByName,
    evaluateCondition: evaluateConditionMemo,
    formData,
  }
}

/**
 * Validate conditional field configuration
 */
export function validateConditionalConfig(fields: FieldConfig[]): string[] {
  const errors: string[] = []
  const fieldNames = new Set(fields.map((f) => f.name))

  for (const field of fields) {
    if (!field.showIf) continue

    const dependencies = getFieldDependencies(field)

    for (const dependency of dependencies) {
      if (!fieldNames.has(dependency)) {
        errors.push(`Field '${field.name}' has a condition that depends on non-existent field '${dependency}'`)
      }
    }

    // Check for circular dependencies
    if (dependencies.includes(field.name)) {
      errors.push(`Field '${field.name}' has a circular dependency on itself`)
    }
  }

  return errors
}

/**
 * Helper functions for creating common conditions
 */
export const conditionHelpers = {
  equals: (field: string, value: string | number | boolean): Condition => ({
    field,
    operator: 'equals',
    value,
  }),

  notEquals: (field: string, value: string | number | boolean): Condition => ({
    field,
    operator: 'notEquals',
    value,
  }),

  contains: (field: string, value: string): Condition => ({
    field,
    operator: 'contains',
    value,
  }),

  isEmpty: (field: string): Condition => ({
    field,
    operator: 'isEmpty',
  }),

  isNotEmpty: (field: string): Condition => ({
    field,
    operator: 'isNotEmpty',
  }),

  greaterThan: (field: string, value: number): Condition => ({
    field,
    operator: 'greaterThan',
    value,
  }),

  lessThan: (field: string, value: number): Condition => ({
    field,
    operator: 'lessThan',
    value,
  }),

  and: (...conditions: (Condition | ComplexCondition)[]): ComplexCondition => ({
    operator: 'and',
    conditions,
  }),

  or: (...conditions: (Condition | ComplexCondition)[]): ComplexCondition => ({
    operator: 'or',
    conditions,
  }),
}
