'use client'

import React, { useRef, useState, useTransition } from 'react'

import { ChevronLeft, ChevronRight, ChevronsLeft, ChevronsRight, RotateCcw } from 'lucide-react'
import { useQueryStates } from 'nuqs'
import { useDebounceCallback } from 'usehooks-ts'

import { cn } from '@/lib/utils'

import { paginationSearchParams } from './pagination-search-params'

interface PaginationControlsProps {
  totalPages: number
  totalItems: number
  currentPage?: number
  itemsPerPage?: number
  className?: string
}

export function PaginationControls({
  totalPages,
  totalItems,
  currentPage: propCurrentPage,
  itemsPerPage = 10,
  className,
}: PaginationControlsProps) {
  const [isLoading, startTransition] = useTransition()

  const [filters, setFilters] = useQueryStates(paginationSearchParams, {
    shallow: false,
    scroll: true,
    startTransition,
  })

  const currentPage = propCurrentPage || filters.page
  const [inputError, setInputError] = useState<string>('')
  const inputRef = useRef<HTMLInputElement>(null)

  // Navigation helper function
  const navigateToPage = (pageNumber: number) => {
    if (pageNumber !== currentPage) {
      handlePageChange(pageNumber)
    }
  }
  const debouncedPageChange = useDebounceCallback(navigateToPage, 500)

  // Validation helper function
  const validatePageNumber = (value: string): string | null => {
    if (value === '') {
      return null // Empty is not an error
    }

    const pageNumber = parseInt(value)

    if (isNaN(pageNumber)) {
      return 'Enter a valid number'
    }

    if (pageNumber < 1) {
      return 'Page must be at least 1'
    }

    if (pageNumber > totalPages) {
      return `Page must be ${totalPages} or less`
    }

    return null // Valid
  }

  // Don't render pagination if there's only one page
  if (totalPages <= 1) {
    return null
  }

  // Handle page change by updating URL state
  const handlePageChange = async (newPage: number) => {
    await setFilters({ page: newPage })
  }

  // Handle input change and navigation
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value

    const error = validatePageNumber(value)
    setInputError(error || '')

    // Navigate if valid
    if (!error && value !== '') {
      const pageNumber = parseInt(value)
      debouncedPageChange(pageNumber)
    }
  }

  const handleInputKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      const value = e.currentTarget.value
      const error = validatePageNumber(value)
      setInputError(error || '')

      if (!error && value !== '') {
        const pageNumber = parseInt(value)
        navigateToPage(pageNumber)
      }
    }
  }

  const handleInputBlur = (e: React.FocusEvent<HTMLInputElement>) => {
    const value = e.currentTarget.value
    const error = validatePageNumber(value)

    if (error) {
      // Reset to current page if invalid and show error briefly
      e.currentTarget.value = currentPage.toString()
      setInputError(error)

      // Clear error after 3 seconds
      setTimeout(() => setInputError(''), 3000)
    } else if (value !== '') {
      // Valid input - navigate
      setInputError('')
      const pageNumber = parseInt(value)
      navigateToPage(pageNumber)
    } else {
      // Empty input - reset to current page
      e.currentTarget.value = currentPage.toString()
      setInputError('')
    }
  }

  // Calculate display text
  const startItem = (currentPage - 1) * itemsPerPage + 1
  const endItem = Math.min(currentPage * itemsPerPage, totalItems)

  const buttonClass =
    'w-8 h-8 flex items-center justify-center bg-white rounded shadow-sm border border-gray-200 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed cursor-pointer'

  return (
    <div className={`flex md:flex-row flex-col items-center gap-3 justify-between w-full mt-auto ${className}`}>
      {/* First Page */}
      <div className="flex items-center gap-1 flex-1">
        <button onClick={() => handlePageChange(1)} disabled={currentPage === 1} className={buttonClass}>
          <ChevronsLeft className="w-4 h-4 text-gray-600" />
        </button>

        {/* Previous Page */}
        <button onClick={() => handlePageChange(currentPage - 1)} disabled={currentPage === 1} className={buttonClass}>
          <ChevronLeft className="w-4 h-4 text-gray-600" />
        </button>

        {/* Page Info with Input */}
        <div className="flex items-center gap-1 text-sm text-gray-700 mx-2 relative">
          <span>Page</span>
          <input
            ref={inputRef}
            key={currentPage} // Force re-render when currentPage changes
            type="number"
            min="1"
            max={totalPages}
            defaultValue={currentPage}
            onChange={handleInputChange}
            onKeyDown={handleInputKeyDown}
            onBlur={handleInputBlur}
            className={cn(
              'w-10 h-8 text-center bg-white rounded shadow-sm border text-sm font-medium focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 [appearance:textfield] [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none hover:bg-gray-50 transition-colors',
              inputError ? 'border-red-500' : 'border-gray-200'
            )}
          />
          <span>of {totalPages}</span>

          {/* Error message */}
          {inputError && (
            <div className="absolute top-full left-1/2 transform -translate-x-1/2 mt-1 px-2 py-1 text-xs text-white bg-red-600 rounded shadow-lg whitespace-nowrap z-10">
              {inputError}
            </div>
          )}
        </div>

        {/* Next Page */}
        <button
          onClick={() => handlePageChange(currentPage + 1)}
          disabled={currentPage === totalPages}
          className={buttonClass}
        >
          <ChevronRight className="w-4 h-4 text-gray-600" />
        </button>

        {/* Last Page */}
        <button
          onClick={() => handlePageChange(totalPages)}
          disabled={currentPage === totalPages}
          className={buttonClass}
        >
          <ChevronsRight className="w-4 h-4 text-gray-600" />
        </button>

        {/* Refresh */}
        <button onClick={() => window.location.reload()} className={cn(buttonClass, 'ml-1')}>
          <RotateCcw className={cn('w-4 h-4 text-gray-600')} />
        </button>

        {/* Loading Spinner */}
        {isLoading && (
          <div className="w-4 h-4 border-2 border-gray-300 border-t-blue-500 rounded-full animate-spin ml-2"></div>
        )}
      </div>

      {/* Display Text */}
      <span className="text-xs md:text-sm text-gray-700 md:ml-4">
        Displaying {startItem}-{endItem} of {totalItems}
      </span>
    </div>
  )
}
