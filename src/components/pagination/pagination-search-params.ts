import { createLoader, inferParserType, parseAsInteger, parseAsString } from 'nuqs/server'

export const ITEMS_PER_PAGE = 10

// Define pagination search parameters with all filters
export const paginationSearchParams = {
  page: parseAsInteger.withOptions({ clearOnDefault: true }).withDefault(1),
  perPage: parseAsInteger.withOptions({ clearOnDefault: true }).withDefault(ITEMS_PER_PAGE),
  search: parseAsString.withOptions({ clearOnDefault: true }).withDefault(''),
  status: parseAsString.withOptions({ clearOnDefault: true }).withDefault(''),
}

export const loadPaginationSearchParams = createLoader(paginationSearchParams)

export type PaginationSearchParams = inferParserType<typeof paginationSearchParams>
