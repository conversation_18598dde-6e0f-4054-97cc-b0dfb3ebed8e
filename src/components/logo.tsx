import Image from 'next/image'

import { cn } from '@/lib/utils'

export enum LogoVariant {
  DEFAULT = 'default',
  HORIZONTAL = 'horizontal',
}

interface LogoProps {
  className?: string
  variant?: LogoVariant
}

const logoVariant = {
  [LogoVariant.DEFAULT]: 'logo-with-text.png',
  [LogoVariant.HORIZONTAL]: 'logo-horizontal.png',
}

export const Logo = ({ className, variant = LogoVariant.DEFAULT }: LogoProps) => {
  return (
    <Image
      src={`/images/${logoVariant[variant]}`}
      alt="mobilversichert"
      className={cn('object-cover w-[200px]', className)}
      width={400}
      height={200}
    />
  )
}
