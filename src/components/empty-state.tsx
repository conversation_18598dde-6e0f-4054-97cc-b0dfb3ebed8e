import React from 'react'

import Image from 'next/image'

import { cn } from '@/lib/utils'

interface EmptyStateProps {
  pageTitle: string
  actions?: React.ReactNode
  title: string
  message?: string
  className?: string
}

export const EmptyState = ({ pageTitle, actions, title, message, className }: EmptyStateProps) => {
  return (
    <div className="flex flex-col  gap-2  text-center h-full px-4 ">
      <div
        className={cn(
          'flex md:flex-row flex-col items-center justify-between mt-4',
          !actions && 'justify-center',
          className
        )}
      >
        {actions && <div className="hidden md:block" />}
        <h1 className="text-2xl font-bold text-[#111827] ">{pageTitle}</h1>
        {actions && actions}
      </div>
      <div className="flex flex-col gap-2 items-center justify-center flex-1 ">
        <Image src="/images/small-logo.png" alt="Empty state" width={40} height={40} />
        <h2 className="text-xl bg-success-foreground text-success-foreground">{title}</h2>
        {message && <p className="text-sm text-gray-500">{message}</p>}
      </div>
    </div>
  )
}
