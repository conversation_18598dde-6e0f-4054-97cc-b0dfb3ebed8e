'use client'

import { motion } from 'framer-motion'
import { Construction } from 'lucide-react'

import { cn } from '@/lib/utils'
import { Badge } from '@/components/ui/badge'
import { Card } from '@/components/ui/card'

export interface WorkInProgressProps {
  /**
   * The message to display
   */
  message?: string
  /**
   * The size of the component
   */
  size?: 'sm' | 'md' | 'lg'
  /**
   * The appearance of the component
   */
  appearance?: 'default' | 'outline' | 'minimal'
  /**
   * The animation variant to use
   */
  animationVariant?: 'pulse' | 'spin' | 'bounce' | 'wave' | 'dots'
  /**
   * The icon to display
   */
  icon?: 'construction' | 'hammer' | 'hardhat' | 'alert' | 'loader' | 'none'
  /**
   * Additional CSS classes
   */
  className?: string
}

export function WorkInProgress({
  message = 'Work in progress',
  size = 'lg',
  appearance = 'default',
  animationVariant = 'pulse',
  className,
}: WorkInProgressProps) {
  const sizeClasses = {
    sm: 'p-3 text-sm',
    md: 'p-4 text-base',
    lg: 'p-6 text-lg',
  }

  const iconSizes = {
    sm: 16,
    md: 20,
    lg: 24,
  }

  const appearanceClasses = {
    default: 'bg-background border-border shadow-sm',
    outline: 'bg-transparent border-border shadow-none',
    minimal: 'bg-transparent border-none shadow-none',
  }

  return (
    <Card
      className={cn(
        'flex flex-col items-center justify-center gap-3 rounded-lg border max-w-xl m-auto',
        sizeClasses[size],
        appearanceClasses[appearance],
        className
      )}
    >
      <Badge variant="outline" className="mb-1 font-medium">
        WIP
      </Badge>

      {animationVariant === 'pulse' && (
        <motion.div
          animate={{ scale: [1, 1.1, 1] }}
          transition={{ duration: 2, repeat: Infinity }}
          className="text-foreground"
        >
          <Construction size={iconSizes[size]} />
        </motion.div>
      )}

      {animationVariant === 'spin' && (
        <motion.div
          animate={{ rotate: 360 }}
          transition={{ duration: 2, repeat: Infinity, ease: 'linear' }}
          className="text-foreground"
        >
          <Construction size={iconSizes[size]} />
        </motion.div>
      )}

      {animationVariant === 'bounce' && (
        <motion.div
          animate={{ y: [0, -10, 0] }}
          transition={{ duration: 1.5, repeat: Infinity }}
          className="text-foreground"
        >
          <Construction size={iconSizes[size]} />
        </motion.div>
      )}

      <div className="mt-2 text-center font-medium text-foreground">{message}</div>
      <span className="sr-only">Work in progress: {message}</span>
    </Card>
  )
}
