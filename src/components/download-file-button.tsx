'use client'

import { useState } from 'react'

import { Download } from 'lucide-react'

import { Button } from './ui/button'

interface DownloadFileButtonProps {
  documentId: string
  accessToken: string
  downloadUrl?: never
}

interface DownloadDirectUrlProps {
  documentId?: never
  accessToken?: never
  downloadUrl: string
}

export const DownloadFileButton = ({
  documentId,
  accessToken,
  downloadUrl,
}: DownloadFileButtonProps | DownloadDirectUrlProps) => {
  const [isDownloading, setIsDownloading] = useState(false)

  const handleDownload = async () => {
    try {
      setIsDownloading(true)
      let url = downloadUrl || ''
      if (!downloadUrl) {
        url = `/api/download?document_id=${documentId}&access_token=${accessToken}`
      }

      // Use our local API route for download
      // Try window.location.href first (simplest approach)
      window.location.href = url
    } catch (error) {
      console.error('Download failed:', error)
    } finally {
      // Reset loading state after a short delay
      setTimeout(() => setIsDownloading(false), 1000)
    }
  }

  return (
    <Button
      variant="ghost"
      size="sm"
      className="h-8 w-8 p-0 text-[#6B7280] hover:text-[#3BCBBF] hover:bg-[#3BCBBF]/10"
      onClick={handleDownload}
      disabled={isDownloading}
    >
      <Download className={`w-4 h-4 ${isDownloading ? 'animate-pulse' : ''}`} />
      <span className="sr-only">Download</span>
    </Button>
  )
}
