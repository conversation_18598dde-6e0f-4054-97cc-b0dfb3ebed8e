'use client'

import { ArrowLeft } from 'lucide-react'
import { useTranslations } from 'next-intl'
import { useRouter } from 'next/navigation'

import { cn } from '@/lib/utils'

import { Button } from './ui/button'

interface NavigationBackButtonProps {
  /** Custom content to render instead of default back text/icon */
  children?: React.ReactNode
  /** Additional CSS classes */
  className?: string
  /** Custom title text to display */
  title?: string
  /** Show only icon without text */
  onlyIcon?: boolean
  /** URL to navigate to when no browser history exists */
  fallbackUrl: string
}

export const NavigationBackButton = ({
  children,
  className,
  title,
  onlyIcon = false,
  fallbackUrl,
}: NavigationBackButtonProps) => {
  const router = useRouter()
  const t = useTranslations()

  // Smart navigation with fallback logic
  const handleNavigation = () => {
    // Check if we can go back in browser history

    if (fallbackUrl) {
      router.push(fallbackUrl)
      return
    }

    if (typeof window !== 'undefined') {
      const hasHistory = window.history.length > 1
      const hasReferrer = Boolean(document.referrer && document.referrer !== window.location.href)

      if (hasHistory || hasReferrer) {
        router.back()
        return
      }
    }

    // Navigate to fallback URL when no history
  }

  const ComponentToRender = title ? (
    <span>{title}</span>
  ) : children ? (
    children
  ) : onlyIcon ? (
    <ArrowLeft className="w-6 h-6 text-[#142A3A] stroke-[1.5]" />
  ) : (
    <span>{t('COMMON.NAVIGATION.BACK', { defaultValue: 'Back' })}</span>
  )

  return (
    <Button
      variant={onlyIcon ? 'ghost' : 'outline'}
      className={cn('cursor-pointer', onlyIcon ? 'w-6 h-6 p-0 hover:bg-transparent' : 'rounded-full', className)}
      onClick={handleNavigation}
    >
      {ComponentToRender}
    </Button>
  )
}
