'use client'

import { memo, useCallback, useRef, useState } from 'react'

import { UserProfile } from '@/modules/auth/types/auth-types'
import { useSignatureUpload } from '@/modules/expert/hooks/use-signature-upload'
import { useTranslations } from 'next-intl'
import SignatureCanvas from 'react-signature-canvas'
import { toast } from 'sonner'

import { Button } from '@/components/ui/button'
import { Dialog, DialogContent, DialogTitle, DialogTrigger } from '@/components/ui/dialog'

export function POAAcceptanceModal({
  profile,
  template,
  apiUrl,
}: {
  profile: UserProfile
  template?: string
  accessToken?: string
  apiUrl: string
}) {
  const t = useTranslations()
  const signaturePadRef = useRef<SignatureCanvas>(null)
  const [error, setError] = useState<string | null>(null)
  const [isOpen, setIsOpen] = useState(false)

  const { uploadSignature, isLoading } = useSignatureUpload(profile.id, apiUrl)

  const handleSignatureSend = async () => {
    if (!template) return
    if (!signaturePadRef.current || signaturePadRef.current.isEmpty()) {
      setError('Please provide a signature.')
      return
    }

    setError(null)
    try {
      const signatureDataUrl = signaturePadRef.current.toDataURL('image/png')
      await uploadSignature({
        signature: signatureDataUrl,
        userId: profile.id,
        template,
      })
      signaturePadRef.current.clear()
      closeDialog()
    } catch {
      toast.error(t('FAILED.MESSAGE'))
    }
  }

  const handleClearSignature = () => {
    signaturePadRef.current?.clear()
    setError(null)
  }
  const closeDialog = useCallback(() => {
    setIsOpen(false)
  }, [])

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button>{t('EXPERT.AGREEMENT_BUTTON_TITLE')}</Button>
      </DialogTrigger>
      <DialogContent className="max-h-[80vh] max-w-[80vw]">
        <div className="text-center space-y-4">
          <DialogTitle className="text-xl font-semibold text-gray-900">
            {t('EXPERT.ACCEPTANCE_AGREEMENT.TITLE')}
          </DialogTitle>
        </div>

        <TemplateParsed template={template} />

        <Dialog>
          <DialogTrigger>
            <div className="absolute items-center justify-center pt-6 bottom-4 left-1/2 transform -translate-x-1/2">
              <Button className="px-8 py-2" disabled={false}>
                {t('EXPERT.AGREEMENT_SIGN')}
              </Button>
            </div>
          </DialogTrigger>
          <DialogContent className="max-w-lg">
            <DialogTitle>{t('EXPERT.SIGNATURE_MODAL_TITLE', { defaultValue: 'Provide your signature' })}</DialogTitle>
            <div className="flex flex-col items-center gap-4">
              <SignatureCanvas
                ref={signaturePadRef}
                penColor="black"
                canvasProps={{ width: 400, height: 200, className: 'border rounded bg-white' }}
              />
              {error && <div className="text-red-500 text-sm">{error}</div>}
              <div className="flex gap-2">
                <Button onClick={handleClearSignature} variant="secondary">
                  {t('EXPERT.CLEAR_SIGNATURE', { defaultValue: 'Clear' })}
                </Button>
                <Button onClick={handleSignatureSend} disabled={isLoading}>
                  {isLoading
                    ? t('COMMON.PROCESSING', { defaultValue: 'Sending...' })
                    : t('EXPERT.SEND_SIGNATURE', { defaultValue: 'Send Signature' })}
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      </DialogContent>
    </Dialog>
  )
}

const TemplateParsed = memo(({ template }: { template?: string }) => {
  if (!template) return null
  return (
    <div className={'overflow-y-auto max-h-[calc(80vh-100px)] pb-12'}>
      {template && <div className="mt-4 text-left" dangerouslySetInnerHTML={{ __html: template }} />}
    </div>
  )
})

TemplateParsed.displayName = 'TemplateParsed'
