'use client'

import { NextIntlClientProvider } from 'next-intl'
import { NuqsAdapter } from 'nuqs/adapters/next/app'

import { Toaster } from '@/components/ui/sonner'

interface ProvidersProps {
  children: React.ReactNode
  locale: string

  messages?: Record<string, string>
}

export default function Providers({ children, locale, messages }: ProvidersProps) {
  return (
    <NextIntlClientProvider locale={locale} messages={messages} timeZone="Europe/Berlin" onError={() => {}}>
      <NuqsAdapter>
        {children}
        <Toaster
          toastOptions={{
            classNames: {
              description: '!text-gray-500',
            },
          }}
        />
      </NuqsAdapter>
    </NextIntlClientProvider>
  )
}
