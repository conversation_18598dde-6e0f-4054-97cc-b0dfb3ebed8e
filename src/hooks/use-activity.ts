'use client'

import { useCallback, useEffect, useState } from 'react'

import { focusManager } from '@/lib/change-managers/focus-manager'
import { notifyManager } from '@/lib/change-managers/notify-manager'
import { onlineManager } from '@/lib/change-managers/online-manager'

export interface SessionActivityOptions {
  enabled?: boolean
  debug?: boolean
  onActivityDetected?: (source: ActivitySource) => void
}

export type ActivitySource = 'focus' | 'online'

export interface SessionActivityState {
  isFocused: boolean
  isOnline: boolean
  lastActivity: {
    source: ActivitySource
    timestamp: number
  } | null
}

export function useActivity(options: SessionActivityOptions = {}): SessionActivityState {
  const { enabled = true, debug = false, onActivityDetected } = options

  // State management
  const [isFocused, setIsFocused] = useState(() => focusManager.isFocused())
  const [isOnline, setIsOnline] = useState(() => onlineManager.isOnline())
  const [lastActivity, setLastActivity] = useState<{
    source: ActivitySource
    timestamp: number
  } | null>(null)

  const log = useCallback(
    (message: string, source?: string) => {
      if (debug) {
        console.log(`[SessionActivity${source ? ` - ${source}` : ''}] ${message}`)
      }
    },
    [debug]
  )

  const triggerActivity = useCallback(
    (source: ActivitySource, details?: string) => {
      const timestamp = Date.now()
      setLastActivity({ source, timestamp })
      log(`Activity detected: ${source}${details ? ` (${details})` : ''}`, source)
      onActivityDetected?.(source)
    },
    [log, onActivityDetected]
  )

  // Subscribe to FocusManager
  useEffect(() => {
    if (!enabled) return

    log('Subscribing to FocusManager')

    const unsubscribe = focusManager.subscribe((focused) => {
      log(`Focus changed: ${focused ? 'focused' : 'blurred'}`)
      setIsFocused(focused)

      if (focused) {
        // Use notifyManager to batch the activity trigger
        notifyManager.schedule(() => {
          triggerActivity('focus', 'Window/tab became focused')
        })
      }
    })

    // Set initial state
    setIsFocused(focusManager.isFocused())

    return () => {
      log('Unsubscribing from FocusManager')
      unsubscribe()
    }
  }, [enabled, triggerActivity, log])

  // Subscribe to OnlineManager
  useEffect(() => {
    if (!enabled) return

    log('Subscribing to OnlineManager')

    const unsubscribe = onlineManager.subscribe((online) => {
      log(`Network changed: ${online ? 'online' : 'offline'}`)
      setIsOnline(online)

      if (online) {
        // Use notifyManager to batch the activity trigger
        notifyManager.schedule(() => {
          triggerActivity('online', 'Network connection restored')
        })
      }
    })

    // Set initial state
    setIsOnline(onlineManager.isOnline())

    return () => {
      log('Unsubscribing from OnlineManager')
      unsubscribe()
    }
  }, [enabled, triggerActivity, log])

  // Log initialization
  useEffect(() => {
    if (debug) {
      log(`Initial state - focused: ${isFocused}, online: ${isOnline}`)
    }
  }, [debug, log, isFocused, isOnline])

  return {
    isFocused,
    isOnline,
    lastActivity,
  }
}
