'use client'

import { useMemo } from 'react'

import { parseAsInteger, useQueryState } from 'nuqs'

import { ITEMS_PER_PAGE } from '@/components/pagination/pagination-search-params'

interface PaginationOptions {
  totalItems: number
  defaultPage?: number
  defaultPerPage?: number
  pageParamName?: string
  perPageParamName?: string
}

interface PaginationResult<T> {
  currentPage: number
  totalPages: number
  itemsPerPage: number
  paginatedData: T[]
  goToPage: (page: number) => Promise<void>
  nextPage: () => Promise<void>
  prevPage: () => Promise<void>
  setItemsPerPage: (perPage: number) => Promise<void>
  startIndex: number
  endIndex: number
  hasNextPage: boolean
  hasPrevPage: boolean
  isLoading: boolean
}

/**
 * A hook for client-side pagination using nuqs for URL state management
 *
 * @param data The full array of data to paginate
 * @param options Pagination options
 * @returns Pagination state and controls
 */
export function useClientPagination<T>(
  data: T[],
  {
    totalItems,
    defaultPage = 1,
    defaultPerPage = ITEMS_PER_PAGE,
    pageParamName = 'page',
    perPageParamName = 'perPage',
  }: PaginationOptions
): PaginationResult<T> {
  // Use nuqs to manage pagination state in URL
  const [page, setPage] = useQueryState(
    pageParamName,
    parseAsInteger.withOptions({ clearOnDefault: true }).withDefault(defaultPage)
  )

  const [perPage, setPerPage] = useQueryState(
    perPageParamName,
    parseAsInteger.withOptions({ clearOnDefault: true }).withDefault(defaultPerPage)
  )

  // Calculate total pages
  const totalPages = Math.max(1, Math.ceil(totalItems / perPage))

  // Ensure current page is within valid range
  const validatedCurrentPage = Math.min(Math.max(1, page), totalPages)

  // If current page is out of range, update it
  if (page !== validatedCurrentPage) {
    setPage(validatedCurrentPage)
  }

  // Calculate start and end indices for slicing the data array
  const startIndex = (validatedCurrentPage - 1) * perPage
  const endIndex = Math.min(startIndex + perPage, data.length)

  // Get paginated data slice
  const paginatedData = useMemo(() => {
    return data?.slice(startIndex, endIndex)
  }, [data, startIndex, endIndex])

  // Navigation functions
  const goToPage = async (newPage: number) => {
    const validPage = Math.min(Math.max(1, newPage), totalPages)
    await setPage(validPage)
  }

  const nextPage = async () => {
    if (validatedCurrentPage < totalPages) {
      await goToPage(validatedCurrentPage + 1)
    }
  }

  const prevPage = async () => {
    if (validatedCurrentPage > 1) {
      await goToPage(validatedCurrentPage - 1)
    }
  }

  const setItemsPerPage = async (newPerPage: number) => {
    await setPerPage(newPerPage)
    // When changing items per page, go back to first page to avoid confusion
    await setPage(1)
  }

  return {
    currentPage: validatedCurrentPage,
    totalPages,
    itemsPerPage: perPage,
    paginatedData,
    goToPage,
    nextPage,
    prevPage,
    setItemsPerPage,
    startIndex,
    endIndex,
    hasNextPage: validatedCurrentPage < totalPages,
    hasPrevPage: validatedCurrentPage > 1,
    isLoading: false, // nuqs handles loading state internally
  }
}
