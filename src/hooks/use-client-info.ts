'use client'

import { useEffect, useState } from 'react'

import { getCurrentCountryCode } from '@/constants/countries'

import { useIsMounted } from './use-is-mounted'

interface ClientInfo {
  country?: string
  timezone: string
  language: string
  browser: {
    name: string
    version: string
    os: string
    mobile: boolean
  }
  screenSize: {
    width?: number
    height?: number
  }
}

export function useClientInfo(enabled: boolean = true) {
  const isMounted = useIsMounted()
  const [clientInfo, setClientInfo] = useState<ClientInfo>({
    timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
    language: navigator.language,
    browser: {
      name: getBrowserName(),
      version: getBrowserVersion(),
      os: getOperatingSystem(),
      mobile: isMobile(),
    },
    screenSize: {
      width: undefined,
      height: undefined,
    },
  })

  useEffect(() => {
    if (!isMounted) return

    setClientInfo((prev) => ({
      ...prev,
      country: getCurrentCountryCode(),
      screenSize: {
        width: window.innerWidth,
        height: window.innerHeight,
      },
    }))
  }, [isMounted])

  useEffect(() => {
    if (!enabled || !isMounted) return

    // Update screen size on resize
    const handleResize = () => {
      setClientInfo((prev) => ({
        ...prev,
        screenSize: {
          width: window.innerWidth,
          height: window.innerHeight,
        },
      }))
    }

    window.addEventListener('resize', handleResize)
    return () => window.removeEventListener('resize', handleResize)
  }, [enabled, isMounted])

  return clientInfo
}

// Helper functions to get browser information
function getBrowserName(): string {
  const userAgent = navigator.userAgent
  if (userAgent.includes('Firefox')) return 'Firefox'
  if (userAgent.includes('Chrome')) return 'Chrome'
  if (userAgent.includes('Safari')) return 'Safari'
  if (userAgent.includes('Edge')) return 'Edge'
  if (userAgent.includes('Opera') || userAgent.includes('OPR')) return 'Opera'
  return 'Unknown'
}

function getBrowserVersion(): string {
  const userAgent = navigator.userAgent
  const match = userAgent.match(/(firefox|chrome|safari|opera|edge)[/\s](\d+(\.\d+)?)/i)
  return match ? match[2] : 'Unknown'
}

function getOperatingSystem(): string {
  const userAgent = navigator.userAgent
  if (userAgent.includes('Win')) return 'Windows'
  if (userAgent.includes('Mac')) return 'MacOS'
  if (userAgent.includes('Linux')) return 'Linux'
  if (userAgent.includes('Android')) return 'Android'
  if (userAgent.includes('iOS')) return 'iOS'
  return 'Unknown'
}

function isMobile(): boolean {
  return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)
}
