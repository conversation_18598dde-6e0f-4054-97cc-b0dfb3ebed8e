// import { getUserLocale } from '@/i18n/i18n-actions'
import Providers from '@/providers/providers'
import type { Metadata } from 'next'
import { getLocale, getMessages } from 'next-intl/server'
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>st_Mono, Mulish } from 'next/font/google'

import './globals.css'

const geistSans = Geist({
  variable: '--font-geist-sans',
  subsets: ['latin'],
})

const geistMono = Geist_Mono({
  variable: '--font-geist-mono',
  subsets: ['latin'],
})

const mulish = Mulish({
  variable: '--font-mulish',
  subsets: ['latin'],
})

export const metadata: Metadata = {
  metadataBase: new URL(process.env.APP_URL || 'http://localhost:3000'),
  title: 'Mobilversichert',
  description: 'Client Portal',
  openGraph: {
    title: 'Mobilversichert',
    description: 'Client Portal',
    images: ['/og-image.png'],
  },
  icons: {
    icon: [
      { url: '/favicon-16x16.png', sizes: '16x16', type: 'image/png' },
      { url: '/favicon-32x32.png', sizes: '32x32', type: 'image/png' },
      { url: '/favicon.png', sizes: '41x40', type: 'image/png' },
    ],
    shortcut: '/favicon.png',
    apple: '/apple-touch-icon.png',
  },
}

export default async function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  const userLocale = await getLocale()
  const messages = await getMessages()

  return (
    <html lang={userLocale}>
      <body className={`${geistSans.variable} ${geistMono.variable} ${mulish.variable} antialiased`}>
        <Providers locale={userLocale} messages={messages ?? undefined}>
          {children}
        </Providers>
      </body>
    </html>
  )
}
