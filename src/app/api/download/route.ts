import { getAppConfig } from '@/app-config'
import { NextRequest } from 'next/server'

export async function GET(request: NextRequest) {
  const searchParams = request.nextUrl.searchParams
  const documentId = searchParams.get('document_id')
  const accessToken = searchParams.get('access_token')

  if (!documentId || !accessToken) {
    return new Response('Missing required parameters', { status: 400 })
  }

  try {
    // Build the API URL
    const apiUrl = `${getAppConfig().apiBaseUrl}/b2c/file?document_id=${documentId}&access_token=${accessToken}&download=1`

    const response = await fetch(apiUrl, {
      headers: {
        Authorization: `Bearer ${accessToken}`,
        'User-Agent': 'Mozilla/5.0 (compatible; Next.js)',
      },
    })

    if (!response.ok) {
      console.error('API response not ok:', response.status, response.statusText)
      return new Response('Failed to fetch document', { status: response.status })
    }

    // Get headers from the original response
    const contentType = response.headers.get('content-type') || 'application/octet-stream'
    const contentLength = response.headers.get('content-length')

    // Try to get filename from Content-Disposition header
    const contentDisposition = response.headers.get('content-disposition')
    let filename = 'document'

    if (contentDisposition) {
      const filenameMatch = contentDisposition.match(/filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/)
      if (filenameMatch && filenameMatch[1]) {
        filename = filenameMatch[1].replace(/['"]/g, '')
      }
    }

    // Set response headers to force download
    const headers = new Headers({
      'Content-Type': contentType,
      'Content-Disposition': `attachment; filename="${filename}"`,
      'Cache-Control': 'no-cache, no-store, must-revalidate',
      Pragma: 'no-cache',
      Expires: '0',
    })

    if (contentLength) {
      headers.set('Content-Length', contentLength)
    }

    // Stream the response
    return new Response(response.body, {
      status: 200,
      headers,
    })
  } catch (error) {
    console.error('Download proxy error:', error)
    return new Response('Internal server error', { status: 500 })
  }
}
