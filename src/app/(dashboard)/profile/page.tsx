import { Suspense } from 'react'

import { ProfileFormServer } from '@/modules/profile/components/profile-form-server'
import { ProfileLoading } from '@/modules/profile/components/profile-loading'
import { Metadata } from 'next'
import { getTranslations } from 'next-intl/server'

import { NavigationBackButton } from '@/components/navigation-back-button'

export const dynamic = 'force-dynamic'

export async function generateMetadata(): Promise<Metadata> {
  const t = await getTranslations()
  return {
    title: t('PROFILE.EDIT_FORM.TITLE', { defaultValue: 'Profile' }),
  }
}

export default async function ProfilePage() {
  const t = await getTranslations('PROFILE')

  return (
    <div className="h-full flex flex-col w-full">
      {/* Header */}
      <div className="flex justify-between items-center pb-6 flex-shrink-0 px-4 pt-4">
        <NavigationBackButton onlyIcon fallbackUrl="/contracts" />
        <h1 className="text-2xl font-semibold text-[#111827]">{t('EDIT_FORM.TITLE')}</h1>
        <div className="w-6" /> {/* Empty div for flex spacing */}
      </div>

      <Suspense fallback={<ProfileLoading />}>
        <ProfileFormServer />
      </Suspense>
    </div>
  )
}
