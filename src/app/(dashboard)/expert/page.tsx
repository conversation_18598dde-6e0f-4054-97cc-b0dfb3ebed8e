import { Suspense } from 'react'

import { ExpertCardServer } from '@/modules/expert/components/expert-card-server'
import { ExpertLoading } from '@/modules/expert/components/expert-loading'
import { Metadata } from 'next'
import { getTranslations } from 'next-intl/server'

export const dynamic = 'force-dynamic'

export async function generateMetadata(): Promise<Metadata> {
  const t = await getTranslations()
  return {
    title: t('EXPERT.DETAILS.TITLE', { defaultValue: 'Expert' }),
  }
}

export default function ExportPage() {
  return (
    <Suspense fallback={<ExpertLoading />}>
      <ExpertCardServer />
    </Suspense>
  )
}
