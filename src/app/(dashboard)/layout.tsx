import { getSession } from '@/modules/auth/actions/session'
import { DashboardLayout } from '@/modules/dashboard/components/dashboard-layout'
import { SessionProvider } from '@/providers/session-provider'

export const dynamic = 'force-dynamic' // Force dynamic rendering for this layout

export default async function Layout({ children }: { children: React.ReactNode }) {
  const session = await getSession()
  return (
    <SessionProvider session={session}>
      <DashboardLayout>{children}</DashboardLayout>
    </SessionProvider>
  )
}
