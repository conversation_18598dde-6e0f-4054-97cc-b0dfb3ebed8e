import { Suspense } from 'react'

import { ContractList } from '@/modules/contracts/components/contract-list'
import { ContractListSkeleton } from '@/modules/contracts/components/contract-skeletons'
import { Metadata } from 'next'
import { getTranslations } from 'next-intl/server'
import { SearchParams } from 'nuqs/server'

import { loadPaginationSearchParams } from '@/components/pagination/pagination-search-params'

export const dynamic = 'force-dynamic'

export async function generateMetadata(): Promise<Metadata> {
  const t = await getTranslations()
  return {
    title: t('Contracts'),
  }
}

interface Props {
  searchParams: Promise<SearchParams>
}

export default async function ContractsPage({ searchParams }: Props) {
  const filters = await loadPaginationSearchParams(searchParams)
  return (
    <Suspense fallback={<ContractListSkeleton />}>
      <ContractList filters={filters} />
    </Suspense>
  )
}
