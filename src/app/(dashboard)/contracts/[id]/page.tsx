import { Suspense } from 'react'

import { ContractDetails } from '@/modules/contracts/components/contract-details'
import { ContractHeader } from '@/modules/contracts/components/contract-header'
import { ContractDetailsSkeleton } from '@/modules/contracts/components/contract-skeletons'
import { Metadata } from 'next'
import { getTranslations } from 'next-intl/server'

export async function generateMetadata(): Promise<Metadata> {
  const t = await getTranslations()
  return {
    title: t('Contract Details'),
  }
}

interface ContractDetailsPageProps {
  params: Promise<{ id: string }>
}

export default async function ContractDetailsPage({ params }: ContractDetailsPageProps) {
  const { id } = await params

  return (
    <div className="w-full px-4 md:px-0 ">
      <ContractHeader id={id} activeUrl="details" backUrl={`/contracts`} />
      <Suspense fallback={<ContractDetailsSkeleton />}>
        <ContractDetails id={id} />
      </Suspense>
    </div>
  )
}
