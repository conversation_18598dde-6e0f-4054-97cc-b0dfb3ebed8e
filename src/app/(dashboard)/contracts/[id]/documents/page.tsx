import { Suspense } from 'react'

import { ContractDocumentList } from '@/modules/contracts/components/contract-document-list'
import { ContractHeader } from '@/modules/contracts/components/contract-header'
import { Metadata } from 'next'
import { getTranslations } from 'next-intl/server'

import { DocumentListSkeleton } from '@/components/documents'

interface ContractDocumentsPageProps {
  params: Promise<{ id: string }>
}

export async function generateMetadata(): Promise<Metadata> {
  const t = await getTranslations()
  return {
    title: t('Documents'),
  }
}

export default async function ContractDocumentsPage({ params }: ContractDocumentsPageProps) {
  const { id } = await params

  return (
    <div className="px-4 md:px-0 ">
      <ContractHeader id={id} activeUrl="documents" backUrl={`/contracts/${id}`} />

      <div className="w-full  mt-6 pb-6 max-w-4xl mx-auto shadow-2xl rounded-lg px-6 py-10 ">
        <div className="max-w-4xl mx-auto overflow-y-auto">
          <Suspense fallback={<DocumentListSkeleton />}>
            <ContractDocumentList contractId={id} />
          </Suspense>
        </div>
      </div>
    </div>
  )
}
