import { Suspense } from 'react'

import { ContractFormServer } from '@/modules/contracts/components/contract-form-server'
import { CreateContractLoading } from '@/modules/contracts/components/contract-skeletons'
import { Metadata } from 'next'
import { getTranslations } from 'next-intl/server'

import { NavigationBackButton } from '@/components/navigation-back-button'

interface EditContractPageProps {
  params: Promise<{ id: string }>
}

export async function generateMetadata(): Promise<Metadata> {
  const t = await getTranslations()
  return {
    title: t('CONTRACTS.EDIT_FORM.EDIT_MODE.TITLE'),
  }
}

export default async function EditContractPage({ params }: EditContractPageProps) {
  const { id } = await params
  const t = await getTranslations()

  return (
    <div className="h-full flex flex-col w-full">
      {/* Header */}
      <div className="flex justify-between items-center pb-6 flex-shrink-0 px-4 pt-4">
        <NavigationBackButton onlyIcon fallbackUrl="/contracts" />
        <h1 className="text-2xl font-semibold text-[#111827]">{t('CONTRACTS.EDIT_FORM.EDIT_MODE.TITLE')}</h1>
        <div className="w-6" /> {/* Empty div for flex spacing */}
      </div>

      <Suspense fallback={<CreateContractLoading />}>
        <ContractFormServer editForm id={id} />
      </Suspense>
    </div>
  )
}
