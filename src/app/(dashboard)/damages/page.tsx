import { Suspense } from 'react'

import { DamageList } from '@/modules/damages/components/damage-list'
import { DamageListSkeleton } from '@/modules/damages/components/damage-skeletons'
import { Metadata } from 'next'
import { getTranslations } from 'next-intl/server'
import { SearchParams } from 'nuqs/server'

import { loadPaginationSearchParams } from '@/components/pagination/pagination-search-params'

export const dynamic = 'force-dynamic'

export async function generateMetadata(): Promise<Metadata> {
  const t = await getTranslations()
  return {
    title: t('DAMAGES.LIST.TITLE'),
  }
}

interface Props {
  searchParams: Promise<SearchParams>
}

export default async function DamagesPage({ searchParams }: Props) {
  const filters = await loadPaginationSearchParams(searchParams)
  return (
    <Suspense fallback={<DamageListSkeleton />}>
      <DamageList filters={filters} />
    </Suspense>
  )
}
