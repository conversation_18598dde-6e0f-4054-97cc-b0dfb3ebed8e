import { Suspense } from 'react'
import { Metada<PERSON> } from 'next'
import { getTranslations } from 'next-intl/server'

import { NavigationBackButton } from '@/components/navigation-back-button'
import { DamageFormServer } from '@/modules/damages/components/damage-form-server'
import { CreateDamageLoading } from '@/modules/damages/components/damage-skeletons'

interface EditDamagePageProps {
  params: Promise<{ id: string }>
}

export async function generateMetadata(): Promise<Metadata> {
  const t = await getTranslations()
  return {
    title: t('DAMAGES.EDIT_FORM.EDIT_MODE.TITLE', { defaultValue: 'Edit Damage' }),
  }
}

export default async function EditDamagePage({ params }: EditDamagePageProps) {
  const { id } = await params
  const t = await getTranslations()

  // For now, we'll need contractId - this should come from the damage data
  // This is a placeholder until we have the actual API integration
  const contractId = 'placeholder-contract-id'

  return (
    <div className="h-full flex flex-col">
      {/* Header - Following contracts edit pattern */}
      <div className="flex justify-between items-center pb-6 flex-shrink-0 px-4 pt-4">
        <NavigationBackButton onlyIcon fallbackUrl={`/damages/${id}`} />
        <h1 className="text-2xl font-semibold text-[#111827]">
          {t('DAMAGES.EDIT_FORM.EDIT_MODE.TITLE', { defaultValue: 'Edit Damage' })}
        </h1>
        <div className="w-6" /> {/* Empty div for flex spacing */}
      </div>

      <Suspense fallback={<CreateDamageLoading />}>
        <DamageFormServer editForm={true} id={id} contractId={contractId} />
      </Suspense>
    </div>
  )
}
