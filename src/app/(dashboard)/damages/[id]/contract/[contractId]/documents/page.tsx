import { Suspense } from 'react'

import { DamageDocuments } from '@/modules/damages/components/damage-documents-list'
import { DamageHeader } from '@/modules/damages/components/damage-header'
import { DamageDetailsSkeleton } from '@/modules/damages/components/damage-skeletons'
import { Metadata } from 'next'
import { getTranslations } from 'next-intl/server'

interface DamageDocumentsPageProps {
  params: Promise<{ id: string; contractId: string }>
}

export async function generateMetadata(): Promise<Metadata> {
  const t = await getTranslations()
  return {
    title: t('DAMAGES.DETAILS.DOCUMENTS_TAB.TITLE'),
  }
}

export default async function DamageDocumentsPage({ params }: DamageDocumentsPageProps) {
  const { id, contractId } = await params

  return (
    <div className="px-4 md:px-0">
      <DamageHeader
        id={id}
        activeUrl="documents"
        contractId={contractId}
        backUrl={`/damages/${id}/contract/${contractId}`}
      />

      <div className="w-full mt-6 pb-6 max-w-4xl mx-auto shadow-2xl rounded-lg px-6 py-10">
        <div className="max-w-4xl mx-auto overflow-y-auto">
          <Suspense fallback={<DamageDetailsSkeleton />}>
            <DamageDocuments damageId={id} />
          </Suspense>
        </div>
      </div>
    </div>
  )
}
