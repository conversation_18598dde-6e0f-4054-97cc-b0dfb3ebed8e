import { Suspense } from 'react'

import { DamageDetails } from '@/modules/damages/components/damage-details'
import { DamageHeader } from '@/modules/damages/components/damage-header'
import { DamageDetailsSkeleton } from '@/modules/damages/components/damage-skeletons'
import { Metadata } from 'next'
import { getTranslations } from 'next-intl/server'

interface DamageDetailsPageProps {
  params: Promise<{ id: string; contractId: string }>
}

export async function generateMetadata(): Promise<Metadata> {
  const t = await getTranslations()
  return {
    title: t('DAMAGES.DETAILS.DETAILS_TAB.TITLE'),
  }
}

export default async function DamageDetailsPage({ params }: DamageDetailsPageProps) {
  const { id, contractId } = await params

  return (
    <div className="px-4 md:px-0">
      <DamageHeader id={id} activeUrl="details" contractId={contractId} backUrl="/damages" />

      <div className="w-full mt-6 pb-6 max-w-4xl mx-auto shadow-2xl rounded-lg px-6 py-10">
        <div className="max-w-4xl mx-auto overflow-y-auto">
          <Suspense fallback={<DamageDetailsSkeleton />}>
            <DamageDetails damageId={id} contractId={contractId} />
          </Suspense>
        </div>
      </div>
    </div>
  )
}
