import { Suspense } from 'react'

import { DamageFormServer } from '@/modules/damages/components/damage-form-server'
import { CreateDamageLoading } from '@/modules/damages/components/damage-skeletons'
import { Metadata } from 'next'
import { getTranslations } from 'next-intl/server'

import { NavigationBackButton } from '@/components/navigation-back-button'

export const dynamic = 'force-dynamic'

export async function generateMetadata(): Promise<Metadata> {
  const t = await getTranslations()
  return {
    title: t('DAMAGES.EDIT_FORM.CREATE_MODE.TITLE', { defaultValue: 'Create Damage' }),
  }
}

export default async function CreateDamagePage() {
  const t = await getTranslations()

  return (
    <div className="h-full flex flex-col">
      {/* Header - Following contracts create pattern */}
      <div className="flex justify-between items-center pb-6 flex-shrink-0 px-4 pt-4">
        <NavigationBackButton onlyIcon fallbackUrl="/damages" />
        <h1 className="text-2xl font-semibold text-[#111827]">
          {t('DAMAGES.EDIT_FORM.CREATE_MODE.TITLE', { defaultValue: 'Create Damage' })}
        </h1>
        <div className="w-6" /> {/* Empty div for flex spacing */}
      </div>

      <Suspense fallback={<CreateDamageLoading />}>
        <DamageFormServer />
      </Suspense>
    </div>
  )
}
