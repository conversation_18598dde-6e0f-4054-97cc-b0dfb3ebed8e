import { Suspense } from 'react'

import { OfferList } from '@/modules/offers/components/offer-list'
import { OfferListSkeleton } from '@/modules/offers/components/offer-skeletons'
import { Metadata } from 'next'
import { getTranslations } from 'next-intl/server'

export const dynamic = 'force-dynamic'

export async function generateMetadata(): Promise<Metadata> {
  const t = await getTranslations('OFFERS.PACKAGES_LIST')
  return {
    title: t('TITLE'),
  }
}

export default async function OffersPage() {
  const t = await getTranslations('OFFERS')
  return (
    <div className="h-full flex flex-col ">
      <div className="hidden md:flex justify-center items-center pb-6 px-4 pt-4 ">
        <h1 className="text-2xl font-semibold text-[#142a3a]">{t('TITLE')}</h1>
      </div>
      <div className="flex-1 overflow-y-auto p-4">
        <div className="max-w-4xl mx-auto space-y-6">
          <div className="text-start mb-8">
            <h2 className="text-xl font-semibold text-[#142a3a] mb-2">{t('PACKAGES_LIST.TITLE')}</h2>
          </div>
          <Suspense fallback={<OfferListSkeleton />}>
            <OfferList />
          </Suspense>
        </div>
      </div>
    </div>
  )
}
