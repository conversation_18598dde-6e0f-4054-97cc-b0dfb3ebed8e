import { Suspense } from 'react'

import { OfferDetailServer } from '@/modules/offers/components/offer-detail-server'
import { OfferDetailSkeleton } from '@/modules/offers/components/offer-skeletons'
import { Metadata } from 'next'
import { getTranslations } from 'next-intl/server'

import { NavigationBackButton } from '@/components/navigation-back-button'

export async function generateMetadata(): Promise<Metadata> {
  return {
    title: 'Offer Details',
  }
}

interface OfferDetailPageProps {
  params: Promise<{ packageId: string; offerId: string }>
}

export default async function OfferDetailPage({ params }: OfferDetailPageProps) {
  const { packageId, offerId } = await params
  const t = await getTranslations('OFFERS.DETAILS')

  return (
    <div className="h-full flex flex-col">
      {/* Header */}
      <div className="flex justify-between items-center pb-6 flex-shrink-0 px-4 pt-4">
        <NavigationBackButton onlyIcon fallbackUrl={`/offers/packages/${packageId}`} />
        <h1 className="text-2xl font-semibold text-[#142a3a]">{t('TITLE', { defaultValue: 'Offer Details' })}</h1>
        <div className="w-20" />
      </div>
      <Suspense fallback={<OfferDetailSkeleton />}>
        <OfferDetailServer packageId={packageId} offerId={offerId} />
      </Suspense>
    </div>
  )
}
