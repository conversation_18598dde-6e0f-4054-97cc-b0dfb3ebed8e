import { Suspense } from 'react'

import { PackageOffersContentSkeleton } from '@/modules/offers/components/offer-skeletons'
import { PackageOffersHeader } from '@/modules/offers/components/package-offers-header'
import { PackageOffersServer } from '@/modules/offers/components/package-offers-server'
import { Metadata } from 'next'
import { getTranslations } from 'next-intl/server'

export const dynamic = 'force-dynamic'

export async function generateMetadata(): Promise<Metadata> {
  const t = await getTranslations()
  return {
    title: t('OFFERS.OFFERS_LIST.TITLE'),
  }
}

interface PackageOffersPageProps {
  params: Promise<{ packageId: string }>
}

export default async function PackageOffersPage({ params }: PackageOffersPageProps) {
  const { packageId } = await params

  return (
    <div>
      <PackageOffersHeader />
      <Suspense fallback={<PackageOffersContentSkeleton />}>
        <PackageOffersServer packageId={packageId} />
      </Suspense>
    </div>
  )
}
