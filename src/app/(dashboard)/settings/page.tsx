import { SettingsForm } from '@/modules/settings/components'
import { getTranslations } from 'next-intl/server'

import { NavigationBackButton } from '@/components/navigation-back-button'

export const dynamic = 'force-dynamic'

export default async function SettingsPage() {
  const t = await getTranslations()
  return (
    <div className="h-full flex flex-col w-full">
      <div className="flex justify-between items-center pb-6 flex-shrink-0 px-4 pt-4">
        <NavigationBackButton onlyIcon fallbackUrl="/contracts" />
        <h1 className="text-2xl font-semibold text-[#111827]">{t('SETTINGS.TITLE')}</h1>
        <div className="w-6" />
      </div>
      <SettingsForm />
    </div>
  )
}
