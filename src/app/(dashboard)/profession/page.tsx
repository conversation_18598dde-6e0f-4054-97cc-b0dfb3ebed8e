import { Suspense } from 'react'

import { ProfessionFormServer } from '@/modules/profession/components/profession-form-server'
import { ProfessionLoading } from '@/modules/profession/components/profession-loading'
import { Metadata } from 'next'
import { getTranslations } from 'next-intl/server'

import { NavigationBackButton } from '@/components/navigation-back-button'

export const dynamic = 'force-dynamic'

export async function generateMetadata(): Promise<Metadata> {
  const t = await getTranslations()
  return {
    title: t('PROFESSION.EDIT.HEADER.TITLE', { defaultValue: 'Profession' }),
  }
}

export default async function ProfessionPage() {
  const t = await getTranslations('PROFESSION')

  return (
    <div className="h-full flex flex-col w-full">
      {/* Header */}
      <div className="flex justify-between items-center pb-6 flex-shrink-0 px-4 pt-4">
        <NavigationBackButton onlyIcon fallbackUrl="/contracts" />
        <h1 className="text-2xl font-semibold text-[#111827]">{t('EDIT.HEADER.TITLE')}</h1>
        <div className="w-6" /> {/* Empty div for flex spacing */}
      </div>

      <Suspense fallback={<ProfessionLoading />}>
        <ProfessionFormServer />
      </Suspense>
    </div>
  )
}
