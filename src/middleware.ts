import { NextRequest, NextResponse } from 'next/server'

import { decryptSession } from './lib/encrypt-session'
import { getSessionCookie } from './modules/auth/actions/session'

const publicRoutes = ['/login', '/sign-up', '/welcome', '/forgot-password', '/data-management']
const apiRoutes = ['/api/auth/refresh-session', '/api/auth/session', '/api/auth/refresh']

export async function middleware(request: NextRequest) {
  const pathname = request.nextUrl.pathname
  const isPublicRoute = publicRoutes.includes(pathname)

  // Allow public routes and API routes to proceed

  try {
    // Only check if session cookie exists - don't trigger refresh logic
    const encryptedSession = await getSessionCookie()
    let hasValidSession = false

    if (encryptedSession) {
      try {
        const session = await decryptSession(encryptedSession)
        // Basic check - just see if we have required fields, don't check expiration
        hasValidSession = !!(session?.accessToken && session?.refreshToken)
      } catch (error) {
        console.log('🔒 [Middleware] Invalid session cookie')
        hasValidSession = false
      }
    }

    if (isPublicRoute || apiRoutes.some((route) => pathname.startsWith(route))) {
      return NextResponse.next()
    }

    // Case 1: No session and trying to access protected route - redirect to login
    // if (!hasValidSession) {
    //   console.log('🔒 [Middleware] No valid session found, redirecting to login')
    //   return NextResponse.redirect(new URL('/login', request.url))
    // }

    // Case 3: Valid session cookie exists - allow access (let components handle expiration)
    return NextResponse.next()
  } catch (error) {
    console.error('❌ [Middleware] Error processing session:', error)
    // On error, redirect to login for security
    return NextResponse.redirect(new URL('/login', request.url))
  }
}

export const config = {
  matcher: [
    // Skip Next.js internals and all static files, unless found in search params
    '/((?!_next|[^?]*\\.(?:html?|css|js(?!on)|jpe?g|webp|png|gif|svg|ttf|woff2?|ico|csv|docx?|xlsx?|zip|webmanifest)).*)',
    // Always run for API routes
    '/(api|trpc)(.*)',
  ],
}
