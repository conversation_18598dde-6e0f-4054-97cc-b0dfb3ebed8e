import { Offer, OfferDisplayData, OfferParameterGroup, OfferParameterIcon } from './offer-types'

/**
 * Extract display data from offer following ExtJS calculated field patterns
 * Mirrors the calculated fields from app/model/Proposal.js
 */
export function getOfferDisplayData(offer: Offer): OfferDisplayData {
  const productInfo = offer.productInfo || {}

  return {
    // Company name - following ExtJS pattern
    company: productInfo.gesellschaft || offer.company || 'N/A',

    // Tariff - following ExtJS calculation logic
    tariff: productInfo.tarif || productInfo.tariflink || productInfo.tarifbez || 'N/A',

    // Insured amount
    insuredAmount: productInfo.deckungssumme || 'N/A',

    // Contribution
    contribution: productInfo.beitrag || 'N/A',

    // Starting date
    startingAt: productInfo.versicherungsbeginn || 'N/A',

    // Payment mode
    paymentMode: productInfo.zahlweise || 'N/A',

    // Logo - following ExtJS pattern
    insurerLogo: productInfo.logos?.versicherer || offer.insurerLogo || '/placeholder-logo.png',

    // Innorata logo
    innorataLogo: productInfo.logos?.innorata || offer.innorataLogo,
  }
}

/**
 * Parse parameter groups from productInfo following ExtJS getParametersTable method
 * Mirrors the logic from app/model/Proposal.js getParametersTable()
 */
export function getParameterGroups(offer: Offer): OfferParameterGroup[] {
  const groups: OfferParameterGroup[] = []
  const productInfo = offer.productInfo

  if (!productInfo) return groups

  let category = 1
  let areCategoriesFinished = false

  do {
    // Use the correct field name from ExtJS: rubrik{category}_head
    const parameterGroupName = productInfo[`rubrik${category}_head`]

    if (parameterGroupName) {
      const parameters = []
      let property = 1
      let arePropertiesFinished = false

      do {
        // Use the correct field names from ExtJS
        const label = productInfo[`rubrik${category}_bezeichner${property}_head`]
        const description = productInfo[`rubrik${category}_bezeichner${property}_info`]
        const tariffText = productInfo[`rubrik${category}_bezeichner${property}_tariftext`]

        if (label) {
          parameters.push({
            name: label,
            label: label,
            value: tariffText || 'N/A',
            description: description || '',
            icons: tariffText ? getParameterIcons(productInfo, category, property) : [],
          })
          property++
        } else {
          arePropertiesFinished = true
        }
      } while (!arePropertiesFinished)

      if (parameters.length > 0) {
        groups.push({
          name: parameterGroupName,
          title: parameterGroupName,
          parameters,
        })
      }
      category++
    } else {
      areCategoriesFinished = true
    }
  } while (!areCategoriesFinished)

  return groups
}

/**
 * Generate parameter icons following ExtJS getIcons method
 * Mirrors the logic from app/model/Proposal.js getIcons()
 */
// eslint-disable-next-line @typescript-eslint/no-explicit-any
export function getParameterIcons(productInfo: any, category: number, property: number): OfferParameterIcon[] {
  const evalValue = productInfo[`rubrik${category}_bezeichner${property}_eval`]
  const sb = productInfo[`rubrik${category}_bezeichner${property}_sb`]
  const demand = productInfo[`rubrik${category}_bezeichner${property}_demand`]
  const ak = productInfo[`rubrik${category}_bezeichner${property}_ak`]
  const max = productInfo[`rubrik${category}_bezeichner${property}_max`]

  const result: OfferParameterIcon[] = []

  // Demand icon (star)
  if (typeof demand === 'string' && !!demand) {
    result.push({
      icon: 'star',
      tooltip: demand,
      type: 'star',
    })
  }

  // AK icon (Europe flag)
  if (typeof ak === 'string' && !!ak) {
    if (ak.includes('nicht die Richtlinie')) {
      result.push({
        icon: 'europe_alt_red',
        tooltip: ak,
        type: 'europe_red',
      })
    } else {
      result.push({
        icon: 'europe_alt',
        tooltip: ak,
        type: 'europe',
      })
    }
  }

  // Max icon (smile)
  if (typeof max === 'string' && !!max) {
    result.push({
      icon: 'smile',
      tooltip: max,
      type: 'smile',
    })
  }

  // SB icon (euro)
  if (typeof sb === 'string' && !!sb) {
    result.push({
      icon: 'euro',
      tooltip: sb,
      type: 'euro',
    })
  }

  // Eval icon (plus/minus)
  if (typeof evalValue === 'string' && !!evalValue) {
    if (
      evalValue.includes('Leistung besser') ||
      evalValue.includes('Leistungswunsch erfüllt') ||
      evalValue.includes('Leistung eingeschlossen')
    ) {
      result.push({
        icon: 'plus_alt',
        tooltip: evalValue,
        type: 'plus',
      })
    } else {
      result.push({
        icon: 'minus_alt',
        tooltip: evalValue,
        type: 'minus',
      })
    }
  }

  return result
}

/**
 * Format currency values following the application patterns
 */
export function formatOfferCurrency(value: string | number | undefined): string {
  if (!value) return 'N/A'

  const numValue = typeof value === 'string' ? parseFloat(value) : value
  if (isNaN(numValue)) return value.toString()

  return new Intl.NumberFormat('de-DE', {
    style: 'currency',
    currency: 'EUR',
  }).format(numValue)
}

/**
 * Format date values for offer display
 */
export function formatOfferDate(dateString: string | undefined): string {
  if (!dateString) return 'N/A'

  try {
    const date = new Date(dateString)
    return date.toLocaleDateString('de-DE')
  } catch {
    return dateString
  }
}
