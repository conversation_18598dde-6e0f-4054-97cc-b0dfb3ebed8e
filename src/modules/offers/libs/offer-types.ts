// Offer Package interface (from /proposal_packages API)
export interface OfferPackage {
  id: string
  name: string
  module: string
}

// Offer/Proposal interface (from /proposals API)
export interface Offer {
  id: number
  recommended: boolean
  accepted: boolean
  acceptedAt?: string
  createdAt: string
  proposalPackageId: number
  updatedAt: string
  productInfo: OfferProductInfo
  // Calculated fields from productInfo
  tariff?: string
  insuredAmount?: string
  contribution?: string
  paymentMode?: string
  insurerLogo?: string
  innorataLogo?: string
  startingAt?: string
  company?: string
}

// Product info structure from the ExtJS model
export interface OfferProductInfo {
  tarif?: string
  tariflink?: string
  tarifbez?: string
  deckungssumme?: string
  beitrag?: string
  zahlweise?: string
  versicherungsbeginn?: string
  gesellschaft?: string
  logos?: {
    versicherer?: string
    innorata?: string
  }
  // Dynamic parameter fields (rubrik1_bezeichner1_name, etc.)
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  [key: string]: any
}

// API Response types
export interface OfferPackagesResponse {
  data: OfferPackage[]
}

export interface OffersResponse {
  data: Offer[]
}

// Paginated response (if needed in the future)
export interface PaginatedOffersResponse {
  offers: Offer[]
  pagination: {
    currentPage: number
    totalPages: number
    totalItems: number
    itemsPerPage: number
  }
}

// Offer status enum
export enum OfferStatus {
  PENDING = 'pending',
  ACCEPTED = 'accepted',
  REJECTED = 'rejected',
}

// Parameter table structure for offer details (enhanced for ExtJS migration)
export interface OfferParameterGroup {
  name: string
  title: string
  parameters: OfferParameter[]
}

export interface OfferParameter {
  name: string
  label: string
  value: string
  description?: string
  type?: 'text' | 'currency' | 'date' | 'boolean' | 'coverage'
  icons?: OfferParameterIcon[]
  conditions?: OfferParameterCondition[]
}

export interface OfferParameterIcon {
  icon: string
  tooltip: string
  type:
    | 'star'
    | 'europe'
    | 'europe_alt'
    | 'europe_red'
    | 'europe_alt_red'
    | 'smile'
    | 'euro'
    | 'plus'
    | 'plus_alt'
    | 'minus'
    | 'minus_alt'
    | 'info'
    | 'warning'
    | 'success'
    | 'error'
    | 'coverage'
}

export interface OfferParameterCondition {
  type: 'covered' | 'not_covered' | 'partial' | 'conditional'
  label: string
  icons?: OfferParameterIcon[]
}

// Enhanced offer data extraction interface
export interface OfferDisplayData {
  company: string
  tariff: string
  insuredAmount: string
  contribution: string
  startingAt: string
  paymentMode: string
  insurerLogo: string
  innorataLogo?: string
}
