'use client'

import { useAction } from 'next-safe-action/hooks'
import { useRouter } from 'next/navigation'
import { toast } from 'sonner'

import { acceptOffer, revokeOffer } from '../api/offers-actions'

export function useOfferActions() {
  const router = useRouter()

  const {
    execute: executeAcceptOffer,
    isPending: isAcceptingOffer,
    result: acceptResult,
  } = useAction(acceptOffer, {
    onSuccess: () => {
      router.refresh()
    },
    onError: (error) => {
      toast.error('Failed to accept offer')
      console.error('Failed to accept offer:', error)
    },
  })

  const {
    execute: executeRevokeOffer,
    isPending: isRevokingOffer,
    result: revokeResult,
  } = useAction(revokeOffer, {
    onSuccess: () => {
      router.refresh()
    },
    onError: (error) => {
      toast.error('Failed to revoke offer')
      console.error('Failed to revoke offer:', error)
    },
  })

  return {
    acceptOffer: executeAcceptOffer,
    revokeOffer: executeRevokeOffer,
    isLoading: isAcceptingOffer || isRevokingOffer,
    isAcceptingOffer,
    isRevokingOffer,
    acceptResult,
    revokeResult,
  }
}
