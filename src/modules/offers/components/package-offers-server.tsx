import { getTranslations } from 'next-intl/server'

import { EmptyState } from '@/components/empty-state'
import { ErrorBoundary } from '@/components/error-boundary'

import { getOffersByPackage } from '../api/offers-api'
import { Offer } from '../libs/offer-types'
import { ProposalCard } from './proposal-card'

interface PackageOffersServerProps {
  packageId: string
}

export async function PackageOffersServer({ packageId }: PackageOffersServerProps) {
  const t = await getTranslations()
  const offersResponse = await getOffersByPackage(packageId)

  if (offersResponse?.serverError) {
    return (
      <ErrorBoundary
        error={offersResponse.serverError.message}
        title={t('OFFERS.ERROR.TITLE', { defaultValue: 'Failed to load offers' })}
        description={t('OFFERS.ERROR.DESCRIPTION', {
          defaultValue: 'There was an error loading the offers for this package. Please try again.',
        })}
      />
    )
  }

  const offers = offersResponse?.data

  if (!offers || offers.length === 0) {
    return (
      <div className="h-full flex flex-col">
        <div className="flex-1 flex items-center justify-center">
          <EmptyState
            pageTitle=""
            title={t('OFFERS.PACKAGE_OFFERS.EMPTY_TEXT', { defaultValue: 'No offers in this package' })}
            message={t('OFFERS.PACKAGE_OFFERS.EMPTY_DESCRIPTION', {
              defaultValue: 'This package currently has no available offers.',
            })}
          />
        </div>
      </div>
    )
  }

  const sortedOffers = [...offers].sort((a, b) => {
    if (a.recommended && !b.recommended) return -1
    if (!a.recommended && b.recommended) return 1
    return a.id - b.id
  })

  return (
    <div className="h-full flex flex-col max-w-6xl mx-auto px-4 2xl:px-0">
      <div className="mx-auto grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 justify-center w-full">
        {sortedOffers.map((offer: Offer) => (
          <ProposalCard key={offer.id} offer={offer} packageId={packageId} />
        ))}
      </div>
    </div>
  )
}
