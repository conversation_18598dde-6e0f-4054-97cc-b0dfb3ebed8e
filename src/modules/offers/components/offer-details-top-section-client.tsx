'use client'

import { But<PERSON> } from '@/components/ui/button'

import { useOfferActions } from '../hooks/use-offers'
import { Offer } from '../libs/offer-types'

interface OfferDetailsTopSectionClientProps {
  offer: Offer
  acceptButtonText: string
  revokeButtonText: string
}

export function OfferDetailsTopSectionClient({ 
  offer, 
  acceptButtonText, 
  revokeButtonText 
}: OfferDetailsTopSectionClientProps) {
  const { acceptOffer, revokeOffer, isLoading } = useOfferActions()

  if (offer.accepted) {
    return (
      <Button
        className="bg-red-600 hover:bg-red-700 h-[46px] w-[150px] rounded-[40px] text-white font-bold text-[14px] leading-[22px] font-['Mulish']"
        onClick={() => revokeOffer({ id: offer.id })}
        disabled={isLoading}
      >
        {revokeButtonText}
      </Button>
    )
  }

  return (
    <Button
      className="bg-[#142a3a] hover:bg-[#142a3a]/90 h-[46px] w-[150px] rounded-[40px] text-white font-bold text-[14px] leading-[22px] font-['Mulish']"
      onClick={() => acceptOffer({ id: offer.id })}
      isLoading={isLoading}
      loadingText={acceptButtonText}
    >
      {acceptButtonText}
    </Button>
  )
}
