import { Building2, Calendar, Euro, Star } from 'lucide-react'
import { useTranslations } from 'next-intl'
import Link from 'next/link'

import { formatCurrency } from '@/lib/utils'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent } from '@/components/ui/card'

import { Offer } from '../libs/offer-types'

interface OfferCardProps {
  offer: Offer
  index: number
  packageId: string
}

export function OfferCard({ offer, packageId }: OfferCardProps) {
  const t = useTranslations()

  const getStatusColor = (accepted: boolean, recommended: boolean) => {
    if (accepted) return 'bg-green-100 text-green-800'
    if (recommended) return 'bg-blue-100 text-blue-800'
    return 'bg-gray-100 text-gray-800'
  }

  const getStatusText = (accepted: boolean, recommended: boolean) => {
    if (accepted) return t('OFFERS.STATUS.ACCEPTED')
    if (recommended) return t('OFFERS.STATUS.RECOMMENDED')
    return t('OFFERS.STATUS.PENDING')
  }

  return (
    <Link href={`/offers/packages/${packageId}/proposals/${offer.id}`}>
      <Card className="hover:shadow-md transition-shadow duration-200 cursor-pointer">
        <CardContent className="p-6">
          <div className="flex items-start justify-between mb-4">
            <div className="flex items-center gap-2">
              {offer.recommended && <Star className="w-5 h-5 text-yellow-500 fill-current" />}
              <h3 className="text-lg font-semibold text-gray-900">
                {offer.tariff || offer.company || `${t('OFFERS.CARD.OFFER')} #${offer.id}`}
              </h3>
            </div>
            <Badge className={getStatusColor(offer.accepted, offer.recommended)} variant="secondary">
              {getStatusText(offer.accepted, offer.recommended)}
            </Badge>
          </div>

          <div className="space-y-3">
            {/* Company */}
            {offer.company && (
              <div className="flex items-center gap-2 text-sm text-gray-600">
                <Building2 className="w-4 h-4" />
                <span>{offer.company}</span>
              </div>
            )}

            {/* Contribution/Payment */}
            {offer.contribution && (
              <div className="flex items-center gap-2 text-sm text-gray-600">
                <Euro className="w-4 h-4" />
                <span>
                  {formatCurrency(parseFloat(offer.contribution))}
                  {offer.paymentMode && ` ${offer.paymentMode}`}
                </span>
              </div>
            )}

            {/* Insured Amount */}
            {offer.insuredAmount && (
              <div className="flex items-center gap-2 text-sm text-gray-600">
                <span className="font-medium">{t('OFFERS.CARD.COVERAGE')}:</span>
                <span>{formatCurrency(parseFloat(offer.insuredAmount))}</span>
              </div>
            )}

            {/* Starting Date */}
            {offer.startingAt && (
              <div className="flex items-center gap-2 text-sm text-gray-600">
                <Calendar className="w-4 h-4" />
                <span>
                  {t('OFFERS.CARD.STARTS_AT')}: {offer.startingAt}
                </span>
              </div>
            )}
          </div>

          {/* Recommended Banner */}
          {offer.recommended && (
            <div className="mt-4 p-2 bg-blue-50 border border-blue-200 rounded-md">
              <div className="flex items-center gap-2 text-sm text-blue-800">
                <Star className="w-4 h-4 fill-current" />
                <span className="font-medium">{t('OFFERS.CARD.RECOMMENDED_TEXT')}</span>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </Link>
  )
}
