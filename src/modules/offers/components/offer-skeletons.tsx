import { Card, CardContent } from '@/components/ui/card'
import { Skeleton } from '@/components/ui/skeleton'

import { ProposalCardSkeleton, ProposalCardSkeletonGrid } from './proposal-card-skeleton'

export function OfferListSkeleton() {
  return (
    <div className="h-full flex flex-col">
      {/* Content Skeleton - matching page.tsx structure */}
      <div className="flex-1 overflow-y-auto ">
        <div className="max-w-4xl mx-auto space-y-6">
          {/* Package Groups Skeleton - matching OfferList structure */}
          <div className="space-y-4">
            {Array.from({ length: 3 }).map((_, groupIndex) => (
              <OfferPackageCardSkeleton key={groupIndex} />
            ))}
          </div>
        </div>
      </div>
    </div>
  )
}

// Skeleton for the blue collapsible header (matching CollapsibleHeader component)
function CollapsibleHeaderSkeleton() {
  return (
    <div className="relative rounded-[10px] shadow-md shrink-0 w-full bg-gray-50">
      <div className="flex flex-col items-center relative size-full">
        <div className="box-border content-stretch flex flex-col gap-[131px] items-center justify-start px-6 py-[18px] relative w-full">
          <div className="relative shrink-0 w-full">
            <div className="box-border content-stretch flex flex-row items-center justify-between p-0 relative w-full">
              {/* Title Skeleton */}
              <Skeleton className="h-[22px] w-32 bg-gray-200" />
              {/* Arrow Skeleton */}
              <Skeleton className="size-6 bg-gray-200" />
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

// Skeleton for the white package card (matching PackageCard component)
function PackageCardSkeleton() {
  return (
    <div className="relative rounded-[10px] shadow-md shrink-0 w-full bg-[#ffffff]">
      <div className="flex flex-col items-center relative size-full">
        <div className="box-border content-stretch flex flex-col gap-[131px] items-center justify-start px-6 py-[18px] relative w-full">
          <div className="relative shrink-0 w-full">
            <div className="box-border content-stretch flex flex-row items-center justify-between p-0 relative w-full">
              {/* Left side - Gift icon and package info skeleton */}
              <div className="relative shrink-0">
                <div className="box-border content-stretch flex flex-row gap-4 items-center justify-start p-0 relative">
                  {/* Gift icon skeleton */}
                  <Skeleton className="size-6" />
                  {/* Package text skeleton */}
                  <Skeleton className="h-[22px] w-48" />
                </div>
              </div>
              {/* Right side - Arrow skeleton */}
              <Skeleton className="size-6" />
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

// Main skeleton component matching OfferPackageCard structure
export function OfferPackageCardSkeleton() {
  return (
    <div className="space-y-2">
      {/* Blue collapsible header skeleton */}
      <CollapsibleHeaderSkeleton />
      {/* White package card skeleton (expanded state) */}
      <div className="space-y-2 pl-4">
        <PackageCardSkeleton />
      </div>
    </div>
  )
}

export function OfferCardSkeleton() {
  return (
    <Card>
      <CardContent className="p-6">
        <div className="flex items-start justify-between mb-4">
          <div className="flex items-center gap-2">
            <Skeleton className="h-6 w-48" />
          </div>
          <Skeleton className="h-6 w-20" />
        </div>

        <div className="space-y-3">
          <div className="flex items-center gap-2">
            <Skeleton className="w-4 h-4" />
            <Skeleton className="h-4 w-32" />
          </div>
          <div className="flex items-center gap-2">
            <Skeleton className="w-4 h-4" />
            <Skeleton className="h-4 w-24" />
          </div>
          <div className="flex items-center gap-2">
            <Skeleton className="w-4 h-4" />
            <Skeleton className="h-4 w-36" />
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

export function OfferDetailSkeleton() {
  return (
    <div className="h-full flex flex-col">
      {/* Header Skeleton */}
      <div className="flex justify-between items-center pb-6 flex-shrink-0 px-4 pt-4">
        <Skeleton className="w-6 h-6" />
        <Skeleton className="h-8 w-48" />
        <div className="w-6" />
      </div>

      {/* Content Skeleton */}
      <div className="flex-1 overflow-y-auto p-4">
        <div className="max-w-5xl mx-auto">
          {/* Main Card Skeleton following Figma design */}
          <div className="relative rounded-lg shadow-md shrink-0 bg-white">
            <div className="flex flex-col justify-center relative size-full">
              {/* Main Content Skeleton - Two Column Layout */}
              <div className="flex flex-row items-center justify-center relative size-full">
                <div className="box-border flex flex-row gap-2 items-center justify-center px-3 py-2 relative w-full">
                  {/* Left side - Logo and Button Skeleton */}
                  <div className="relative shrink-0 w-[184px]">
                    <div className="box-border flex flex-col gap-6 items-center justify-center p-0 relative w-[184px]">
                      <div className="relative shrink-0">
                        <div className="box-border flex flex-col gap-6 items-center justify-center p-0 relative">
                          {/* Logo Skeleton */}
                          <Skeleton className="w-[160px] h-[160px] rounded-md" />
                          {/* Button Skeleton */}
                          <Skeleton className="h-[46px] w-[150px] rounded-[40px]" />
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Right side - Details Skeleton */}
                  <div className="relative shrink-0 flex-1">
                    <div className="box-border flex flex-col gap-6 items-start justify-center pb-4 pt-0 px-4 relative w-full">
                      {/* Company Name Skeleton */}
                      <div className="relative shrink-0 w-[297px]">
                        <Skeleton className="h-7 w-[203px]" />
                      </div>

                      {/* Horizontal Line */}
                      <div className="h-0 relative shrink-0 w-[477px]">
                        <div className="w-full h-[1px] bg-[#E1E4E8]" />
                      </div>

                      {/* Details Fields Skeleton */}
                      <div className="space-y-6 w-full">
                        {Array.from({ length: 5 }).map((_, i) => (
                          <div key={i} className="relative shrink-0 w-[297px]">
                            <div className="space-y-1">
                              <Skeleton className="h-4 w-[148px]" />
                              <Skeleton className="h-6 w-[148px]" />
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Tabs Skeleton */}
              <div className="relative shrink-0 px-6 pb-6">
                <div className="relative shrink-0 mb-4">
                  <div className="flex gap-2">
                    <Skeleton className="h-10 w-32 rounded-lg" />
                    <Skeleton className="h-10 w-28 rounded-lg" />
                    <Skeleton className="h-10 w-24 rounded-lg" />
                  </div>
                </div>

                {/* Parameter Table Skeleton */}
                <div className="bg-white rounded-[10px] w-[779px]">
                  <div className="box-border flex flex-col gap-4 items-start justify-center px-0 py-4 relative w-[779px]">
                    {/* Table Header Skeleton */}
                    <div className="relative shrink-0 w-[760px]">
                      <div className="flex justify-between px-2">
                        <Skeleton className="h-4 w-20" />
                        <Skeleton className="h-4 w-24" />
                      </div>
                    </div>

                    {/* Parameters List Skeleton */}
                    <div className="relative shrink-0 w-[767px]">
                      <div className="flex justify-between">
                        <div className="w-[616px] space-y-4">
                          {Array.from({ length: 3 }).map((_, i) => (
                            <div key={i} className="bg-[#f7f8f9] rounded-md w-[616px] h-[65px] p-3">
                              <div className="space-y-1">
                                <Skeleton className="h-4 w-48" />
                                <Skeleton className="h-4 w-24" />
                              </div>
                            </div>
                          ))}
                        </div>
                        <div className="w-[142px] space-y-4">
                          {Array.from({ length: 3 }).map((_, i) => (
                            <div
                              key={i}
                              className="bg-[#f7f8f9] rounded-md w-[142px] h-[65px] flex items-center justify-end pr-2"
                            >
                              <Skeleton className="w-5 h-5" />
                            </div>
                          ))}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

// Skeleton for the full page including header (used when header is also loading)
export function PackageOffersSkeleton() {
  return (
    <div className="h-full flex flex-col">
      {/* Header Skeleton */}
      <div className="flex flex-col gap-4 px-4 pt-4 pb-6 flex-shrink-0">
        <div className="flex justify-between items-center">
          <Skeleton className="h-9 w-20" />
          <Skeleton className="h-8 w-32" />
          <Skeleton className="h-9 w-36" />
        </div>
        <Skeleton className="h-4 w-40" />
      </div>

      {/* Content Skeleton */}
      <div className="flex-1 overflow-y-auto p-4">
        <ProposalCardSkeletonGrid />
      </div>
    </div>
  )
}

// Skeleton for just the content area (used when header is already rendered)
export function PackageOffersContentSkeleton() {
  return (
    <div className="h-full flex flex-col max-w-6xl mx-auto px-4 2xl:px-0">
      <div className="mx-auto grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 justify-center w-full">
        {Array.from({ length: 3 }).map((_, index) => (
          <ProposalCardSkeleton key={index} />
        ))}
      </div>
    </div>
  )
}
