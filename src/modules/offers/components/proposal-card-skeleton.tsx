import { Skeleton } from '@/components/ui/skeleton'

export function ProposalCardSkeleton() {
  return (
    <div className="bg-white relative rounded-xl shadow-2xl w-full  mx-auto">
      {/* Card Content */}
      <div className="flex flex-col gap-6 items-center justify-center py-6 px-4 w-full ">
        {/* Company Section and Info */}
        <div className="flex flex-col gap-2 items-center justify-center w-full">
          {/* Company Logo and Name */}
          <div className="w-full">
            <div className="flex items-center gap-4">
              {/* Company Logo Skeleton */}
              <Skeleton className="w-[60px] h-[60px] rounded-[7.5px] flex-shrink-0" />

              {/* Company Name Skeleton */}
              <div className="flex-1">
                <Skeleton className="h-6 w-32" />
              </div>
            </div>
          </div>

          {/* Information Rows Skeletons */}
          <div className="w-full space-y-2 mt-4">
            {/* Tariff */}
            <div className="flex flex-col gap-0.5">
              <Skeleton className="h-4 w-16" />
              <Skeleton className="h-5 w-20" />
            </div>

            {/* Coverage Amount */}
            <div className="flex flex-col gap-0.5">
              <Skeleton className="h-4 w-28" />
              <Skeleton className="h-5 w-32" />
            </div>

            {/* Contribution */}
            <div className="flex flex-col gap-0.5">
              <Skeleton className="h-4 w-20" />
              <Skeleton className="h-5 w-16" />
            </div>

            {/* Start of Insurance */}
            <div className="flex flex-col gap-0.5">
              <Skeleton className="h-4 w-32" />
              <Skeleton className="h-5 w-24" />
            </div>

            {/* Payment Mode */}
            <div className="flex flex-col gap-0.5">
              <Skeleton className="h-4 w-24" />
              <Skeleton className="h-5 w-20" />
            </div>
            <div className="flex flex-col gap-0.5">
              <Skeleton className="h-4 w-16" />
              <Skeleton className="h-5 w-20" />
            </div>
          </div>
        </div>

        {/* Action Buttons Skeletons */}
        <div className="flex gap-[6.6px] w-full ">
          <Skeleton className="h-8 flex-1 rounded-[40px]" />
          <Skeleton className="h-8 flex-1 rounded-[40px]" />
        </div>
      </div>
    </div>
  )
}

export function ProposalCardSkeletonGrid() {
  return (
    <div className="max-w-7xl mx-auto grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
      {Array.from({ length: 8 }).map((_, index) => (
        <ProposalCardSkeleton key={index} />
      ))}
    </div>
  )
}
