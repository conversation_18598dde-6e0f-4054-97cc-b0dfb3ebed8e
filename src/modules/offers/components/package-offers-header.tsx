import { getTranslations } from 'next-intl/server'

import { NavigationBackButton } from '@/components/navigation-back-button'

export async function PackageOffersHeader() {
  const t = await getTranslations()

  return (
    <div className="flex flex-col gap-4 px-4 pt-4 pb-6 flex-shrink-0">
      {/* Main Header */}
      <div className="flex justify-between items-center">
        {/* Back Navigation */}
        <NavigationBackButton onlyIcon fallbackUrl="/offers" />

        {/* Title */}
        <h1 className="text-2xl font-semibold text-[#142a3a]">
          {t('OFFERS.OFFERS_LIST.TITLE', { defaultValue: 'Proposals' })}
        </h1>
        <div />
      </div>
    </div>
  )
}
