'use client'

import { useTranslations } from 'next-intl'

import { cn } from '@/lib/utils'

import { OfferParameter } from '../libs/offer-types'
import { ParameterIconsList } from './parameter-icons'

interface OfferParameterTableProps {
  parameters: OfferParameter[]
  className?: string
}

interface ParameterRowProps {
  parameter: OfferParameter
}

function ParameterRow({ parameter }: ParameterRowProps) {
  return (
    <div className="grid grid-cols-1 lg:grid-cols-[1fr_142px] gap-4 w-full">
      {/* Parameter Content */}
      <div className="bg-[#f7f8f9] min-h-[67px] relative rounded-md shrink-0 w-full">
        <div className="flex flex-col justify-center relative size-full">
          <div className="box-border flex flex-col gap-1 items-start justify-center pb-2 pl-2 pr-2 pt-[9px] relative w-full">
            <div className="relative shrink-0 w-full">
              <div className="relative size-full">
                <div className="box-border flex flex-col lg:flex-row lg:items-start lg:justify-between gap-2 py-0 relative w-full">
                  <div className="font-['Mulish'] font-normal leading-[22px] flex-1 text-[#142a3a] text-[14px] text-left overflow-hidden">
                    <div className="line-clamp-2 break-words">{parameter.label}</div>
                  </div>
                </div>
              </div>
            </div>
            <div className="font-['Mulish'] font-normal leading-[22px] relative shrink-0 text-[#8a94a5] text-[14px] text-left w-full overflow-hidden">
              <div className="line-clamp-2 break-words">{parameter.value}</div>
            </div>
            {/* Icons on mobile - show stacked vertically */}
            <div className="relative shrink-0 flex items-start lg:hidden">
              <ParameterIconsList icons={parameter.icons || []} />
            </div>
          </div>
        </div>
      </div>

      {/* Conditions/Icons - Desktop only */}
      <div className="bg-[#f7f8f9] min-h-[67px] relative rounded-md shrink-0 w-[142px] hidden lg:block">
        <div className="flex flex-row justify-end relative size-full">
          <div className="box-border content-stretch flex flex-row gap-1.5 min-h-[67px] items-start justify-end pb-1 pt-2 px-2 relative w-full">
            <div className="relative shrink-0">
              <div className="box-border content-stretch flex flex-row gap-[9px] items-start justify-start p-0 relative">
                <ParameterIconsList icons={parameter.icons || []} />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export function OfferParameterTable({ parameters, className }: OfferParameterTableProps) {
  const t = useTranslations('OFFERS.DETAILS.PARAMETERS_TABLE')

  if (!parameters || parameters.length === 0) {
    return (
      <div className={cn('bg-white rounded-[10px] p-6 w-full', className)}>
        <div className="text-center text-gray-500">{t('EMPTY', { defaultValue: 'No parameters available' })}</div>
      </div>
    )
  }

  return (
    <div className={cn('bg-[#ffffff] relative rounded-[10px] w-full', className)}>
      <div className="flex flex-col justify-center relative w-full">
        <div className="box-border content-stretch flex flex-col gap-4 items-start justify-center px-0 py-4 relative w-full">
          {/* Table Header */}
          <div className="relative shrink-0 w-full max-w-none">
            <div className="grid grid-cols-1 lg:grid-cols-[1fr_142px] gap-4 w-full">
              <div className="box-border font-['Mulish'] font-normal leading-[0] px-2 py-0 relative text-[#142a3a] text-[14px] text-left text-nowrap">
                <p className="block leading-[22px] text-nowrap whitespace-pre">
                  {t('DESCRIPTION_COLUMN.HEADER', { defaultValue: 'Parameter' })}
                </p>
              </div>
              <div className="box-border font-['Mulish'] font-normal leading-[0] px-2 py-0 relative text-[#142a3a] text-[14px] text-left text-nowrap hidden lg:block">
                <p className="block leading-[22px] text-nowrap whitespace-pre">
                  {t('CONDITIONS_COLUMN.HEADER', { defaultValue: 'Conditions' })}
                </p>
              </div>
            </div>
          </div>

          {/* Parameters List */}
          <div className="relative shrink-0 w-full">
            <div className="box-border content-stretch flex flex-col gap-4 items-start justify-start p-0 relative w-full">
              {parameters.map((parameter, index) => (
                <ParameterRow key={index} parameter={parameter} />
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
