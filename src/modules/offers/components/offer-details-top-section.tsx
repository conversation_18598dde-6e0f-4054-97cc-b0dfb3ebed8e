import { getTranslations } from 'next-intl/server'
import Image from 'next/image'

import { formatOfferCurrency, formatOfferDate, getOfferDisplayData } from '../libs/offer-data-helpers'
import { Offer } from '../libs/offer-types'
import { OfferDetailsTopSectionClient } from './offer-details-top-section-client'

interface OfferDetailsTopSectionProps {
  offer: Offer
  packageId: string
}

export async function OfferDetailsTopSection({ offer }: OfferDetailsTopSectionProps) {
  const t = await getTranslations('OFFERS.DETAILS')

  const displayData = getOfferDisplayData(offer)

  return (
    <div className="flex flex-col items-center justify-start lg:flex-row lg:items-start gap-6 lg:gap-12">
      {/* Left side - Logo and Accept button */}
      <div className="shrink-0">
        <div className="flex flex-col gap-6 items-center justify-start">
          {/* Logo container */}
          <div
            className="overflow-hidden relative rounded-[26.2px] shrink-0 size-[262px] bg-[#F7F8F9] flex items-center justify-center"
            data-name="logo"
          >
            <Image src={displayData.insurerLogo} alt={`${displayData.company} logo`} className="object-contain" fill />
          </div>

          {/* Accept button */}
          <div className="w-[184px]">
            <div className="flex flex-col gap-6 items-center justify-center">
              <OfferDetailsTopSectionClient
                offer={offer}
                acceptButtonText={t('ACCEPT_BUTTON.TEXT')}
                revokeButtonText={t('REVOKE_BUTTON.TEXT')}
              />
            </div>
          </div>
        </div>
      </div>

      {/* Right side - Offer details */}
      <div className="shrink-0 w-full max-w-[493px] flex flex-col justify-center">
        <div className="flex flex-col gap-2 md:gap-6 items-start justify-center pb-4 pt-0 ">
          {/* Company name */}
          <div className="w-full max-w-[297px]">
            <div className="flex flex-row items-start justify-between">
              <div className="w-full" data-name="cart_text">
                <div className="flex flex-col gap-4 items-start justify-start">
                  <div className="w-full">
                    <div className="flex flex-row gap-4 items-center justify-start">
                      <div className="w-full">
                        <div className="flex flex-col items-start justify-start">
                          <div className="w-full max-w-[203px]">
                            <div className="flex flex-col items-start justify-start">
                              <h1 className="font-bold text-[20px] leading-[28px] text-[#142a3a] text-left font-['Mulish']">
                                {displayData.company}
                              </h1>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Horizontal line */}
          <div className="h-0 w-full max-w-[477px] relative">
            <div className="absolute bottom-[-0.5px] left-0 right-0 top-[-0.5px]">
              <svg className="block size-full" fill="none" preserveAspectRatio="none" viewBox="0 0 477 2">
                <path d="M0 1H477" stroke="#E1E4E8" />
              </svg>
            </div>
          </div>

          {/* Tariff */}
          <div className="w-full max-w-[297px]">
            <div className="flex flex-col gap-1 items-start justify-center font-['Mulish'] text-left">
              <div className="text-[#8a94a5] text-[14px] leading-[22px] font-normal">{t('TARIFF_FIELD.LABEL')}</div>
              <div className="text-[#142a3a] text-[16px] leading-[24px] font-normal">{displayData.tariff}</div>
            </div>
          </div>

          {/* Coverage amount */}
          <div className="w-full max-w-[297px]">
            <div className="flex flex-col gap-1 items-start justify-center font-['Mulish'] text-left">
              <div className="text-[#8a94a5] text-[14px] leading-[22px] font-normal">
                {t('COVERAGE_AMOUNT_FIELD.LABEL')}
              </div>
              <div className="text-[#142a3a] text-[16px] leading-[24px] font-normal">
                {formatOfferCurrency(displayData.insuredAmount)}
              </div>
            </div>
          </div>

          {/* Contribution */}
          <div className="w-full max-w-[297px]">
            <div className="flex flex-col gap-1 items-start justify-center font-['Mulish'] text-left">
              <div className="text-[#8a94a5] text-[14px] leading-[22px] font-normal">
                {t('CONTRIBUTION_FIELD.LABEL')}
              </div>
              <div className="text-[#142a3a] text-[16px] leading-[24px] font-normal">
                {formatOfferCurrency(displayData.contribution)}
              </div>
            </div>
          </div>

          {/* Start of insurance */}
          <div className="w-full max-w-[297px]">
            <div className="flex flex-col gap-1 items-start justify-center font-['Mulish'] text-left">
              <div className="text-[#8a94a5] text-[14px] leading-[22px] font-normal">
                {t('STARTING_AT_FIELD.LABEL')}
              </div>
              <div className="text-[#142a3a] text-[16px] leading-[24px] font-normal">
                {formatOfferDate(displayData.startingAt)}
              </div>
            </div>
          </div>

          {/* Payment mode */}
          <div className="w-full max-w-[297px]">
            <div className="flex flex-col gap-1 items-start justify-center font-['Mulish'] text-left">
              <div className="text-[#8a94a5] text-[14px] leading-[22px] font-normal">
                {t('PAYMENT_MODE_FIELD.LABEL')}
              </div>
              <div className="text-[#142a3a] text-[16px] leading-[24px] font-normal">{displayData.paymentMode}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
