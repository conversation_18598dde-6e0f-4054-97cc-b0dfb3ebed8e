'use client'

import { useState } from 'react'

import { OfferParameterGroup } from '../libs/offer-types'
import { OfferParameterTable } from './offer-parameter-table'

interface OfferParameterTabsProps {
  parameterGroups: OfferParameterGroup[]
}

export function OfferParameterTabs({ parameterGroups }: OfferParameterTabsProps) {
  const [activeTab, setActiveTab] = useState(parameterGroups[0]?.name || '')

  if (!parameterGroups || parameterGroups.length === 0) {
    return null
  }

  // If only one group, don't show tabs
  if (parameterGroups.length === 1) {
    return <OfferParameterTable parameters={parameterGroups[0].parameters} />
  }

  const activeGroup = parameterGroups.find((group) => group.name === activeTab)

  return (
    <div className="h-full flex flex-col pb-4">
      {/* Tab Bar with grid layout - matching Figma design */}
      <div className="relative shrink-0 mb-4">
        <div className="box-border flex flex-row gap-2 items-start justify-start p-0 relative">
          {/* Grid container for tabs - automatically wraps to multiple rows */}
          <div className="w-full">
            <div className="flex flex-wrap items-center gap-x-1 md:gap-x-2 gap-y-2">
              {parameterGroups.map((group) => (
                <button
                  key={group.name}
                  onClick={() => setActiveTab(group.name)}
                  className={`
                    relative cursor-pointer rounded-lg shadow-md transition-all duration-200 hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-[#142a3a]/20  
                    ${activeTab === group.name ? 'bg-[#142a3a]' : 'bg-[#ffffff] hover:bg-gray-50'}
                  `}
                  data-name="Tab"
                  aria-pressed={activeTab === group.name}
                >
                  <div className="flex flex-row items-center justify-center relative size-full">
                    <div className="box-border content-stretch flex flex-row gap-2 items-center justify-center px-2 md:px-3 py-1 md:py-2 relative">
                      <div
                        className={`
                        font-['Mulish'] font-light text-[11px] md:text-[12px] relative shrink-0  text-center break-words
                        ${activeTab === group.name ? 'text-[#ffffff]' : 'text-[#142a3a]'}
                      `}
                      >
                        {group.title || group.name}
                      </div>
                    </div>
                  </div>
                </button>
              ))}
            </div>
          </div>
        </div>
      </div>
      <div className="flex-1 border rounded-md px-2 shadow-md">
        {activeGroup && <OfferParameterTable parameters={activeGroup.parameters} />}
      </div>
    </div>
  )
}
