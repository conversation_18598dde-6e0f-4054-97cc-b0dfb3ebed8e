import { getTranslations } from 'next-intl/server'

import { EmptyState } from '@/components/empty-state'

import { getParameterGroups } from '../libs/offer-data-helpers'
import { Offer } from '../libs/offer-types'
import { OfferParameterTable } from './offer-parameter-table'
import { OfferParameterTabs } from './offer-parameter-tabs'

interface OfferDetailProps {
  offer: Offer
  packageId: string
}

export async function OfferDetailBottomSection({ offer }: OfferDetailProps) {
  const parameterGroups = getParameterGroups(offer)

  const t = await getTranslations()

  if (!parameterGroups || parameterGroups.length === 0) {
    return (
      <EmptyState
        pageTitle={t('OFFERS.DETAILS.EMPTY_PAGE_TITLE', { defaultValue: 'Offer Details' })}
        title={t('OFFERS.DETAILS.EMPTY_TITLE', { defaultValue: 'No parameters available' })}
        message={t('OFFERS.DETAILS.EMPTY_MESSAGE', {
          defaultValue: 'This offer does not have any parameters to display.',
        })}
      />
    )
  }

  // If only one group, don't show tabs
  if (parameterGroups.length === 1) {
    return <OfferParameterTable parameters={parameterGroups[0].parameters} />
  }

  return <OfferParameterTabs parameterGroups={parameterGroups} />
}
