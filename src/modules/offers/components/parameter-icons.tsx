import Image from 'next/image'

import { cn } from '@/lib/utils'

import { OfferParameterIcon } from '../libs/offer-types'

interface ParameterIconProps {
  icon: OfferParameterIcon
  className?: string
}

// Map icon types to their file paths and dimensions
// Regular versions: neutral/negative colors (dark gray, red)
// _alt versions: positive colors (green, blue)
function getIconPath(iconType: string): { src: string; width: number; height: number } {
  switch (iconType) {
    case 'star':
      return { src: '/icons/star-icon.svg', width: 20, height: 20 }

    case 'europe':
    case 'europe_red':
    case 'europe_alt_red':
      return { src: '/images/offer_icons/europe_alt.svg', width: 22, height: 16 } // Blue background
    case 'smile':
      return { src: '/images/offer_icons/smile.svg', width: 20, height: 20 }
    case 'euro':
    case 'euro_alt':
      return { src: '/images/offer_icons/euro_alt.svg', width: 20, height: 20 }
    case 'plus':
    case 'plus_alt':
      return { src: '/images/offer_icons/plus_alt.svg', width: 20, height: 20 } // Dark gray background
    case 'minus':
    case 'minus_alt':
      return { src: '/images/offer_icons/minus_alt.svg', width: 20, height: 20 } // Dark gray background

    default:
      return { src: '/icons/info-icon.svg', width: 20, height: 20 }
  }
}

export function ParameterIcon({ icon, className }: ParameterIconProps) {
  const iconConfig = getIconPath(icon.type)

  return (
    <div title={icon.tooltip} className={cn('relative flex items-center justify-center', className)}>
      <Image
        src={iconConfig.src}
        alt={icon.tooltip || icon.type}
        width={iconConfig.width}
        height={iconConfig.height}
        className="object-contain"
      />
    </div>
  )
}

interface ParameterIconsListProps {
  icons: OfferParameterIcon[]
  className?: string
}

export function ParameterIconsList({ icons, className }: ParameterIconsListProps) {
  if (!icons || icons.length === 0) {
    return null
  }

  return (
    <div className={cn('flex items-start justify-start gap-1', className)}>
      {icons.map((icon, index) => (
        <ParameterIcon key={index} icon={icon} />
      ))}
    </div>
  )
}
