import { getTranslations } from 'next-intl/server'

import { ErrorBoundary } from '@/components/error-boundary'

import { getOfferById } from '../api/offers-api'
import { OfferDetailBottomSection } from './offer-detail-bottom-section'
import { OfferDetailsTopSection } from './offer-details-top-section'

interface OfferDetailServerProps {
  packageId: string
  offerId: string
}

export async function OfferDetailServer({ packageId, offerId }: OfferDetailServerProps) {
  const t = await getTranslations()

  const offerResponse = await getOfferById({ packageId, id: offerId })

  if (offerResponse?.serverError || !offerResponse?.data) {
    return (
      <ErrorBoundary
        error={offerResponse?.serverError?.message || 'Offer not found'}
        title={t('OFFERS.ERROR.TITLE')}
        description={t('OFFERS.ERROR.DESCRIPTION')}
      />
    )
  }

  const offer = offerResponse?.data

  return (
    <div className="max-w-5xl mx-auto px-4 2xl:px-0 flex flex-col gap-8 md:gap-16">
      {/* Main Content - Two Column Layout */}
      <OfferDetailsTopSection offer={offer} packageId={packageId} />

      <OfferDetailBottomSection offer={offer} packageId={packageId} />
    </div>
  )
}
