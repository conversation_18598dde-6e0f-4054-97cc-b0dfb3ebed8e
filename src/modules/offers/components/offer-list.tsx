import { getTranslations } from 'next-intl/server'

import { EmptyState } from '@/components/empty-state'
import { ErrorBoundary } from '@/components/error-boundary'

import { getOfferPackages } from '../api/offers-api'
import { OfferPackage } from '../libs/offer-types'
import { OfferPackageCard } from './offer-package-card'

export async function OfferList() {
  const t = await getTranslations('OFFERS')

  // Fetch offer packages
  const offerPackagesResponse = await getOfferPackages()

  if (offerPackagesResponse?.serverError) {
    return (
      <ErrorBoundary
        error={offerPackagesResponse.serverError.message}
        title={t('ERROR.TITLE', { defaultValue: 'Failed to load offers' })}
        description={t('ERROR.DESCRIPTION', {
          defaultValue: 'There was an error loading your  Please try again.',
        })}
      />
    )
  }

  const offerPackages = offerPackagesResponse?.data

  if (!offerPackages || offerPackages.length === 0) {
    return (
      <EmptyState
        pageTitle={t('Offers')}
        title={t('LIST.EMPTY_TEXT', { defaultValue: 'No offers available' })}
        message={t('LIST.EMPTY_DESCRIPTION', {
          defaultValue: 'There are currently no insurance offers available for you.',
        })}
      />
    )
  }

  // Group packages by module (following ExtJS pattern)
  const groupedPackages = offerPackages.reduce<Record<string, OfferPackage[]>>((acc, pkg) => {
    const moduleKey = pkg.module || 'Other'
    if (!acc[moduleKey]) {
      acc[moduleKey] = []
    }
    acc[moduleKey].push(pkg)
    return acc
  }, {})

  return (
    <div className="space-y-4">
      {Object.entries(groupedPackages).map(([module, packages], index) => (
        <div key={module} className="space-y-2">
          <OfferPackageCard package={{ ...packages[0], module }} className="w-full" isExpanded={index === 0} />
        </div>
      ))}
    </div>
  )
}
