'use client'

import { useTranslations } from 'next-intl'
import Link from 'next/link'

import { Button } from '@/components/ui/button'
import { CardFooter } from '@/components/ui/card'

import { useOfferActions } from '../hooks/use-offers'
import { Offer } from '../libs/offer-types'

interface ProposalCardFooterProps {
  offer: Offer
  packageId: string
  accepted: boolean
}

export function ProposalCardFooter({ offer, packageId, accepted }: ProposalCardFooterProps) {
  const t = useTranslations()

  const { acceptOffer, revokeOffer, isLoading } = useOfferActions()

  return (
    <CardFooter className="flex justify-center gap-2 p-4 pt-0 flex-wrap items-stretch">
      <Button className="rounded-full flex-1" asChild>
        <Link href={`/offers/packages/${packageId}/proposals/${offer.id}`}>
          {t('OFFERS.OFFERS_LIST.ITEM.DETAILS_BUTTON.TEXT')}
        </Link>
      </Button>
      {accepted ? (
        <Button
          className="rounded-full flex-1 bg-red-600 hover:bg-red-700"
          onClick={() => revokeOffer({ id: offer.id })}
          disabled={isLoading}
        >
          {t('OFFERS.OFFERS_LIST.ITEM.REVOKE_BUTTON.TEXT')}
        </Button>
      ) : (
        <Button
          className="rounded-full flex-1 bg-slate-800 hover:bg-slate-900"
          onClick={() => acceptOffer({ id: offer.id })}
          isLoading={isLoading}
          loadingText={t('Loading...')}
        >
          {t('OFFERS.OFFERS_LIST.ITEM.ACCEPT_BUTTON.TEXT')}
        </Button>
      )}
    </CardFooter>
  )
}
