import { Separator } from '@radix-ui/react-select'
import { getTranslations } from 'next-intl/server'
import Image from 'next/image'

import { cn } from '@/lib/utils'
import { Card, CardContent } from '@/components/ui/card'

import { Offer } from '../libs/offer-types'
import { ProposalCardFooter } from './proposal-card-footer'

interface ProposalCardProps {
  offer: Offer
  packageId: string
}

// Helper function to extract data from productInfo (following ExtJS pattern)
function getOfferDisplayData(offer: Offer) {
  const productInfo = offer.productInfo || {}

  return {
    // Company name - following ExtJS pattern
    company: productInfo.gesellschaft || offer.company || 'N/A',

    // Tariff - following ExtJS calculation logic
    tariff: productInfo.tarif || productInfo.tariflink || productInfo.tarifbez || 'N/A',

    // Insured amount
    insuredAmount: productInfo.deckungssumme || 'N/A',

    // Contribution
    contribution: productInfo.beitrag || 'N/A',

    // Starting date
    startingAt: productInfo.versicherungsbeginn || 'N/A',

    // Payment mode
    paymentMode: productInfo.zahlweise || 'N/A',

    // Logo - following ExtJS pattern
    insurerLogo: productInfo.logos?.versicherer || offer.insurerLogo || '/placeholder-logo.png',
  }
}

export async function ProposalCard({ offer, packageId }: ProposalCardProps) {
  const t = await getTranslations('OFFERS.OFFERS_LIST')

  const recommended = offer.recommended
  const accepted = offer.accepted

  // Extract display data using the helper function
  const displayData = getOfferDisplayData(offer)

  return (
    <div className="relative w-full">
      {/* Recommended banner at the top when enabled */}
      {recommended && (
        <div className="absolute top-0 left-0 w-full h-7 overflow-hidden">
          <div className="h-full w-full gradient-light-blue rounded-t-[10px] px-4 flex items-center justify-center gap-2 text-white text-xs font-normal">
            <Image src="/icons/star-icon.svg" alt="Star icon" width={20} height={20} />
            <span>{t('ITEM.HEADER.RECOMMENDED.TEXT')}</span>
          </div>
        </div>
      )}

      <Card className={cn(` shadow-2xl`, accepted ? 'border-green-500' : '')}>
        <CardContent className="p-4 pt-0 ">
          <div className="flex items-center gap-4 mb-4">
            <div className=" size-[60px] rounded-lg flex items-center justify-center overflow-hidden relative">
              {/* Company logo */}
              <Image
                src={displayData.insurerLogo}
                alt={`${displayData.company} logo`}
                className="object-contain"
                fill
              />
            </div>
            <div>
              <h3 className="text-slate-800 text-base font-bold">{displayData.company}</h3>
            </div>
          </div>

          <Separator className="my-2" />

          <div className="flex flex-col gap-2">
            <CardRow label={t('ITEM.TARIFF_FIELD.LABEL')} value={displayData.tariff} />
            <CardRow label={t('ITEM.COVERAGE_AMOUNT_FIELD.LABEL')} value={displayData.insuredAmount} />
            <CardRow label={t('ITEM.CONTRIBUTION_FIELD.LABEL')} value={displayData.contribution} />
            <CardRow label={t('ITEM.STARTING_AT_FIELD.LABEL')} value={displayData.startingAt} />
            <CardRow label={t('ITEM.PAYMENT_MODE_FIELD.LABEL')} value={displayData.paymentMode} />
          </div>

          <Separator className="mt-4" />
        </CardContent>

        <ProposalCardFooter offer={offer} packageId={packageId} accepted={accepted} />
      </Card>
    </div>
  )
}

const CardRow = ({ label, value }: { label: string; value: string }) => (
  <div className="flex flex-col gap-0.5">
    <span className="text-gray-400 text-sm font-normal">{label}</span>
    <span className="text-slate-800 text-sm font-normal">{value}</span>
  </div>
)
