import { createDetailedError } from '@/lib/fetch-api'
import { fetchAuthenticatedApi } from '@/lib/fetch-with-auth-api'

import { Offer, OfferPackagesResponse, OffersResponse } from '../libs/offer-types'
import { OFFERS_TAGS } from './offers-tags'

export const getOfferPackages = async () => {
  const response = await fetchAuthenticatedApi<OfferPackagesResponse>('/proposal_packages', {
    next: {
      tags: [OFFERS_TAGS.PACKAGES],
    },
  })

  if (response.error) {
    const error = createDetailedError(response.error, response.details)
    return {
      serverError: error,
    }
  }

  return { data: response.data?.data || [] }
}

export const getOffersByPackage = async (packageId: string) => {
  const response = await fetchAuthenticatedApi<OffersResponse>(`/proposal_packages/${packageId}/proposals`, {
    next: {
      tags: [`${OFFERS_TAGS.PROPOSALS}-${packageId}`],
    },
  })

  if (response.error) {
    const error = createDetailedError(response.error, response.details)
    return {
      serverError: error,
    }
  }

  return { data: response.data?.data || [] }
}

export const getOfferById = async ({ packageId, id }: { packageId: string; id: string }) => {
  const response = await fetchAuthenticatedApi<OffersResponse>(`/proposal_packages/${packageId}/proposals`, {
    next: {
      tags: [`${OFFERS_TAGS.DETAIL}-${id}`],
    },
  })

  if (response.error) {
    const error = createDetailedError(response.error, response.details)
    return {
      serverError: error,
    }
  }

  // Find the specific proposal by ID
  const offer = response?.data?.data.find((proposal: Offer) => proposal.id.toString() === id)

  // Clean up problematic fields (following ExtJS pattern)
  if (offer?.productInfo && offer?.productInfo['user.iban_info']) {
    delete offer.productInfo['user.iban_info']
  }

  return { data: offer }
}
