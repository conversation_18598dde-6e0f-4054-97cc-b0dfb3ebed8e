'use server'

import { revalidateTag } from 'next/cache'

import { createDetailedError, fetchApi } from '@/lib/fetch-api'
import { authenticatedAction } from '@/lib/safe-actions'

import { offerActionSchema } from '../types/offer-schema'
import { OFFERS_TAGS } from './offers-tags'

// Accept an offer/proposal
export const acceptOffer = authenticatedAction
  .schema(offerActionSchema.omit({ action: true }))
  .action(async ({ parsedInput: { id }, ctx: { session } }) => {
    const response = await fetchApi(
      `/proposals/${id}/accept`,
      {
        method: 'PATCH',
      },
      session
    )

    if (response.error) {
      throw createDetailedError(response.error, response.details)
    }

    // Revalidate relevant caches
    revalidateTag(OFFERS_TAGS.ALL)
    revalidateTag(`${OFFERS_TAGS.DETAIL}-${id}`)

    return response.data
  })

// Revoke an offer/proposal
export const revokeOffer = authenticatedAction
  .schema(offerActionSchema.omit({ action: true }))
  .action(async ({ parsedInput: { id }, ctx: { session } }) => {
    const response = await fetchApi(
      `/proposals/${id}/revoke`,
      {
        method: 'PATCH',
      },
      session
    )

    if (response.error) {
      throw createDetailedError(response.error, response.details)
    }

    // Revalidate relevant caches
    revalidateTag(OFFERS_TAGS.ALL)
    revalidateTag(`${OFFERS_TAGS.DETAIL}-${id}`)

    return response.data
  })
