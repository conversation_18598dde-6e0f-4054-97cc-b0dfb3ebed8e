import { z } from 'zod'

// Export offer status type
export type OfferStatusType = 'pending' | 'accepted' | 'rejected'

// Define offer status options with type safety
export const offerStatusOptions: Array<{ value: OfferStatusType; label: string }> = [
  { value: 'pending', label: 'OFFERS.STATUS.PENDING' },
  { value: 'accepted', label: 'OFFERS.STATUS.ACCEPTED' },
  { value: 'rejected', label: 'OFFERS.STATUS.REJECTED' },
] as const

// Schema for accepting/rejecting offers
export const offerActionSchema = z.object({
  id: z.coerce.number({
    required_error: 'OFFERS.VALIDATION.ID_REQUIRED',
    invalid_type_error: 'OFFERS.VALIDATION.ID_INVALID',
  }),
  action: z.enum(['accept', 'revoke'], {
    required_error: 'OFFERS.VALIDATION.ACTION_REQUIRED',
  }),
})

// Schema for filtering offers
export const offerFilterSchema = z.object({
  page: z.number().default(1),
  perPage: z.number().default(10),
  search: z.string().optional(),
  status: z.string().optional(),
  packageId: z.string().optional(),
  recommended: z.boolean().optional(),
})

// Type for offer action inputs
export type OfferActionInputs = z.infer<typeof offerActionSchema>

// Type for offer filter inputs
export type OfferFilterInputs = z.infer<typeof offerFilterSchema>
