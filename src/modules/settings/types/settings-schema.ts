import { z } from 'zod'

export const passwordChangeSchema = z
  .object({
    currentPassword: z.string().min(1, 'SETTINGS.VALIDATION.OLD_PASSWORD_REQUIRED'),
    newPassword: z
      .string()
      .min(8, 'SETTINGS.VALIDATION.PASSWORD_MIN_LENGTH')
      .regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/, 'SETTINGS.VALIDATION.PASSWORD_COMPLEXITY'),
    repeatPassword: z.string().min(1, 'SETTINGS.VALIDATION.REPEAT_PASSWORD_REQUIRED'),
  })
  .refine((data) => data.newPassword === data.repeatPassword, {
    message: 'SETTINGS.VALIDATION.PASSWORDS_DONT_MATCH',
    path: ['repeatNewPassword'],
  })

export type PasswordChangeFormInputs = z.infer<typeof passwordChangeSchema>
