'use server'

import { createDetailedError, fetchApi } from '@/lib/fetch-api'
import { authenticatedAction } from '@/lib/safe-actions'

import { passwordChangeSchema } from '../types/settings-schema'

export const updateUserPassword = authenticatedAction
  .schema(passwordChangeSchema)
  .action(async ({ parsedInput, ctx: { session } }) => {
    if (!session?.accessToken) {
      throw new Error('No session found')
    }
    const response = await fetchApi(
      '/change-password',
      {
        method: 'POST',
        body: JSON.stringify(parsedInput),
      },
      session
    )
    if (response.error) {
      throw createDetailedError(response.error, response.details)
    }

    return response.data
  })
