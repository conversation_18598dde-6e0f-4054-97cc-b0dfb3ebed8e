import { getAppConfig } from '@/app-config'
import { getTranslations } from 'next-intl/server'

import { getAppVersion } from '@/lib/get-app-version'
import { Logo, LogoVariant } from '@/components/logo'

export async function HelpCardServer() {
  const config = getAppConfig()
  const t = await getTranslations()

  return (
    <div className="flex-1 overflow-y-auto p-4 justify-center items-center flex flex-col gap-4 bg-error-100 h-[100%] gap-8">
      <Logo variant={LogoVariant.HORIZONTAL} />
      <span>{`${t('HELP.APP_VERSION')}: ${getAppVersion()} ${config.environment}`}</span>
      <span>{t('HELP.FURTHER_INFORMATION_UNDER')}</span>
      <a href={config.mvpUrl} target={'_blank'} className={'underline text-primary'}>
        {config.mvpUrl}
      </a>
    </div>
  )
}
