'use client'

import { use } from 'react'

import { UserProfile } from '@/modules/auth/types/auth-types'
import { DialogTitle } from '@radix-ui/react-dialog'
import Image from 'next/image'
import { usePathname } from 'next/navigation'

import { ApiError } from '@/lib/fetch-api'
import { Sheet, SheetContent, SheetTrigger } from '@/components/ui/sheet'

import { MobileMenu } from './dashboard-mobile-menu'

const titleMap = {
  '/': 'mobilversichert',
  '/contracts': 'Contracts',
  '/offers': 'Offers',
  '/damages': 'Damages',
  '/expert': 'Expert',
}

interface MobileHeaderProps {
  userPromise: Promise<
    | {
        serverError: ApiError
        data?: undefined
      }
    | {
        data: UserProfile | undefined
        serverError?: undefined
      }
  >
}

export function MobileHeader({ userPromise }: MobileHeaderProps) {
  const pathname = usePathname()
  const userData = use(userPromise)
  const user = userData?.data

  let title = 'mobilversichert'

  if (pathname in titleMap) {
    title = titleMap[pathname as keyof typeof titleMap]
  }

  return (
    <header className="flex items-center justify-between bg-white p-4 border-b">
      <Sheet>
        <SheetTrigger aria-label="Open menu">
          <Image src="/icons/menu-icon.svg" alt="Menu" width={24} height={24} className="w-6 h-6" />
        </SheetTrigger>
        <SheetContent side="left" className="w-full p-0">
          <DialogTitle className="sr-only">Menu</DialogTitle>
          {user && <MobileMenu user={user} />}
        </SheetContent>
      </Sheet>
      <h1 className="text-primary font-medium text-lg">{title}</h1>
      <div className="w-6" /> {/* Empty space to balance the header */}
    </header>
  )
}
