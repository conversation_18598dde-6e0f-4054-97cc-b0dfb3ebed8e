import { UserProfile } from '@/modules/auth/types/auth-types'
import { Avatar, AvatarFallback, AvatarImage } from '@radix-ui/react-avatar'
import Image from 'next/image'

interface MobileMenuProps {
  user: UserProfile
}

interface MenuLinkProps {
  icon: string
  label: string
}

export function MobileMenu({ user }: MobileMenuProps) {
  return (
    <div className="h-full flex flex-col bg-[#F7F8F9]">
      <div className="p-4 border-b bg-white drop-shadow-sm">
        <h2 className="text-center text-[#3966B2] font-bold text-base">Menu</h2>
      </div>

      <div className="flex flex-col gap-8 flex-1 overflow-auto">
        {/* Profile Section */}
        <div className="px-4 pt-4">
          <h3 className="text-sm font-bold mb-4 text-[#142A3A]">Profile</h3>
          <div className="bg-white rounded-lg p-4">
            <div className="flex items-center gap-4">
              <Avatar className="rounded-full size-10 relative">
                {user.agencyImgId && <AvatarImage src={user.agencyImgId} className="rounded-full size-10" />}
                <AvatarFallback>
                  {user.firstName?.charAt(0)}
                  {user.lastName?.charAt(0)}
                </AvatarFallback>
              </Avatar>
              <div className="flex-1 min-w-0">
                <p className="font-bold text-sm text-[#142A3A] truncate whitespace-nowrap overflow-hidden">
                  {user.firstName} {user.lastName}
                </p>
                <p className="text-sm text-[#142A3A] opacity-60 truncate whitespace-nowrap overflow-hidden">
                  {user.email}
                </p>
              </div>
              <Image
                src="/icons/chevron-right.svg"
                alt="Arrow"
                width={20}
                height={20}
                className="text-[#C3C9D3] h-5 w-5 flex-shrink-0"
              />
            </div>
          </div>
        </div>

        {/* Overview Section */}
        <div className="px-4">
          <h3 className="text-sm font-bold mb-4 text-[#142A3A]">Overview</h3>
          <div className="bg-white rounded-lg p-3">
            <MenuLink icon="/icons/activity-icon.svg" label="Risks" />
            <div className="h-[1px] bg-gray-100 my-1" />
            <MenuLink icon="/icons/profession-icon.svg" label="Profession" />
          </div>
        </div>

        {/* Further Section */}
        <div className="px-4">
          <h3 className="text-sm font-bold mb-4 text-[#142A3A]">Further</h3>
          <div className="bg-white rounded-lg p-3">
            <MenuLink icon="/icons/customer-benefit-icon.svg" label="Customer Benefit" />
          </div>
        </div>

        {/* Privacy Section */}
        <div className="px-4">
          <h3 className="text-sm font-bold mb-4 text-[#142A3A]">Privacy</h3>
          <div className="bg-white rounded-lg p-3">
            <MenuLink icon="/icons/info-icon.svg" label="Imprint" />
            <div className="h-[1px] bg-gray-100 my-1" />
            <MenuLink icon="/icons/shield-icon.svg" label="Data protection" />
            <div className="h-[1px] bg-gray-100 my-1" />
            <MenuLink icon="/icons/shield-icon.svg" label="Terms of use" />
          </div>
        </div>

        {/* Settings Section */}
        <div className="px-4">
          <h3 className="text-sm font-bold mb-4 text-[#142A3A]">Settings</h3>
          <div className="bg-white rounded-lg p-3">
            <MenuLink icon="/icons/lock-icon.svg" label="Change password" />
            <div className="h-[1px] bg-gray-100 my-1" />
            <MenuLink icon="/icons/globe-icon.svg" label="App language" />
          </div>
        </div>

        {/* Help Section */}
        <div className="px-4">
          <h3 className="text-sm font-bold mb-4 text-[#142A3A]">Help</h3>
          <div className="bg-white rounded-lg p-3">
            <MenuLink icon="/icons/info-icon.svg" label="Version info" />
          </div>
        </div>

        {/* Logout Button */}
        <div className="px-6 mt-auto mb-8">
          <button className="w-full bg-[#9A2921] text-white rounded-full py-[18px] font-bold text-sm">Logout</button>
        </div>
      </div>
    </div>
  )
}

function MenuLink(item: MenuLinkProps) {
  return (
    <div className="flex items-center py-3 px-2">
      <div className="text-[#142A3A] mr-4">
        <Image src={item.icon} alt={item.label} width={16} height={16} className="w-4 h-4" />
      </div>
      <span className="flex-1 text-sm font-normal text-[#142A3A]">{item.label}</span>
      <Image src="/icons/chevron-right.svg" alt="Arrow" width={20} height={20} className="text-[#C3C9D3] h-5 w-5" />
    </div>
  )
}
