import { Skeleton } from '@/components/ui/skeleton'
import { Separator } from '@/components/ui/separator'

export function DashboardHeaderSkeleton() {
  return (
    <header className="p-2 flex items-center justify-end bg-white m-4 mr-0 rounded-lg shadow-sm max-w-fit ml-auto">
      <div className="flex items-center h-full gap-4 text-black font-semibold">
        {/* Agency Avatar and Name */}
        <div className="flex items-center gap-2">
          <Skeleton className="w-8 h-8 rounded-full" />
          <Skeleton className="h-4 w-24" />
        </div>

        {/* Separator */}
        <div className="h-8">
          <Separator orientation="vertical" />
        </div>

        {/* User Section */}
        <div className="flex items-center gap-3">
          {/* User Profile Avatar */}
          <div className="relative">
            <div className="rounded-full overflow-hidden ring-2 ring-white shadow-sm">
              <Skeleton className="size-8 rounded-full" />
            </div>
          </div>

          {/* User Greeting */}
          <div className="flex items-center gap-2">
            <Skeleton className="h-4 w-32" />
            {/* UserHeaderItems skeleton - assuming it's a dropdown or menu */}
            <Skeleton className="w-4 h-4" />
          </div>
        </div>
      </div>
    </header>
  )
}
