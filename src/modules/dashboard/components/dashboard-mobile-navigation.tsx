'use client'

import Image from 'next/image'
import Link from 'next/link'
import { usePathname } from 'next/navigation'

import { cn } from '@/lib/utils'

import { type NavItem } from '../dashboard-types'

const navItems: NavItem[] = [
  { href: '/contracts', icon: '/icons/contracts-icon.svg', label: 'Contracts' },
  { href: '/offers', icon: '/icons/offers-icon.svg', label: 'Offers' },
  { href: '/damages', icon: '/icons/damage-icon.svg', label: 'Damages' },
  { href: '/expert', icon: '/icons/expert-icon.svg', label: 'Expert' },
]

export function MobileNavigation() {
  const pathname = usePathname()

  return (
    <nav className="bg-white border-t py-2 px-4">
      <div className="flex justify-between items-center">
        {navItems.map((item) => (
          <NavItem key={item.href} item={item} isActive={pathname === item.href} />
        ))}
      </div>
    </nav>
  )
}

const NavItem = ({ item, isActive }: { item: NavItem; isActive: boolean }) => {
  return (
    <Link
      prefetch
      href={item.href}
      key={item.href}
      className={cn('flex flex-col items-center', isActive ? 'text-black' : 'text-gray-500')}
    >
      <Image src={item.icon} alt={item.label} width={16} height={16} className="w-4 h-4" />
      <span className={cn('text-xs', isActive ? 'text-black' : 'text-gray-500')}>{item.label}</span>
    </Link>
  )
}
