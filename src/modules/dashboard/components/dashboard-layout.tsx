import React, { Suspense } from 'react'

import { getUserProfile } from '@/modules/profile/api/profile-api'
import Image from 'next/image'

import { DashboardHeader } from './dashboard-header'
import { DashboardHeaderSkeleton } from './dashboard-header-skeleton'
import { MobileHeader } from './dashboard-mobile-header'
import { MobileNavigation } from './dashboard-mobile-navigation'
import { DashboardSidebar } from './dashboard-sidebar'

interface DashboardLayoutProps {
  children: React.ReactNode
}

export function DashboardLayout({ children }: DashboardLayoutProps) {
  const userProfilePromise = getUserProfile()

  return (
    <div className="h-screen relative overflow-hidden">
      <Image
        priority
        width={1920}
        height={1080}
        src="/images/auth-background.png"
        alt="Background"
        className="absolute inset-0 w-full h-full object-fill -z-10"
      />

      {/* Desktop Layout */}
      <div className="hidden md:flex p-4">
        <DashboardSidebar />
        <div className="flex flex-col flex-1  ml-[200px]">
          <Suspense fallback={<DashboardHeaderSkeleton />}>
            <DashboardHeader userPromise={userProfilePromise} />
          </Suspense>
          <div className="h-[90vh] w-full rounded-md border bg-white flex flex-col overflow-hidden">
            <div className=" overflow-y-auto  flex-1 pb-5">{children}</div>
          </div>
        </div>
      </div>

      {/* Mobile Layout */}
      <div className="flex flex-col h-screen md:hidden">
        <Suspense fallback={<div>Loading...</div>}>
          <MobileHeader userPromise={userProfilePromise} />
        </Suspense>

        {/* Mobile Content */}
        <main className="flex-1 overflow-auto bg-white  shadow-sm">
          <div className="h-full overflow-auto  ">{children}</div>
        </main>

        {/* Mobile Bottom Navigation */}
        <MobileNavigation />
      </div>
    </div>
  )
}
