'use client'

import { useTranslations } from 'next-intl'
import Image from 'next/image'
import Link from 'next/link'
import { usePathname } from 'next/navigation'

import { cn } from '@/lib/utils'
import LocaleSwitcher from '@/components/locale-switcher'
import { Logo } from '@/components/logo'

import { NavItem } from '../dashboard-types'

const navItems: NavItem[] = [
  {
    href: '/contracts',
    icon: '/icons/contracts-icon.svg',
    label: 'NAVIGATION.SIDEBAR.CONTRACTS_BUTTON.TEXT',
  },
  {
    href: '/offers',
    icon: '/icons/offers-icon.svg',
    label: 'NAVIGATION.SIDEBAR.OFFERS_BUTTON.TEXT',
  },
  {
    href: '/damages',
    icon: '/icons/damage-icon.svg',
    label: 'NAVIGATION.SIDEBAR.DAMAGES_BUTTON.TEXT',
  },
  {
    href: '/expert',
    icon: '/icons/expert-icon.svg',
    label: 'NAVIGATION.SIDEBAR.EXPERT_BUTTON.TEXT',
  },
  {
    href: '/risk',
    icon: '/icons/risk-icon.svg',
    label: 'NAVIGATION.SIDEBAR.RISK_BUTTON.TEXT',
  },
  {
    href: '/profession',
    icon: '/icons/profession-icon.svg',
    label: 'NAVIGATION.SIDEBAR.PROFESSION_BUTTON.TEXT',
  },
  {
    href: '/settings',
    icon: '/icons/setting-icon.svg',
    label: 'NAVIGATION.SIDEBAR.SETTINGS_BUTTON.TEXT',
  },
  {
    href: '/help',
    icon: '/icons/info-icon.svg',
    label: 'NAVIGATION.SIDEBAR.HELP_BUTTON.TEXT',
  },
]

export function DashboardSidebar() {
  const pathname = usePathname()
  const t = useTranslations()

  return (
    <aside className="w-[200px] px-4 bg-white flex flex-col fixed top-0 left-0 h-full">
      <div className="p-4 flex justify-center">
        <Link href="/contracts" className="flex items-center justify-center">
          <Logo />
        </Link>
      </div>
      <nav className="flex-1 py-2">
        <ul className="space-y-1">
          {navItems.map((item) => {
            const isActive = pathname?.includes(item.href)
            return (
              <li key={item.href}>
                <Link
                  href={item.href}
                  prefetch
                  className={cn(
                    'flex  gap-2 items-center px-2 py-3 text-xs font-medium',
                    isActive ? 'text-success ' : 'text-gray-500 hover:text-gray-900'
                  )}
                >
                  <Image src={item.icon} alt={t(item.label)} width={16} height={16} className="w-4 h-4" />
                  <span>{t(item.label)}</span>
                </Link>
              </li>
            )
          })}
        </ul>
      </nav>
      <div className="p-4 mt-auto">
        <LocaleSwitcher />
      </div>
    </aside>
  )
}
