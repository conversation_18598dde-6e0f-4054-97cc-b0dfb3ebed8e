'use client'

import { use } from 'react'

import { UserProfile } from '@/modules/auth/types/auth-types'
import { useTranslations } from 'next-intl'
import Image from 'next/image'

import { ApiError } from '@/lib/fetch-api'
import { Separator } from '@/components/ui/separator'

import { UserHeaderItems } from './user-header-items'

interface DashboardHeaderProps {
  userPromise: Promise<
    | {
        serverError: ApiError
        data?: undefined
      }
    | {
        data: UserProfile | undefined
        serverError?: undefined
      }
  >
}

export function DashboardHeader({ userPromise }: DashboardHeaderProps) {
  const userData = use(userPromise)
  const user = userData?.data

  const t = useTranslations()

  return (
    <header className="p-2 flex items-center justify-end bg-white m-4 mr-0 rounded-lg shadow-sm max-w-fit ml-auto">
      <div className="flex items-center h-full gap-4 text-black font-semibold">
        <div className="flex items-center gap-2">
          <div className="w-8 h-8 relative rounded-md overflow-hidden">
            <Image src={user?.agencyImgId || ''} alt={user?.agencyName || 'Agency'} fill />
          </div>

          <span className="text-sm">{user?.agencyName}</span>
        </div>

        <div className="h-8">
          <Separator orientation="vertical" />
        </div>

        <div className="flex items-center gap-3">
          {/* Small Profile Avatar */}
          <div className="relative">
            <div className="rounded-full overflow-hidden ring-2 ring-white shadow-sm">
              <div className=" size-8 relative   rounded-md overflow-hidden">
                <Image
                  src={user?.imgId || ''}
                  alt={`${user?.firstName || 'User'} ${user?.lastName || 'Profile'}`}
                  className="object-cover object-center "
                  fill
                />
              </div>
            </div>
          </div>

          {/* User Greeting */}
          <div className="flex items-center">
            <p className="text-sm">
              {t('NAVIGATION.TOPBAR.PROFILE_BUTTON.HELLO_TEXT')},
              <span className="text-primary ml-1">
                {user?.firstName} {user?.lastName}
              </span>
            </p>
            <UserHeaderItems />
          </div>
        </div>
      </div>
    </header>
  )
}
