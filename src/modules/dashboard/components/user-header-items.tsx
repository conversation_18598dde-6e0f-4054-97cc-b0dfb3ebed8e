'use client'

import { SyntheticEvent, useCallback, useState } from 'react'

import { getAppConfig } from '@/app-config'
import { logout } from '@/modules/auth/actions/auth-actions'
import { BadgeInfo, ChevronDown, LogOut, Shield, ShieldUser, User } from 'lucide-react'
import { useTranslations } from 'next-intl'
import Link from 'next/link'

import { Button } from '@/components/ui/button'
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { PDFPreview } from '@/components/documents'
import { DownloadFileButton } from '@/components/download-file-button'

export enum PolicyDocs {
  impressumFileUrl = 'impressumFileUrl',
  dataProtectionFileUrl = 'dataProtectionFileUrl',
  termsOfUseFileUrl = 'termsOfUseFileUrl',
}

const docTranslationHeadlines: Record<PolicyDocs, string> = {
  [PolicyDocs.impressumFileUrl]: 'IMPRINT_BUTTON.TEXT',
  [PolicyDocs.dataProtectionFileUrl]: 'DATA_PROTECTION_BUTTON.TEXT',
  [PolicyDocs.termsOfUseFileUrl]: 'TERMS_OF_USE_BUTTON.TEXT',
}

const getLink = (docMode: PolicyDocs): string => {
  const config = getAppConfig()
  if (config.isProduction) {
    return config[docMode]
  }
  return config[docMode].replace('https://mobilversichert.de/wp-content/uploads/', '/pdf-proxy/')
}

export const UserHeaderItems = () => {
  const t = useTranslations('NAVIGATION.TOPBAR.PROFILE_MENU')
  const [docMode, setDocMode] = useState<PolicyDocs | undefined>()
  const showDoc = (e: SyntheticEvent) => {
    const docMode = e.currentTarget.getAttribute('data-docmode') as PolicyDocs
    if (!docMode) {
      return
    }
    setDocMode(docMode)
  }

  const onCloseDocViewer = useCallback((open: boolean) => !open && setDocMode(undefined), [])

  return (
    <>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="ghost" size="sm" className="h-8 gap-1 font-normal">
            <ChevronDown className="h-4 w-4" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          <DropdownMenuItem asChild>
            <Link href="/profile">
              <User className="h-4 w-4" />
              {t('PROFILE_BUTTON.TEXT')}
            </Link>
          </DropdownMenuItem>
          <DropdownMenuSeparator />
          <DropdownMenuItem data-docmode={PolicyDocs.impressumFileUrl} onClick={showDoc}>
            <BadgeInfo className="h-4 w-4" />
            {t('IMPRINT_BUTTON.TEXT')}
          </DropdownMenuItem>
          <DropdownMenuItem data-docmode={PolicyDocs.dataProtectionFileUrl} onClick={showDoc}>
            <ShieldUser className="h-4 w-4" />
            {t('DATA_PROTECTION_BUTTON.TEXT')}
          </DropdownMenuItem>
          <DropdownMenuItem data-docmode={PolicyDocs.termsOfUseFileUrl} onClick={showDoc}>
            <Shield className="h-4 w-4" />
            {t('TERMS_OF_USE_BUTTON.TEXT')}
          </DropdownMenuItem>
          <DropdownMenuSeparator />
          <DropdownMenuItem onClick={async () => await logout()}>
            <LogOut className="h-4 w-4" />
            {t('LOGOUT_BUTTON.TEXT')}
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
      <Dialog open={!!docMode} onOpenChange={onCloseDocViewer}>
        <DialogContent className="max-w-3xl">
          <DialogHeader>
            <DialogTitle className="text-xl font-semibold text-gray-900 mb-2 gap-2 flex">
              {t(`${docTranslationHeadlines[docMode || PolicyDocs.impressumFileUrl]}`)}
              {!!docMode && <DownloadFileButton downloadUrl={getLink(docMode)} />}
            </DialogTitle>
          </DialogHeader>
          <div className="flex flex-col items-center space-y-4">
            {!!docMode && <PDFPreview fileUrl={getLink(docMode)} />}
          </div>
        </DialogContent>
      </Dialog>
    </>
  )
}
