import { useTranslations } from 'next-intl'
import { useAction } from 'next-safe-action/hooks'
import { useRouter } from 'next/navigation'
import { toast } from 'sonner'

import { createDamage, updateDamage } from '../api/damages-actions'
import { DamageFormInputs } from '../types/damage-schema'

/**
 * Helper function to format error messages with validation details
 */
// eslint-disable-next-line @typescript-eslint/no-explicit-any
function formatErrorMessage(error: any, t: (key: string) => string): { title: string; description: string } {
  const serverError = error.serverError || error.message || t('An unexpected error occurred')

  // Check if it's our custom ApiError with validation details
  if (error.serverError && typeof error.serverError === 'string') {
    try {
      // Try to parse if the server error contains our formatted validation errors
      if (error.serverError.includes('Validation errors:')) {
        const [mainMessage, validationPart] = error.serverError.split('. Validation errors: ')

        return {
          title: mainMessage || t('Validation Error'),
          description: validationPart || serverError,
        }
      }
    } catch {
      // If parsing fails, fall back to original error
    }
  }

  return {
    title: t('Error'),
    description: serverError,
  }
}

/**
 * Hook for creating and updating damages
 * Follows the same pattern as useContracts from contracts module
 * Provides loading states, success/error handling, and navigation
 */
export function useDamageForm() {
  const router = useRouter()
  const t = useTranslations()

  const { execute, isExecuting } = useAction(createDamage, {
    onSuccess: () => {
      toast.success(
        t('DAMAGES.EDIT_FORM.CREATE_MODE.TOAST.SUCCESS.MESSAGE', {
          defaultValue: 'Damage created successfully',
        })
      )
      router.push('/damages')
    },
    onError: ({ error }) => {
      const { title, description } = formatErrorMessage(error, t)

      toast.error(title, {
        description: description,
        duration: 8000, // Longer duration for validation errors
      })
    },
  })

  const { isExecuting: isUpdating } = useAction(updateDamage, {
    onSuccess: () => {
      toast.success(
        t('DAMAGES.EDIT_FORM.EDIT_MODE.TOAST.SUCCESS.MESSAGE', {
          defaultValue: 'Damage updated successfully',
        })
      )
      router.push('/damages')
    },
    onError: ({ error }) => {
      const { title, description } = formatErrorMessage(error, t)

      toast.error(title, {
        description: description,
        duration: 8000, // Longer duration for validation errors
      })
    },
  })

  const executeAsync = async (data: DamageFormInputs) => {
    if (data.editForm) {
    } else {
      return execute(data)
    }
  }

  return {
    executeAsync,
    isPending: isExecuting || isUpdating,
  }
}
