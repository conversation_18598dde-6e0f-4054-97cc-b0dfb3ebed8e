import { z } from 'zod'

// Define comprehensive damage type enum based on ExtJS implementation
const DamageTypeEnum = z.enum([
  // Vehicle related (storeItems1)
  'KH_PERSONAL',
  'KH_PROPERTY',
  'KH_P_C',
  'KH_ASSET',
  'VK',
  'COVERAGE_ADD',
  'DRIVERPLUS_IU',
  'TK_GLASS',
  'TK_ED',
  'TK_ST_H',
  'TK_WILDANIMAL',
  'TK_OTHERS',
  'REAR_COLLISION',
  'LOADING_UNLOADING_DAMAGE',
  'SALVAGE_DAMAGE',
  'HIT_AND_RUN',
  'BIKE_CAR',
  'HAIL_DAMAGE',
  'MARTEN_BITE',
  'GLITCH',
  'PROTECTION_LETTER_DAMAGE',
  'ROCKFALL',
  'TOTAL_LOSS',
  'VANDALISM',

  // Electronics/Glass related (storeItems2)
  'THEFT',
  'DAMAGE',
  'LIGHTNING_STRIKE',
  'LIGHTNING_SURGE',
  'BURGLARY',
  'BURGLARY_VANDALISM',
  'EL_DAMAGE',
  'FIRE',
  'ATTEMPTED_BURGLARY',

  // Building related (storeItems3)
  'ASSET',
  'INSOLVENCY',
  'PIPE_DAMAGE',
  'BROKEN_GLASS',
  'GLASS',
  'BURGLARY_THEFT',

  // Household related (storeItems4)
  'WATER',
  'STORM',
  'HAIL',
  'E_D_VANDALISMUS',
  'ELEMENTARY',
  'BIKE_ED',
  'ALL_RISK',
  'LEAK_TEST',
  'BICYCLE_THEFT',

  // Life/Health related (storeItems5)
  'DEATH',
  'DISABILITY',
  'PROFESSION_CARE',
  'PERSONAL_INJURY',

  // Health related (storeItems6)
  'AMBULATORY',
  'STATONARY',
  'TOOTH',

  // Liability related (storeItems7)
  'PERSONAL',
  'PROPERTY',
  'P_S',

  // Accident related (storeItems8)
  'KHT_GG',

  // Residential related (storeItems9)
  'KEY_BREAK',
  'GRAFFITI',

  // Personal liability related (storeItems10)
  'DUTY_SUPERVISION',
  'DEPRIVATION',
  'EXTERNAL_DAMAGE_AGENCY',
  'LIABILITY_DAMAGE',
  'ONLINE_FRAUD',
  'LEGAL_PROTECTION',
  'PET_OWNER_LIABILITY',
  'PECUNIARY_LOSS',

  // Common/Others
  'OTHERS',
  'ACCIDENT',
  'CAR_DAMAGE',
  'KH_DAMAGE',
  'LW_DAMAGE',
  'STORM_DAMAGE',
  'PARTIALLY_COMPREHENSIVE',
  'FULLY_COMPREHENSIVE_DAMAGE',
  'WILD_ANIMAL_DAMAGE',
] as const)

const DamageEntityEnum = z.enum([
  'TREE',
  'GLASSES',
  'BIKE',
  'TV',
  'BUILDING',
  'DISHWASHER',
  'MOBILE',
  'CAR',
  'AWNING',
  'WALL',
  'NAVI',
  'PHONE_INTERCOM',
  'STAIRS',
  'DOOR',
  'FRONT_WALL',
  'OTHERS',
] as const)

const DamageReasonEnum = z.enum([
  'TOWING_COSTS',
  'WASTE_WATER',
  'COLLISION_REAR',
  'FREEWAY',
  'EVADE',
  'UNLOADING_DAMAGE',
  'DAMAGE_OTHER_VEHICLE',
  'BURGLARY',
  'ROAD_WIDTH',
  'BIKE_THEFT',
  'GLITCH',
  'PIPE_BROKE',
  'MANOEUVRING',
  'LANE_CHANGE',
  'HEAVY_RAIN',
  'ROCKFALL',
  'STORM',
  'THEFT_ALL',
  'STORM_TREE',
  'CARELESSNESS',
  'UNKNOWN',
  'VANDALISM',
  'CLOGGED',
  'CONSTIPATION',
  'THEFT_ATTEMPT',
  'WILD_ANIMAL_DAMAGE',
  'OTHERS',
] as const)

const DamageStatusEnum = z.enum(['PENDING', 'ACTIVE', 'CLOSED', 'REJECTED'] as const)

// Helper function for string to boolean transformation (reusable)
const stringToBooleanTransform = z.union([z.string(), z.boolean()]).transform((val) => {
  if (typeof val === 'boolean') return val
  if (val === 'true') return true
  if (val === 'false') return false
  throw new Error('Invalid boolean string value')
})

// Helper function for boolean to string transformation (for form display)
export const booleanToStringTransform = z.union([z.string(), z.boolean()]).transform((val) => {
  if (typeof val === 'string') return val
  return val ? 'true' : 'false'
})

// Export inferred types (following documents-types.ts pattern)
export type DamageType = z.infer<typeof DamageTypeEnum>
export type DamageEntityType = z.infer<typeof DamageEntityEnum>
export type DamageReasonType = z.infer<typeof DamageReasonEnum>
export type DamageStatusType = z.infer<typeof DamageStatusEnum>

// Define damage type options with type safety
export const damageTypeOptions: Array<{ value: DamageType; label: string }> = [
  { value: 'THEFT', label: 'DAMAGES.EDIT_FORM.TYPE_FIELD.OPTION.THEFT.TEXT' },
  { value: 'FIRE', label: 'DAMAGES.EDIT_FORM.TYPE_FIELD.OPTION.FIRE.TEXT' },
  { value: 'WATER', label: 'DAMAGES.EDIT_FORM.TYPE_FIELD.OPTION.WATER.TEXT' },
  { value: 'STORM', label: 'DAMAGES.EDIT_FORM.TYPE_FIELD.OPTION.STORM.TEXT' },
  { value: 'HAIL', label: 'DAMAGES.EDIT_FORM.TYPE_FIELD.OPTION.HAIL.TEXT' },
  { value: 'VANDALISM', label: 'DAMAGES.EDIT_FORM.TYPE_FIELD.OPTION.VANDALISM.TEXT' },
  { value: 'GLASS', label: 'DAMAGES.EDIT_FORM.TYPE_FIELD.OPTION.GLASS.TEXT' },
  { value: 'BURGLARY', label: 'DAMAGES.EDIT_FORM.TYPE_FIELD.OPTION.BURGLARY.TEXT' },
  { value: 'ACCIDENT', label: 'DAMAGES.EDIT_FORM.TYPE_FIELD.OPTION.ACCIDENT.TEXT' },
  { value: 'OTHERS', label: 'DAMAGES.EDIT_FORM.TYPE_FIELD.OPTION.OTHERS.TEXT' },
] as const

// Define damage entity options with type safety
export const damageEntityOptions: Array<{ value: DamageEntityType; label: string }> = [
  { value: 'TREE', label: 'DAMAGES.EDIT_FORM.ENTITY_FIELD.OPTION.TREE.TEXT' },
  { value: 'GLASSES', label: 'DAMAGES.EDIT_FORM.ENTITY_FIELD.OPTION.GLASSES.TEXT' },
  { value: 'BIKE', label: 'DAMAGES.EDIT_FORM.ENTITY_FIELD.OPTION.BIKE.TEXT' },
  { value: 'TV', label: 'DAMAGES.EDIT_FORM.ENTITY_FIELD.OPTION.TV.TEXT' },
  { value: 'BUILDING', label: 'DAMAGES.EDIT_FORM.ENTITY_FIELD.OPTION.BUILDING.TEXT' },
  { value: 'DISHWASHER', label: 'DAMAGES.EDIT_FORM.ENTITY_FIELD.OPTION.DISHWASHER.TEXT' },
  { value: 'MOBILE', label: 'DAMAGES.EDIT_FORM.ENTITY_FIELD.OPTION.MOBILE.TEXT' },
  { value: 'CAR', label: 'DAMAGES.EDIT_FORM.ENTITY_FIELD.OPTION.CAR.TEXT' },
  { value: 'AWNING', label: 'DAMAGES.EDIT_FORM.ENTITY_FIELD.OPTION.AWNING.TEXT' },
  { value: 'WALL', label: 'DAMAGES.EDIT_FORM.ENTITY_FIELD.OPTION.WALL.TEXT' },
  { value: 'NAVI', label: 'DAMAGES.EDIT_FORM.ENTITY_FIELD.OPTION.NAVI.TEXT' },
  { value: 'PHONE_INTERCOM', label: 'DAMAGES.EDIT_FORM.ENTITY_FIELD.OPTION.PHONE_INTERCOM.TEXT' },
  { value: 'STAIRS', label: 'DAMAGES.EDIT_FORM.ENTITY_FIELD.OPTION.STAIRS.TEXT' },
  { value: 'DOOR', label: 'DAMAGES.EDIT_FORM.ENTITY_FIELD.OPTION.DOOR.TEXT' },
  { value: 'FRONT_WALL', label: 'DAMAGES.EDIT_FORM.ENTITY_FIELD.OPTION.FRONT_WALL.TEXT' },
  { value: 'OTHERS', label: 'DAMAGES.EDIT_FORM.ENTITY_FIELD.OPTION.OTHERS.TEXT' },
] as const

// Define damage reason options with type safety
export const damageReasonOptions: Array<{ value: DamageReasonType; label: string }> = [
  { value: 'TOWING_COSTS', label: 'DAMAGES.EDIT_FORM.REASON_FIELD.OPTION.TOWING_COSTS.TEXT' },
  { value: 'WASTE_WATER', label: 'DAMAGES.EDIT_FORM.REASON_FIELD.OPTION.WASTE_WATER.TEXT' },
  { value: 'COLLISION_REAR', label: 'DAMAGES.EDIT_FORM.REASON_FIELD.OPTION.COLLISION_REAR.TEXT' },
  { value: 'FREEWAY', label: 'DAMAGES.EDIT_FORM.REASON_FIELD.OPTION.FREEWAY.TEXT' },
  { value: 'EVADE', label: 'DAMAGES.EDIT_FORM.REASON_FIELD.OPTION.EVADE.TEXT' },
  { value: 'UNLOADING_DAMAGE', label: 'DAMAGES.EDIT_FORM.REASON_FIELD.OPTION.UNLOADING_DAMAGE.TEXT' },
  { value: 'DAMAGE_OTHER_VEHICLE', label: 'DAMAGES.EDIT_FORM.REASON_FIELD.OPTION.DAMAGE_OTHER_VEHICLE.TEXT' },
  { value: 'BURGLARY', label: 'DAMAGES.EDIT_FORM.REASON_FIELD.OPTION.BURGLARY.TEXT' },
  { value: 'ROAD_WIDTH', label: 'DAMAGES.EDIT_FORM.REASON_FIELD.OPTION.ROAD_WIDTH.TEXT' },
  { value: 'BIKE_THEFT', label: 'DAMAGES.EDIT_FORM.REASON_FIELD.OPTION.BIKE_THEFT.TEXT' },
  { value: 'GLITCH', label: 'DAMAGES.EDIT_FORM.REASON_FIELD.OPTION.GLITCH.TEXT' },
  { value: 'PIPE_BROKE', label: 'DAMAGES.EDIT_FORM.REASON_FIELD.OPTION.PIPE_BROKE.TEXT' },
  { value: 'MANOEUVRING', label: 'DAMAGES.EDIT_FORM.REASON_FIELD.OPTION.MANOEUVRING.TEXT' },
  { value: 'LANE_CHANGE', label: 'DAMAGES.EDIT_FORM.REASON_FIELD.OPTION.LANE_CHANGE.TEXT' },
  { value: 'HEAVY_RAIN', label: 'DAMAGES.EDIT_FORM.REASON_FIELD.OPTION.HEAVY_RAIN.TEXT' },
  { value: 'ROCKFALL', label: 'DAMAGES.EDIT_FORM.REASON_FIELD.OPTION.ROCKFALL.TEXT' },
  { value: 'STORM', label: 'DAMAGES.EDIT_FORM.REASON_FIELD.OPTION.STORM.TEXT' },
  { value: 'THEFT_ALL', label: 'DAMAGES.EDIT_FORM.REASON_FIELD.OPTION.THEFT_ALL.TEXT' },
  { value: 'STORM_TREE', label: 'DAMAGES.EDIT_FORM.REASON_FIELD.OPTION.STORM_TREE.TEXT' },
  { value: 'CARELESSNESS', label: 'DAMAGES.EDIT_FORM.REASON_FIELD.OPTION.CARELESSNESS.TEXT' },
  { value: 'UNKNOWN', label: 'DAMAGES.EDIT_FORM.REASON_FIELD.OPTION.UNKNOWN.TEXT' },
  { value: 'VANDALISM', label: 'DAMAGES.EDIT_FORM.REASON_FIELD.OPTION.VANDALISM.TEXT' },
  { value: 'CLOGGED', label: 'DAMAGES.EDIT_FORM.REASON_FIELD.OPTION.CLOGGED.TEXT' },
  { value: 'CONSTIPATION', label: 'DAMAGES.EDIT_FORM.REASON_FIELD.OPTION.CONSTIPATION.TEXT' },
  { value: 'THEFT_ATTEMPT', label: 'DAMAGES.EDIT_FORM.REASON_FIELD.OPTION.THEFT_ATTEMPT.TEXT' },
  { value: 'WILD_ANIMAL_DAMAGE', label: 'DAMAGES.EDIT_FORM.REASON_FIELD.OPTION.WILD_ANIMAL_DAMAGE.TEXT' },
  { value: 'OTHERS', label: 'DAMAGES.EDIT_FORM.REASON_FIELD.OPTION.OTHERS.TEXT' },
] as const

// Base schema for damage fields (using Zod v4 best practices)
export const damageSchema = z.object({
  // Basic damage information - title is optional, not required like in ExtJS
  title: z.string().optional(),

  description: z.string().optional(),

  // Damage classification using enum schemas
  type: DamageTypeEnum.optional(),
  entity: DamageEntityEnum.optional(),
  reason: DamageReasonEnum.optional(),

  // Financial information (fixed Zod v4 error handling)
  estimatedAmount: z.coerce
    .number({
      invalid_type_error: 'DAMAGES.VALIDATION.AMOUNT_INVALID',
    })
    .min(0, 'DAMAGES.VALIDATION.AMOUNT_POSITIVE')
    .optional(),
  currency: z.string().default('EUR'),

  // Dates
  date: z
    .date({
      invalid_type_error: 'DAMAGES.VALIDATION.DATE_INVALID',
    })
    .optional(),

  providerReportDate: z
    .date({
      invalid_type_error: 'DAMAGES.VALIDATION.DATE_INVALID',
    })
    .optional(),

  policeReportDate: z
    .date({
      invalid_type_error: 'DAMAGES.VALIDATION.DATE_INVALID',
    })
    .optional(),

  // Additional fields from ExtJS
  internalNumber: z.string().optional(),
  clientDamageNumber: z.string().optional(),
  location: z.string().optional(),
  witnesses: z.string().optional(),
  externalNumberPolice: z.string().optional(),

  // New required fields from backend validation (both are boolean on backend)
  rentedObject: stringToBooleanTransform,
  isPersonInjured: stringToBooleanTransform,

  // Required contract reference
  contractId: z
    .string({
      required_error: 'DAMAGES.VALIDATION.CONTRACT_REQUIRED',
    })
    .min(1, 'DAMAGES.VALIDATION.CONTRACT_REQUIRED'),

  // File uploads and form metadata
  files: z.array(z.any()).optional(),
  editForm: z.boolean().optional(),
  damageId: z.string().optional(),
})

// Type for form inputs
export type DamageFormInputs = z.infer<typeof damageSchema>

// Schema for creating damage
export const createDamageSchema = damageSchema.omit({ editForm: true, damageId: true })

// Schema for updating damage
export const updateDamageSchema = damageSchema.extend({
  editForm: z.literal(true),
  damageId: z.string({
    required_error: 'DAMAGES.VALIDATION.DAMAGE_ID_REQUIRED',
  }),
  status: DamageStatusEnum.optional(),
  actualAmount: z.coerce
    .number({
      invalid_type_error: 'DAMAGES.VALIDATION.AMOUNT_INVALID',
    })
    .min(0, 'DAMAGES.VALIDATION.AMOUNT_POSITIVE')
    .optional(),
})

// Type for create damage inputs
export type CreateDamageInputs = z.infer<typeof createDamageSchema>

// Type for update damage inputs
export type UpdateDamageInputs = z.infer<typeof updateDamageSchema>

// Utility function to get valid damage types based on contract module (from ExtJS logic)
export function getDamageTypesByModule(module?: string): Array<{ value: DamageType; label: string }> {
  let damageTypes: DamageType[] = []

  switch (module) {
    case 'ANHAENGER':
    case 'CAMPING':
    case 'EVB':
    case 'KRAD':
    case 'LKWL':
    case 'PKW':
      damageTypes = [
        'KH_PERSONAL',
        'KH_PROPERTY',
        'KH_P_C',
        'KH_ASSET',
        'VK',
        'COVERAGE_ADD',
        'DRIVERPLUS_IU',
        'TK_GLASS',
        'TK_ED',
        'TK_ST_H',
        'TK_WILDANIMAL',
        'TK_OTHERS',
        'OTHERS',
        'REAR_COLLISION',
        'LOADING_UNLOADING_DAMAGE',
        'SALVAGE_DAMAGE',
        'HIT_AND_RUN',
        'BIKE_CAR',
        'HAIL_DAMAGE',
        'MARTEN_BITE',
        'GLITCH',
        'PROTECTION_LETTER_DAMAGE',
        'ROCKFALL',
        'TOTAL_LOSS',
        'VANDALISM',
      ]
      break

    case 'ART_INSURANCE':
    case 'GLAS':
    case 'ELECTRONIC':
    case 'BOAT':
    case 'GEV':
      damageTypes = [
        'THEFT',
        'DAMAGE',
        'OTHERS',
        'LIGHTNING_STRIKE',
        'LIGHTNING_SURGE',
        'BURGLARY',
        'BURGLARY_VANDALISM',
        'EL_DAMAGE',
        'FIRE',
        'VANDALISM',
        'ATTEMPTED_BURGLARY',
      ]
      break

    case 'BAU':
    case 'BAU_VERSICHERUNG':
      damageTypes = [
        'ASSET',
        'DAMAGE',
        'INSOLVENCY',
        'OTHERS',
        'PIPE_DAMAGE',
        'BROKEN_GLASS',
        'GLASS',
        'FIRE',
        'BURGLARY_VANDALISM',
        'BURGLARY_THEFT',
      ]
      break

    case 'HAU':
      damageTypes = [
        'FIRE',
        'WATER',
        'STORM',
        'HAIL',
        'E_D_VANDALISMUS',
        'THEFT',
        'ELEMENTARY',
        'GLASS',
        'BIKE_ED',
        'ALL_RISK',
        'OTHERS',
        'LIGHTNING_STRIKE',
        'LIGHTNING_SURGE',
        'LEAK_TEST',
        'BURGLARY',
        'BURGLARY_VANDALISM',
        'BURGLARY_THEFT',
        'EL_DAMAGE',
        'BICYCLE_THEFT',
        'BROKEN_GLASS',
        'ATTEMPTED_BURGLARY',
      ]
      break

    case 'KLV':
    case 'LRV':
    case 'RIE':
    case 'RLV':
    case 'PRV':
    case 'BU':
    case 'CARE_PENSION':
      damageTypes = ['DEATH', 'ASSET', 'DISABILITY', 'PROFESSION_CARE', 'OTHERS', 'PERSONAL_INJURY']
      break

    case 'KV':
    case 'ANIMAL_SICK':
      damageTypes = ['AMBULATORY', 'STATONARY', 'TOOTH', 'OTHERS']
      break

    case 'THI':
    case 'HUN':
    case 'PFE':
    case 'HUG':
    case 'OTHERS':
    case 'RES':
    case 'SGV':
    case 'BICYCLE_EBIKE':
    case 'TRAVEL_CANCELLATION':
    case 'TRAVEL_LUGGAGE':
    case 'LOSS_OF_RENT':
    case 'GEW':
    case 'GBHV':
    case 'GFRV':
      damageTypes = ['PERSONAL', 'PROPERTY', 'P_S', 'ASSET', 'OTHERS']
      break

    case 'UNF':
      damageTypes = ['DISABILITY', 'DEATH', 'KHT_GG', 'OTHERS', 'PERSONAL_INJURY']
      break

    case 'WOH':
      damageTypes = [
        'FIRE',
        'WATER',
        'STORM',
        'HAIL',
        'E_D_VANDALISMUS',
        'THEFT',
        'ELEMENTARY',
        'GLASS',
        'OTHERS',
        'ATTEMPTED_BURGLARY',
        'KEY_BREAK',
        'PIPE_DAMAGE',
        'GRAFFITI',
        'BROKEN_GLASS',
        'BURGLARY',
        'LEAK_TEST',
        'LIGHTNING_SURGE',
        'LIGHTNING_STRIKE',
      ]
      break

    case 'PHV':
      damageTypes = [
        'PERSONAL',
        'PROPERTY',
        'P_S',
        'ASSET',
        'OTHERS',
        'KEY_BREAK',
        'DUTY_SUPERVISION',
        'DEPRIVATION',
        'EXTERNAL_DAMAGE_AGENCY',
        'LIABILITY_DAMAGE',
        'ONLINE_FRAUD',
        'LEGAL_PROTECTION',
        'PET_OWNER_LIABILITY',
        'PECUNIARY_LOSS',
      ]
      break

    default:
      // Default damage types for unknown modules
      damageTypes = [
        'PERSONAL',
        'PROPERTY',
        'P_S',
        'ASSET',
        'REAR_COLLISION',
        'DUTY_SUPERVISION',
        'LOADING_UNLOADING_DAMAGE',
        'DEPRIVATION',
        'SALVAGE_DAMAGE',
        'DISABILITY',
        'LIGHTNING_STRIKE',
        'LIGHTNING_SURGE',
        'LEAK_TEST',
        'THEFT',
        'BURGLARY',
        'BURGLARY_VANDALISM',
        'BURGLARY_THEFT',
        'EL_DAMAGE',
        'HIT_AND_RUN',
        'BIKE_CAR',
        'BICYCLE_THEFT',
        'FIRE',
        'EXTERNAL_DAMAGE_AGENCY',
        'GLASS',
        'BROKEN_GLASS',
        'GRAFFITI',
        'LIABILITY_DAMAGE',
        'HAIL_DAMAGE',
        'CAR_DAMAGE',
        'KH_DAMAGE',
        'LW_DAMAGE',
        'MARTEN_BITE',
        'ONLINE_FRAUD',
        'GLITCH',
        'PERSONAL_INJURY',
        'LEGAL_PROTECTION',
        'PIPE_DAMAGE',
        'DAMAGE',
        'KEY_BREAK',
        'PROTECTION_LETTER_DAMAGE',
        'ROCKFALL',
        'STORM_DAMAGE',
        'PARTIALLY_COMPREHENSIVE',
        'PET_OWNER_LIABILITY',
        'TOTAL_LOSS',
        'ACCIDENT',
        'VANDALISM',
        'PECUNIARY_LOSS',
        'ATTEMPTED_BURGLARY',
        'FULLY_COMPREHENSIVE_DAMAGE',
        'WILD_ANIMAL_DAMAGE',
        'OTHERS',
      ]
      break
  }

  // Map to options with translation keys
  return damageTypes.map((type) => ({
    value: type,
    label: `DAMAGES.EDIT_FORM.TYPE_FIELD.OPTION.${type}.TEXT`,
  }))
}
