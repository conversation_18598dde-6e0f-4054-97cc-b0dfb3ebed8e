/**
 * Damage types and interfaces
 * Following the same pattern as contracts module
 */

import { BaseDocument } from '@/components/documents/types'

// Damage status enum (following backend API)
export type DamageStatus = 'PENDING' | 'ACTIVE' | 'CLOSED' | 'REJECTED' | 'OPEN'

// Damage types from ExtJS controller (simplified main categories)
export type DamageType =
  | 'THEFT'
  | 'FIRE'
  | 'WATER'
  | 'STORM'
  | 'HAIL'
  | 'VANDALISM'
  | 'GLASS'
  | 'BURGLARY'
  | 'ACCIDENT'
  | 'DUTY_SUPERVISION'
  | 'OTHERS'

// Damage entity types (matching ExtJS implementation)
export type DamageEntity =
  | 'TREE'
  | 'GLASSES'
  | 'BIKE'
  | 'TV'
  | 'BUILDING'
  | 'DISHWASHER'
  | 'MOBILE'
  | 'CAR'
  | 'AWNING'
  | 'WALL'
  | 'NAVI'
  | 'PHONE_INTERCOM'
  | 'STAIRS'
  | 'DOOR'
  | 'FRONT_WALL'
  | 'OTHERS'

// Damage reason types (matching ExtJS implementation)
export type DamageReason =
  | 'TOWING_COSTS'
  | 'WASTE_WATER'
  | 'COLLISION_REAR'
  | 'FREEWAY'
  | 'EVADE'
  | 'UNLOADING_DAMAGE'
  | 'DAMAGE_OTHER_VEHICLE'
  | 'BURGLARY'
  | 'ROAD_WIDTH'
  | 'BIKE_THEFT'
  | 'GLITCH'
  | 'PIPE_BROKE'
  | 'MANOEUVRING'
  | 'LANE_CHANGE'
  | 'HEAVY_RAIN'
  | 'ROCKFALL'
  | 'STORM'
  | 'THEFT_ALL'
  | 'STORM_TREE'
  | 'CARELESSNESS'
  | 'UNKNOWN'
  | 'VANDALISM'
  | 'CLOGGED'
  | 'CONSTIPATION'
  | 'THEFT_ATTEMPT'
  | 'WILD_ANIMAL_DAMAGE'
  | 'OTHERS'

// Main Damage interface (matching backend API response)
export interface Damage {
  id: string
  contractId: string

  // Agency/Agent information
  agencyId?: string
  agentId?: string
  clientId?: string

  // Basic damage information
  internalNumber?: string
  clientDamageNumber?: string
  title?: string
  description?: string
  location?: string
  witnesses?: string

  // Damage classification
  type?: DamageType
  entity?: DamageEntity
  reason?: DamageReason
  status: DamageStatus

  // Financial information
  estimatedAmount?: number
  actualAmount?: number
  currency?: string
  totalDamagePayments?: number
  reserve?: number | null

  // Dates
  date?: string
  providerReportDate?: string
  policeReportDate?: string
  clientReportDate?: string | null

  // Provider information
  externalNumberProvider?: string | null
  contactPersonProvider?: string | null
  contactPersonClient?: string | null

  // Police report information
  externalNumberPolice?: string

  // Expert information
  expertId?: string | null
  expert?: string | null

  // Tax information
  taxDeduction?: boolean
  vnTaxDeduction?: string | null

  // Causer information
  causerType?: string | null
  causerBirthdate?: string | null
  causerName?: string | null

  // Contract detailed information (from backend response)
  contractInsuranceNumber?: string
  contractStatus?: string
  contractCategoryName?: string
  contractProviderName?: string
  contractProductName?: string

  // New required fields from backend validation
  rentedObject?: boolean
  isPersonInjured?: boolean

  // System timestamps (optional since not always present in API response)
  createdAt?: string
  updatedAt?: string

  // Related data (legacy structure for compatibility)
  contract?: {
    id: string
    insurance_number?: string
    provider_name?: string
    category_name?: string
  }

  // Documents
  documents?: BaseDocument[]
}

// Damage document interface

// Damage list item (simplified for list view)
export interface DamageListItem {
  id: string
  contractId: string
  internalNumber?: string
  title?: string
  date?: string
  status: DamageStatus

  // For display purposes
  displayTitle: string
  displaySubtitle: string
}

// API response types
export interface DamagesResponse {
  damages: Damage[]
  pagination?: {
    page: number
    perPage: number
    totalItems: number
    totalPages: number
  }
}

// Constants for damage types (from ExtJS)
export const DAMAGE_TYPES: Record<DamageType, string> = {
  THEFT: 'Theft',
  FIRE: 'Fire',
  WATER: 'Water Damage',
  STORM: 'Storm',
  HAIL: 'Hail',
  VANDALISM: 'Vandalism',
  GLASS: 'Glass Damage',
  BURGLARY: 'Burglary',
  ACCIDENT: 'Accident',
  DUTY_SUPERVISION: 'Duty Supervision',
  OTHERS: 'Others',
}

export const DAMAGE_ENTITIES: Record<DamageEntity, string> = {
  TREE: 'Tree',
  GLASSES: 'Glasses',
  BIKE: 'Bike',
  TV: 'TV',
  BUILDING: 'Building',
  DISHWASHER: 'Dishwasher',
  MOBILE: 'Mobile',
  CAR: 'Car',
  AWNING: 'Awning',
  WALL: 'Wall',
  NAVI: 'Navigation',
  PHONE_INTERCOM: 'Phone/Intercom',
  STAIRS: 'Stairs',
  DOOR: 'Door',
  FRONT_WALL: 'Front Wall',
  OTHERS: 'Others',
}

export const DAMAGE_STATUSES: Record<DamageStatus, string> = {
  PENDING: 'Pending',
  ACTIVE: 'Active',
  CLOSED: 'Closed',
  REJECTED: 'Rejected',
  OPEN: 'Open',
}
