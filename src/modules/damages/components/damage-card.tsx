import { ChevronRight } from 'lucide-react'
import Image from 'next/image'
import Link from 'next/link'

import { Card, CardContent } from '@/components/ui/card'

import { Damage } from '../types/damage-types'

interface DamageCardProps {
  damage: Damage
}

export async function DamageCard({ damage }: DamageCardProps) {
  // Create display title and subtitle following Figma design
  const displayTitle = damage.title || `Damage_${damage.internalNumber || damage.id.slice(-4)}`
  const displaySubtitle = damage.type || 'Label'

  return (
    <Link href={`/damages/${damage.id}/contract/${damage.contractId}`}>
      <Card className="border border-[#E5E7EB] overflow-hidden rounded-[10px] shadow-[0px_12px_38px_0px_rgba(0,66,117,0.10)] transition-all duration-200 hover:border-[#3BCBBF] focus:border-[#3BCBBF]">
        <CardContent className="px-6  h-full">
          <div className="flex justify-between items-center h-full">
            {/* Left side - Icon and text */}
            <div className="flex justify-start items-center gap-2.5">
              {/* Damage Icon - Following Figma design */}
              <div className="w-6 h-6 relative flex-shrink-0">
                <Image
                  src="/icons/damage-icon.svg"
                  alt="Document icon"
                  className="w-full h-full"
                  width={24}
                  height={24}
                />
              </div>

              {/* Text content */}
              <div className="inline-flex flex-col justify-center items-start">
                <div className="text-slate-800 text-sm font-normal font-['Mulish'] leading-snug">{displayTitle}</div>
                <div className="text-gray-400 text-xs font-normal font-['Mulish'] leading-tight">{displaySubtitle}</div>
              </div>
            </div>

            {/* Right side - Arrow */}
            <div className="flex justify-start items-start gap-4">
              <ChevronRight className="w-6 h-6 text-slate-800" strokeWidth={1.5} />
            </div>
          </div>
        </CardContent>
      </Card>
    </Link>
  )
}
