import { PlusIcon } from 'lucide-react'
import { getTranslations } from 'next-intl/server'
import Link from 'next/link'

import { Button } from '@/components/ui/button'
import { EmptyState } from '@/components/empty-state'
import { ErrorBoundary } from '@/components/error-boundary'
import { PaginationControls } from '@/components/pagination/pagination-controls'
import { PaginationSearchParams } from '@/components/pagination/pagination-search-params'

import { getDamages } from '../api/damages-api'
import { Damage } from '../types/damage-types'
import { DamageCard } from './damage-card'

interface Props {
  filters: PaginationSearchParams
}

interface Props {
  filters: PaginationSearchParams
}

export async function DamageList({ filters }: Props) {
  const t = await getTranslations()

  // Get pagination parameters from URL
  const { page, perPage, search, status } = filters

  // Fetch damages with all filters
  const damagesResult = await getDamages({
    page,
    perPage,
    search,
    status,
  })

  if (damagesResult?.serverError) {
    return (
      <ErrorBoundary
        error={damagesResult.serverError.message}
        title={t('DAMAGES.ERROR.TITLE', { defaultValue: 'Failed to load damages' })}
        description={t('DAMAGES.ERROR.DESCRIPTION', {
          defaultValue: 'There was an error loading your damages. Please try again.',
        })}
      />
    )
  }

  const damages = damagesResult?.data?.damages
  const pagination = damagesResult?.data?.pagination
  const totalItems = pagination?.totalItems || 0
  const totalPages = pagination?.totalPages || 0

  if (pagination?.totalItems === 0) {
    return (
      <EmptyState
        pageTitle={t('DAMAGES.LIST.TITLE')}
        actions={
          <Button variant="ghost" asChild className="text-success">
            <Link href="/damages/create">
              <PlusIcon className="w-4 h-4" />
              {t('DAMAGES.LIST.ADD_BUTTON.TEXT', { defaultValue: 'Add damage' })}
            </Link>
          </Button>
        }
        title={t('No damages added yet')}
        message={t('Create new damage')}
      />
    )
  }

  return (
    <div className="h-full flex flex-col ">
      {/* Header - Following contract-list.tsx pattern */}
      <div className="hidden md:flex justify-between items-center pb-6 flex-shrink-0 px-4 pt-4 ">
        <div />
        <h1 className="text-2xl font-semibold text-[#111827]">{t('DAMAGES.LIST.TITLE')}</h1>
        <Button asChild variant="ghost" className="text-success">
          <Link href="/damages/create">
            <PlusIcon />
            {t('DAMAGES.LIST.ADD_BUTTON.TEXT', { defaultValue: 'Add damage' })}
          </Link>
        </Button>
      </div>

      {/* Add Damage Button - Mobile */}
      <div className="md:hidden flex justify-end p-4">
        <Button asChild variant="ghost" className="text-success">
          <Link href="/damages/create">
            <PlusIcon />
            {t('DAMAGES.LIST.ADD_BUTTON.TEXT', { defaultValue: 'Add damage' })}
          </Link>
        </Button>
      </div>

      {/* Scrollable Content Area - Following responsive patterns */}
      <div className="flex-1 overflow-y-auto p-4">
        <div className="max-w-4xl grid grid-cols-1 mx-auto gap-3 md:gap-6 pb-8">
          {damages?.map((damage: Damage) => <DamageCard key={damage.id} damage={damage} />)}
        </div>
      </div>

      {/* Fixed Pagination at Bottom */}
      {totalPages > 1 && (
        <PaginationControls
          totalPages={totalPages}
          totalItems={totalItems}
          currentPage={page}
          itemsPerPage={perPage}
          className="justify-center flex-shrink-0 pt-4 border-t border-gray-200 bg-white sticky bottom-0 px-4 pb-2"
        />
      )}
    </div>
  )
}
