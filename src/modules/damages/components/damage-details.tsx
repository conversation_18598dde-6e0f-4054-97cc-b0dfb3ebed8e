import { getTranslations } from 'next-intl/server'

import {
  getDamageDisplayTitle,
  getDamageInternalNumber,
  getEntityTranslated,
  getStatusColor,
  getStatusName,
  getTypeTranslated,
} from '@/lib/damage-helpers'
import { formatDateTimeFull } from '@/lib/format-date-locale'
import { Badge } from '@/components/ui/badge'
import { ErrorBoundary } from '@/components/error-boundary'

import { getDamageById } from '../api/damages-api'

interface DamageDetailsProps {
  damageId: string
  contractId: string
}

interface DetailRowProps {
  label: string
  value: string | number | null | undefined
  fallback?: string
}

function DetailRow({ label, value, fallback = 'Not specified' }: DetailRowProps) {
  return (
    <div className="space-y-1">
      <p className="text-sm text-muted-foreground">{label}</p>
      <p className="text-sm font-medium">{value || fallback}</p>
    </div>
  )
}

export async function DamageDetails({ damageId, contractId }: DamageDetailsProps) {
  const t = await getTranslations('DAMAGES.DETAILS.DETAILS_TAB')

  const damageResult = await getDamageById({ contractId, damageId })

  if (damageResult?.serverError || !damageResult?.data) {
    return (
      <ErrorBoundary
        error={damageResult?.serverError?.message || 'Failed to load damage'}
        title="Failed to load damage"
        description="There was an error loading the damage. Please try again."
      />
    )
  }

  const damage = damageResult?.data

  // Format dates with locale awareness
  const formattedDamageDate = damage.date ? await formatDateTimeFull(new Date(damage.date)) : undefined
  const formattedProviderReportDate = damage.providerReportDate
    ? await formatDateTimeFull(new Date(damage.providerReportDate))
    : undefined
  const formattedPoliceReportDate = damage.policeReportDate
    ? await formatDateTimeFull(new Date(damage.policeReportDate))
    : undefined

  return (
    <div>
      {/* Damage Title and Status */}
      <div className="flex items-start justify-between mb-6">
        <h2 className="text-lg font-semibold">{getDamageDisplayTitle(damage)}</h2>
        <Badge variant="secondary" className={getStatusColor(damage.status)}>
          {damage.status === 'ACTIVE' ? 'Open' : getStatusName(damage.status)}
        </Badge>
      </div>

      {/* Damage Details Grid */}
      <div className="space-y-6">
        <DetailRow label={t('INTERNAL_NUMBER_FIELD.LABEL')} value={getDamageInternalNumber(damage)} />

        <DetailRow label={t('CLIENT_DAMAGE_NUMBER_FIELD.LABEL')} value={damage.clientDamageNumber} />

        <DetailRow label={t('ESTIMATED_DAMAGE_AMOUNT_FIELD.LABEL')} value={damage.estimatedAmount} />

        <DetailRow label={t('LOCATION_FIELD.LABEL')} value={damage.location} />

        <DetailRow label={t('DAMAGED_ENTITY_FIELD.LABEL')} value={getEntityTranslated(damage.entity)} />

        <DetailRow label={t('CONTRACT_FIELD.LABEL')} value={damage?.contractProductName} />

        <DetailRow label={t('DAMAGE_TIME_FIELD.LABEL')} value={formattedDamageDate} />

        <DetailRow label={t('DAMAGE_REASON_FIELD.LABEL')} value={damage.reason} />

        <DetailRow label={t('REPORTED_TO_AGENT_DATE_FIELD.LABEL')} value={formattedProviderReportDate} />

        <DetailRow label={t('REPORTED_TO_POLICE_DATE_FIELD.LABEL')} value={formattedPoliceReportDate} />

        <DetailRow label={t('POLICE_REPORT_NUMBER_FIELD.LABEL')} value={damage.externalNumberPolice} />

        <DetailRow label={t('WITNESS_FIELD.LABEL')} value={damage.witnesses} />

        <DetailRow
          label={t('EXPERT_FIELD.LABEL')}
          value={damage.expert} // This seems to be hardcoded in the design
        />

        <DetailRow label={t('DAMAGE_TYPE_FIELD.LABEL')} value={getTypeTranslated(damage.type)} />

        {damage.description && (
          <>
            <div className="space-y-2">
              <p className="text-sm text-muted-foreground">{t('DAMAGE_DESCRIPTION_FIELD.LABEL')}</p>
              <p className="text-sm leading-relaxed">{damage.description}</p>
            </div>
          </>
        )}
      </div>
    </div>
  )
}
