'use client'

import { useMemo } from 'react'

import { Contract } from '@/modules/contracts/libs/contract-types'
import { zodResolver } from '@hookform/resolvers/zod'
import { Loader2 } from 'lucide-react'
import { useTranslations } from 'next-intl'
import { useForm } from 'react-hook-form'

import { fileToDocument } from '@/lib/actions/documents/file-to-document'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { Form } from '@/components/ui/form'
import { FormCalendar, FormInput, FormSelect, FormTextarea, FormUpload } from '@/components/form-inputs'

import { useDamageForm } from '../hooks/use-damages'
import {
  damageEntityOptions,
  DamageFormInputs,
  damageReasonOptions,
  damageSchema,
  getDamageTypesByModule,
} from '../types/damage-schema'
import { Damage } from '../types/damage-types'

interface DamageFormProps {
  editForm?: boolean
  damage?: Damage
  contracts?: Contract[]
}

export function DamageForm({ editForm, damage, contracts }: DamageFormProps) {
  const t = useTranslations()
  const { executeAsync, isPending: isCreatingDamage } = useDamageForm()

  const form = useForm({
    resolver: zodResolver(damageSchema),
    defaultValues: {
      // Basic damage information - no title field like in ExtJS
      description: damage?.description || '',
      internalNumber: damage?.internalNumber || '',
      clientDamageNumber: damage?.clientDamageNumber || '',
      location: damage?.location || '',
      witnesses: damage?.witnesses || '',

      // Damage classification
      type: damage?.type || undefined,
      entity: damage?.entity || undefined,
      reason: damage?.reason || undefined,

      // Financial information
      estimatedAmount: damage?.estimatedAmount || undefined,
      currency: damage?.currency || 'EUR',

      // Dates
      date: damage?.date ? new Date(damage.date) : undefined,
      providerReportDate: damage?.providerReportDate ? new Date(damage.providerReportDate) : undefined,
      policeReportDate: damage?.policeReportDate ? new Date(damage.policeReportDate) : undefined,

      // Police report information
      externalNumberPolice: damage?.externalNumberPolice || '',

      // Required contract reference
      contractId: damage?.contractId || '',

      // New required fields from backend (will be transformed to boolean)
      rentedObject: damage?.rentedObject ? 'true' : 'false',
      isPersonInjured: damage?.isPersonInjured ? 'true' : 'false',

      // Form metadata
      editForm: editForm || false,
      damageId: damage?.id,
      files: undefined,
    },
  })

  const onSubmit = async (data: DamageFormInputs) => {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    let files: any[] = []

    if (data.files) {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      files = await Promise.all(data.files.map(async (file: any) => await fileToDocument(file)))
    }

    await executeAsync({
      ...data,
      files: files,
    })
  }

  // Watch for contract selection to update damage types
  const selectedContractId = form.watch('contractId')
  const selectedContract = contracts?.find((contract) => contract.id === selectedContractId)

  // Generate dynamic damage types based on selected contract module
  const dynamicDamageTypes = useMemo(() => {
    if (!selectedContract?.module) {
      return []
    }
    return getDamageTypesByModule(selectedContract.module).map((option) => ({
      value: option.value,
      label: t(option.label),
    }))
  }, [selectedContract?.module, t])

  // Contract options for dropdown
  const contractOptions = useMemo(() => {
    return (
      contracts?.map((contract) => ({
        value: contract.id,
        label: `${contract.product_name}`,
      })) || []
    )
  }, [contracts])

  const safeDamageEntityOptions = damageEntityOptions.map((option) => ({
    value: option.value,
    label: t(option.label),
  }))

  const safeDamageReasonOptions = damageReasonOptions.map((option) => ({
    value: option.value,
    label: t(option.label),
  }))

  // Boolean options for both rentedObject and isPersonInjured (reusable)
  const booleanOptions = [
    {
      value: 'true',
      label: t('Yes'),
    },
    {
      value: 'false',
      label: t('No'),
    },
  ]

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8 max-w-4xl mx-auto">
        <Card>
          <CardContent className="pt-6">
            <div className="grid grid-cols-1 gap-4">
              {/* Internal Number (read-only in edit mode) */}
              {editForm && (
                <FormInput<DamageFormInputs>
                  name="internalNumber"
                  label="DAMAGES.EDIT_FORM.INTERNAL_NUMBER_FIELD.LABEL"
                  disabled={true}
                />
              )}
              {/* Contract Selection - now visible dropdown */}
              <FormSelect<DamageFormInputs>
                name="contractId"
                label="DAMAGES.EDIT_FORM.CONTRACT_FIELD.LABEL"
                options={contractOptions}
                disabled={editForm}
              />

              {/* Client Damage Number */}
              <FormInput<DamageFormInputs>
                name="clientDamageNumber"
                label="DAMAGES.EDIT_FORM.CLIENT_DAMAGE_NUMBER_FIELD.LABEL"
              />

              {/* Estimated Amount (3rd position in ExtJS) */}
              <FormInput<DamageFormInputs>
                name="estimatedAmount"
                label="DAMAGES.EDIT_FORM.ESTIMATED_AMOUNT_FIELD.LABEL"
                type="number"
              />

              {/* Location */}
              <FormInput<DamageFormInputs> name="location" label="DAMAGES.EDIT_FORM.LOCATION_FIELD.LABEL" />

              {/* Entity */}
              <FormSelect<DamageFormInputs>
                name="entity"
                label="DAMAGES.EDIT_FORM.ENTITY_FIELD.LABEL"
                options={safeDamageEntityOptions}
              />

              {/* Damage Date */}
              <FormCalendar<DamageFormInputs>
                name="date"
                label="DAMAGES.EDIT_FORM.DAMAGE_DATE_FIELD.LABEL"
                includeTime
              />

              {/* Reason */}
              <FormSelect<DamageFormInputs>
                name="reason"
                label="DAMAGES.EDIT_FORM.REASON_FIELD.LABEL"
                options={safeDamageReasonOptions}
              />

              {/* Provider Report Date */}
              <FormCalendar<DamageFormInputs>
                name="providerReportDate"
                label="DAMAGES.EDIT_FORM.PROVIDER_REPORT_DATE_FIELD.LABEL"
                includeTime
              />

              {/* Police Report Date */}
              <FormCalendar<DamageFormInputs>
                name="policeReportDate"
                label="DAMAGES.EDIT_FORM.POLICE_REPORT_DATE_FIELD.LABEL"
                includeTime
              />

              {/* Police Report Number */}
              <FormInput<DamageFormInputs>
                name="externalNumberPolice"
                label="DAMAGES.EDIT_FORM.POLICE_REPORT_NUMBER_FIELD.LABEL"
              />

              {/* Dynamic Damage Type (disabled until contract is selected) */}
              <FormSelect<DamageFormInputs>
                name="type"
                label="DAMAGES.EDIT_FORM.DAMAGE_TYPE_FIELD.LABEL"
                options={dynamicDamageTypes}
                disabled={!selectedContractId || dynamicDamageTypes.length === 0}
              />

              {/* Witnesses */}
              <FormInput<DamageFormInputs> name="witnesses" label="DAMAGES.EDIT_FORM.WITNESS_FIELD.LABEL" />

              {/* New required fields from backend (boolean fields) */}
              <FormSelect<DamageFormInputs>
                name="rentedObject"
                label="DAMAGES.EDIT_FORM.RENTED_OBJECT_FIELD.LABEL"
                options={booleanOptions}
              />

              <FormSelect<DamageFormInputs>
                name="isPersonInjured"
                label="DAMAGES.EDIT_FORM.IS_PERSON_INJURED_FIELD.LABEL"
                options={booleanOptions}
              />

              {/* Description (at the end, matching ExtJS) */}
              <FormTextarea<DamageFormInputs> name="description" label="DAMAGES.EDIT_FORM.DESCRIPTION_FIELD.LABEL" />
            </div>

            {/* File Upload */}
            {!editForm && (
              <div className="mt-6">
                <FormUpload<DamageFormInputs>
                  name="files"
                  label="DAMAGES.EDIT_FORM.DOCUMENTS_FIELD.TEXT"
                  accept=".pdf,.jpg,.jpeg,.png"
                  multiple={true}
                  maxFiles={10}
                  maxSize={10}
                />
              </div>
            )}
          </CardContent>
        </Card>

        {/* Submit Button */}
        <div className="flex justify-end">
          <Button type="submit" disabled={isCreatingDamage} className="w-full md:w-auto">
            {isCreatingDamage ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                {t('DAMAGES.EDIT_FORM.SUBMITTING')}
              </>
            ) : (
              t('Save')
            )}
          </Button>
        </div>
      </form>
    </Form>
  )
}
