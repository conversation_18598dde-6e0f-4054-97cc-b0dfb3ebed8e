import { PlusIcon } from 'lucide-react'
import { getTranslations } from 'next-intl/server'

import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { Skeleton } from '@/components/ui/skeleton'

export const DamageHeaderSkeleton = () => {
  return (
    <div className="hidden md:flex justify-between items-center pb-6 flex-shrink-0 px-4 pt-4">
      <div />
      <Skeleton className="h-8 w-48" />
      <div />
    </div>
  )
}

export const DamageListSkeleton = async () => {
  const t = await getTranslations()

  return (
    <div className="h-full flex flex-col">
      {/* Header - matching DamageList structure */}
      <div className="hidden md:flex justify-between items-center pb-6 flex-shrink-0 px-4 pt-4">
        <div />
        <h1 className="text-2xl font-bold text-[#111827]">{t('DAMAGES.LIST.TITLE')}</h1>
        <Button variant="ghost" className="text-success">
          <PlusIcon />
          {t('DAMAGES.LIST.ADD_BUTTON.TEXT', { defaultValue: 'Add damage' })}
        </Button>
      </div>

      {/* Add Damage Button - Mobile */}
      <div className="md:hidden flex justify-end p-4">
        <Skeleton className="h-10 w-40 rounded-md" />
      </div>

      {/* Scrollable Content Area with skeleton cards */}
      <div className="flex-1 overflow-y-auto p-4">
        <div className="max-w-4xl grid grid-cols-1 mx-auto gap-3 md:gap-6">
          {Array.from({ length: 5 }).map((_, index) => (
            <DamageCardSkeleton key={index} />
          ))}
        </div>
      </div>
    </div>
  )
}

export const DamageCardSkeleton = () => {
  return (
    <Card className="h-14 border border-[#E5E7EB] overflow-hidden rounded-[10px] shadow-[0px_12px_38px_0px_rgba(0,66,117,0.10)]">
      <CardContent className="px-6 py-3 h-full">
        <div className="flex justify-between items-center h-full">
          {/* Left side - Icon and text skeleton */}
          <div className="flex justify-start items-center gap-2.5">
            {/* Icon skeleton */}
            <Skeleton className="w-6 h-6 flex-shrink-0" />

            {/* Text content skeleton */}
            <div className="inline-flex flex-col justify-center items-start gap-1">
              <Skeleton className="h-4 w-20" />
              <Skeleton className="h-3 w-16" />
            </div>
          </div>

          {/* Right side - Arrow skeleton */}
          <Skeleton className="w-6 h-6" />
        </div>
      </CardContent>
    </Card>
  )
}

export const DamageDetailsSkeleton = () => {
  return (
    <div className="px-4 md:px-0">
      {/* Header skeleton */}
      <div className="justify-between items-center px-6 py-8 hidden md:flex">
        <Skeleton className="w-6 h-6" />
        <Skeleton className="h-7 w-48" />
        <Skeleton className="h-6 w-20" />
      </div>

      {/* Content skeleton */}
      <div className="w-full mt-6 pb-6 max-w-4xl mx-auto shadow-2xl rounded-lg px-6 py-10">
        <div className="max-w-4xl mx-auto overflow-y-auto space-y-6">
          <Skeleton className="h-8 w-full" />
          <Skeleton className="h-4 w-3/4" />
          <Skeleton className="h-4 w-1/2" />
          <div className="space-y-3">
            {Array.from({ length: 4 }).map((_, index) => (
              <Skeleton key={index} className="h-4 w-full" />
            ))}
          </div>
        </div>
      </div>
    </div>
  )
}

export const CreateDamageLoading = () => {
  return (
    <div className="flex-1 overflow-y-auto p-4">
      <div className="max-w-4xl mx-auto">
        <Card>
          <CardContent className="pt-6">
            <div className="space-y-6">
              {Array.from({ length: 6 }).map((_, index) => (
                <div key={index} className="space-y-2">
                  <Skeleton className="h-4 w-24" />
                  <Skeleton className="h-10 w-full" />
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
