'use server'

import { getSession } from '@/modules/auth/actions/session'
import { getTranslations } from 'next-intl/server'

import { DocumentList } from '@/components/documents'
import { ErrorBoundary } from '@/components/error-boundary'

import { getDamageDocuments } from '../api/damages-api'

interface DamageDocumentsProps {
  damageId: string
}

export async function DamageDocuments({ damageId }: DamageDocumentsProps) {
  const t = await getTranslations()
  const session = await getSession()

  if (!session) {
    return <ErrorBoundary error="Authentication required" />
  }

  const documentsResponse = await getDamageDocuments(damageId)

  // Handle server error
  if (documentsResponse?.serverError) {
    return <ErrorBoundary error={documentsResponse.serverError.message} />
  }

  // Extract documents array from response
  const responseData = documentsResponse?.data
  const documents = Array.isArray(responseData) ? responseData : responseData || []

  // Custom empty state configuration for contracts
  const emptyStateConfig = {
    title: t('DAMAGES.DETAILS.DOCUMENTS_TAB.EMPTY_TEXT'),
  }

  return <DocumentList documents={documents} accessToken={session.accessToken} emptyStateConfig={emptyStateConfig} />
}
