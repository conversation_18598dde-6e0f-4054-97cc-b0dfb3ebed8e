import { getAllContracts } from '@/modules/contracts/api/contracts-api'

import { ErrorBoundary } from '@/components/error-boundary'

import { getDamageById } from '../api/damages-api'
import { Damage } from '../types/damage-types'
import { DamageForm } from './damage-form'

interface DamageFormServerProps {
  editForm?: boolean
  id?: string
  contractId?: string
}

export async function DamageFormServer({ editForm, id, contractId }: DamageFormServerProps) {
  let damage: Damage | undefined

  // Get all contracts for the dropdown
  const contractsResponse = await getAllContracts()

  if (contractsResponse?.serverError || !contractsResponse?.data) {
    return (
      <ErrorBoundary
        error={contractsResponse?.serverError.message || 'Unknown error'}
        title="Failed to load contracts"
        description="There was an error loading the contracts. Please try again."
      />
    )
  }

  // If editing, fetch the existing damage
  if (editForm && id && contractId) {
    const damageResult = await getDamageById({ contractId, damageId: id })

    if (damageResult?.serverError) {
      return (
        <ErrorBoundary
          error={damageResult.serverError.message}
          title="Failed to load damage"
          description="There was an error loading the damage. Please try again."
        />
      )
    }

    damage = damageResult?.data
  }

  return (
    <div className="flex-1 overflow-y-auto p-4">
      <DamageForm editForm={editForm} damage={damage} contracts={contractsResponse.data} />
    </div>
  )
}
