import qs from 'qs'

import { createDetailedError } from '@/lib/fetch-api'
import { fetchAuthenticatedApi } from '@/lib/fetch-with-auth-api'
import { BaseDocument } from '@/components/documents'
import { PaginationSearchParams } from '@/components/pagination/pagination-search-params'

import { Damage } from '../types/damage-types'
import { DAMAGE_TAGS } from './damages-tags'

export const getDamages = async ({
  page,
  perPage,
  search,
  status,
  contractId,
}: PaginationSearchParams & { contractId?: string }) => {
  // Calculate start parameter for API pagination
  const start = (page - 1) * perPage

  // Build query parameters using qs library for better URL handling
  const queryParams: Record<string, string | number> = {
    page: page,
    start: start,
    limit: perPage,
  }

  // Add optional filters
  if (search && search.trim()) {
    queryParams.search = search.trim()
  }
  if (status && status.trim()) {
    queryParams.status = status.trim()
  }
  if (contractId && contractId.trim()) {
    queryParams.contractId = contractId.trim()
  }

  // Generate clean URL using qs library
  const queryString = qs.stringify(queryParams, {
    addQueryPrefix: true,
    encode: false, // Don't encode since these are safe parameters
  })

  const response = await fetchAuthenticatedApi<{ data: Damage[]; total_items: number }>(`/damages${queryString}`, {
    next: {
      revalidate: 60,
      tags: [DAMAGE_TAGS.all],
    },
  })

  if (response.error) {
    const error = createDetailedError(response.error, response.details)
    return {
      serverError: error,
    }
  }

  const damages = response.data?.data || []
  const totalItems = response.data?.total_items || 0
  const totalPages = Math.ceil(totalItems / perPage)

  return {
    data: {
      damages,
      pagination: {
        page,
        perPage,
        totalItems,
        totalPages,
      },
    },
  }
}

export const getDamageById = async ({ contractId, damageId }: { damageId: string; contractId: string }) => {
  const response = await fetchAuthenticatedApi<Damage>(`/contracts/${contractId}/damages/${damageId}`, {
    next: {
      revalidate: 60,
      tags: [DAMAGE_TAGS.byId(damageId)],
    },
  })

  if (response.error) {
    const error = createDetailedError(response.error, response.details)
    return {
      serverError: error,
    }
  }

  return { data: response.data }
}

export const getDamageDocuments = async (damageId: string) => {
  const response = await fetchAuthenticatedApi<{ data: BaseDocument[] }>(`/documents?params="DAMAGE",${damageId}`, {
    next: {
      revalidate: 60,
      tags: [DAMAGE_TAGS.documents(damageId)],
    },
  })

  if (response.error) {
    const error = createDetailedError(response.error, response.details)
    return {
      serverError: error,
    }
  }

  return { data: response.data?.data || [] }
}
