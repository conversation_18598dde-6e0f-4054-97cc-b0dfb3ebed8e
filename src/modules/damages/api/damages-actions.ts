'use server'

import { revalidateTag } from 'next/cache'

import { fetchApiOrThrow } from '@/lib/fetch-api'
import { authenticatedAction } from '@/lib/safe-actions'

import { createDamageSchema, updateDamageSchema } from '../types/damage-schema'
import { Damage } from '../types/damage-types'
import { DAMAGE_TAGS } from './damages-tags'

export const createDamage = authenticatedAction
  .schema(createDamageSchema)
  .action(async ({ parsedInput, ctx: { session } }) => {
    // Format dates to match API expectations (YYYY-MM-DD HH:mm:ss format)
    const formatDateForApi = (date: Date | undefined) => {
      if (!date) return undefined
      return date.toISOString().replace('T', ' ').substring(0, 19)
    }

    // Format data for API (matching the cURL request structure)
    const apiData = {
      // System fields
      activeModule: 'contracts',
      language: null,
      mode: 'create',

      // Basic damage information
      title: parsedInput.title,
      description: parsedInput.description || '',
      internalNumber: parsedInput.internalNumber || '',
      clientDamageNumber: parsedInput.clientDamageNumber || '',
      location: parsedInput.location || '',
      witnesses: parsedInput.witnesses || '',

      // Damage classification
      type: parsedInput.type,
      entity: parsedInput.entity,
      reason: parsedInput.reason,

      // Financial information
      estimatedAmount: parsedInput.estimatedAmount,
      _estimatedAmount: parsedInput.estimatedAmount, // API expects both fields
      currency: parsedInput.currency || 'EUR',

      // Dates (formatted as YYYY-MM-DD HH:mm:ss)
      date: formatDateForApi(parsedInput.date),
      providerReportDate: formatDateForApi(parsedInput.providerReportDate),
      policeReportDate: formatDateForApi(parsedInput.policeReportDate),

      // Police report information
      externalNumberPolice: parsedInput.externalNumberPolice || '',

      // Contract reference
      contractId: parsedInput.contractId,

      // New required fields from backend validation
      rentedObject: parsedInput.rentedObject,
      isPersonInjured: parsedInput.isPersonInjured,
    }

    const response = await fetchApiOrThrow<Damage>(
      `/contracts/${parsedInput.contractId}/damages`,
      {
        method: 'POST',
        body: JSON.stringify(apiData),
      },
      session
    )

    // Handle file uploads if present
    if (parsedInput.files && parsedInput.files.length > 0) {
      const mappedFiles = parsedInput.files.map((file) => ({
        ...file,
        type: 'PICTURE',
        owner_type: 'DAMAGE',
        owner_id: response.id || '',
      }))

      for (const file of mappedFiles) {
        await fetchApiOrThrow(
          `/documents_base64`,
          {
            method: 'POST',
            body: JSON.stringify(file),
          },
          session
        )
      }
    }

    revalidateTag(DAMAGE_TAGS.all)
    revalidateTag(DAMAGE_TAGS.byId(response.id))

    return response
  })

export const updateDamage = authenticatedAction
  .schema(updateDamageSchema)
  .action(async ({ parsedInput, ctx: { session } }) => {
    // Format dates to match API expectations (YYYY-MM-DD HH:mm:ss format)
    const formatDateForApi = (date: Date | undefined) => {
      if (!date) return undefined
      return date.toISOString().replace('T', ' ').substring(0, 19)
    }

    // Format data for API (matching the cURL request structx3ure)
    const apiData = {
      // System fields
      activeModule: 'contracts',
      language: null,
      mode: 'edit',

      // Basic damage information
      title: parsedInput.title,
      description: parsedInput.description || '',
      internalNumber: parsedInput.internalNumber || '',
      clientDamageNumber: parsedInput.clientDamageNumber || '',
      location: parsedInput.location || '',
      witnesses: parsedInput.witnesses || '',

      // Damage classification
      type: parsedInput.type,
      entity: parsedInput.entity,
      reason: parsedInput.reason,

      // Financial information
      estimatedAmount: parsedInput.estimatedAmount,
      _estimatedAmount: parsedInput.estimatedAmount, // API expects both fields
      actualAmount: parsedInput.actualAmount,
      currency: parsedInput.currency || 'EUR',

      // Dates (formatted as YYYY-MM-DD HH:mm:ss)
      date: formatDateForApi(parsedInput.date),
      providerReportDate: formatDateForApi(parsedInput.providerReportDate),
      policeReportDate: formatDateForApi(parsedInput.policeReportDate),

      // Police report information
      externalNumberPolice: parsedInput.externalNumberPolice || '',

      // Contract reference and status
      contractId: parsedInput.contractId,
      status: parsedInput.status,

      // New required fields from backend validation
      rentedObject: parsedInput.rentedObject,
      isPersonInjured: parsedInput.isPersonInjured,
    }

    const response = await fetchApiOrThrow<Damage>(
      `/contracts/${parsedInput.contractId}/damages/${parsedInput.damageId}`,
      {
        method: 'PUT',
        body: JSON.stringify(apiData),
      },
      session
    )

    // Handle file uploads if present
    if (parsedInput.files && parsedInput.files.length > 0) {
      const mappedFiles = parsedInput.files.map((file) => ({
        ...file,
        type: 'PICTURE',
        owner_type: 'DAMAGE',
        owner_id: parsedInput.damageId,
      }))

      for (const file of mappedFiles) {
        await fetchApiOrThrow(
          `/documents_base64`,
          {
            method: 'POST',
            body: JSON.stringify(file),
          },
          session
        )
      }
    }

    revalidateTag(DAMAGE_TAGS.all)
    revalidateTag(DAMAGE_TAGS.byId(parsedInput.damageId))

    return response
  })
