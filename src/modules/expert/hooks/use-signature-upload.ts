import { useState } from 'react'

import { revalidateProfile } from '@/modules/profile/api/profile-actions'
import { useSession } from '@/providers/session-provider'
import { useTranslations } from 'next-intl'
import { toast } from 'sonner'

interface SignatureUploadParams {
  signature: string
  userId: string
  template: string
}

export const useSignatureUpload = (clientId: string, uploadUrl: string) => {
  const [isLoading, setIsLoading] = useState(false)
  const { session, isLoading: isLoadingSession } = useSession()
  const t = useTranslations()

  const uploadSignature = async ({ signature, userId, template }: SignatureUploadParams) => {
    setIsLoading(true)
    try {
      const formData = new FormData()
      const templateBlob = new Blob([template], { type: 'text/html' })
      const basePlain = signature.split(',')[1]

      formData.append('ownerId', userId)
      formData.append('ownerType', 'CLIENT')
      formData.append('type', 'AUTHORISATION')
      formData.append('document[templateBlob]', templateBlob, 'template.html')
      formData.append('document[name]', 'Maklervollmacht')
      formData.append('document[description]', '')
      formData.append('document[signature]', basePlain)

      const response = await fetch(uploadUrl, {
        method: 'POST',
        body: formData,
        headers: {
          Authorization: `Bearer ${session?.accessToken}`,
        },
      })
      if (!response.ok) {
        throw new Error()
      }
      toast.info(t('EXPERT.SIGNATURE_SENT', { defaultValue: 'Signature sent successfully!' }))
      return revalidateProfile()
    } catch {
      toast.error(t('ERROR.SIGNING.ACCEPT'))
    } finally {
      setIsLoading(false)
    }
  }

  return {
    uploadSignature,
    isLoading: isLoading || isLoadingSession,
  }
}
