import { z } from 'zod'

export const expertSchema = z.object({
  firstName: z.string().optional(),
  lastName: z.string().optional(),
  agencyImgId: z.string().optional(),
  imgId: z.string().optional(),
  agencyStreet: z.string().optional(),
  agencyStreetNumber: z.string().optional(),
  agencyZip: z.string().optional(),
  agencyCity: z.string().optional(),
  agencyName: z.string().optional(),
  phone: z.string().optional(),
  mobile: z.string().optional(),
  fax: z.string().optional(),
  email: z.string().email().optional(),
  acceptance_request: z.string().optional(),
  agencyLegalForm: z.string().optional(),
  ihkNumber: z.string().optional(),
})

export type ExpertSchema = z.infer<typeof expertSchema> 