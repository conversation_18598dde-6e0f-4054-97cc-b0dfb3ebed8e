'use client'

import { UserProfile } from '@/modules/auth/types/auth-types'
import { useSession } from '@/providers/session-provider'
import { useTranslations } from 'next-intl'

import { Card } from '@/components/ui/card'
import { DocumentPreviewModal } from '@/components/documents'
import { DownloadFileButton } from '@/components/download-file-button'
import { POAAcceptanceModal } from '@/components/expert/poa-acceptance-modal'

export interface AcceptanceDocument {
  sourceType: string
  extension: string
  documentUrl: string
  id: string
  modified: string
  name: string
}

interface AcceptanceSectionProps {
  profileResponse?: UserProfile
  template?: string
  apiUrl: string
  acceptanceStatus: string | undefined
  acceptanceId: string | null | undefined
  acceptanceTime: string | null | undefined
  acceptanceDocument?: AcceptanceDocument | null
}

export function AcceptanceSection({
  profileResponse,
  template,
  apiUrl,
  acceptanceStatus,
  acceptanceId,
  acceptanceTime,
  acceptanceDocument,
}: AcceptanceSectionProps) {
  const { session } = useSession()

  const t = useTranslations()

  if (!profileResponse?.id || !session?.accessToken) {
    return null
  }

  return (
    <div className="flex justify-end">
      {acceptanceStatus === 'REQUESTED' && (
        <POAAcceptanceModal template={template} apiUrl={apiUrl} profile={profileResponse} />
      )}
      {acceptanceStatus === 'AUTHORIZED' && acceptanceId && (
        <Card className={'p-4'}>
          <div>{t('EXPERT.ACCEPTANCE_AGREEMENT.STATUS')}</div>
          <div className={'flex flex-row  items-center gap-2'}>
            {t('EXPERT.AGREEMENT_SINGED') + ': ' + acceptanceTime}
            <div className={'self-end flex items-center '}>
              <DownloadFileButton documentId={acceptanceId?.toString()} accessToken={session?.accessToken} />
              {acceptanceDocument && <DocumentPreviewModal document={acceptanceDocument} />}
            </div>
          </div>
        </Card>
      )}
    </div>
  )
}
