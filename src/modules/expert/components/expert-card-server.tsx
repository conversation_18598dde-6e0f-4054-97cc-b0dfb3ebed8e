import { <PERSON>pert } from '@/modules/expert/types/expert-types'

import { ErrorBoundary } from '@/components/error-boundary'

import { getExpert } from '../actions/expert-api'
import { ExpertCard } from './expert-card'

export async function ExpertCardServer() {
  const getExpertResponse = await getExpert()

  if (getExpertResponse?.serverError) {
    return (
      <ErrorBoundary
        error={getExpertResponse.serverError.message}
        title="Failed to load expert profile"
        description="There was an error loading the expert profile. Please try again."
      />
    )
  }

  const expertData = getExpertResponse?.data as Expert

  return (
    <div className="flex-1 overflow-y-auto p-4">
      <ExpertCard initialData={expertData} />
    </div>
  )
}
