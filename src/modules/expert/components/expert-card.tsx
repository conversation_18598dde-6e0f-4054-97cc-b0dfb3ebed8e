import { getSession } from '@/modules/auth/actions/session'
import { Expert } from '@/modules/expert/types/expert-types'
import { getUserProfile } from '@/modules/profile/api/profile-api'
import { getTranslations } from 'next-intl/server'

import { createApiUrl } from '@/lib/fetch-api'
import { formatDateFull } from '@/lib/format-date-locale'
import { getProcessedAgreementTemplate } from '@/lib/replace-acceptance-request'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Card, CardContent } from '@/components/ui/card'

import { AcceptanceSection, type AcceptanceDocument } from './acceptance-section'

interface ExpertFormProps {
  initialData?: Expert
}

interface ExpertDetailProp {
  label: string
  value: string | number | null | undefined
  fallback?: string
}

function DetailRow({ label, value, fallback = 'Not specified' }: ExpertDetailProp) {
  return (
    <div className="space-y-1">
      <p className="text-sm text-muted-foreground">{label}</p>
      <p className="text-sm font-medium">{value || fallback}</p>
    </div>
  )
}

export async function ExpertCard({ initialData }: ExpertFormProps) {
  const t = await getTranslations()
  const profileResponse = await getUserProfile()
  const session = await getSession()

  const expertInitials = initialData
    ? `${initialData.firstName?.charAt(0) || ''}${initialData.lastName?.charAt(0) || ''}`.toUpperCase() || 'AM'
    : 'AM'

  const acceptanceStatus = profileResponse?.data?.acceptanceStatus
  const acceptanceId = profileResponse?.data?.acceptanceId
  const apiUrl = await createApiUrl('/documents/authorisation', true)

  const acceptanceTime =
    profileResponse?.data?.acceptanceTime && (await formatDateFull(profileResponse.data?.acceptanceTime))
  const acceptanceDocumentUrl = profileResponse?.data?.acceptanceDocumentUrl

  const template = await getProcessedAgreementTemplate({
    agent: initialData,
    userProfile: profileResponse?.data,
    accessToken: session?.accessToken,
  })

  const acceptanceDocument = !!acceptanceId
    ? ({
        sourceType: 'remoteDocument',
        extension: 'pdf',
        documentUrl: acceptanceDocumentUrl || '',
        id: acceptanceId || '',
        modified: '',
        name: '',
      } as AcceptanceDocument)
    : null

  return (
    <div className="max-w-4xl mx-auto space-y-8">
      <div className="space-y-8">
        <Card>
          <CardContent className="pt-6">
            <div className="flex flex-col items-center space-y-4">
              <div className="relative">
                <div className="relative w-32 h-32 sm:w-40 sm:h-40 lg:w-48 lg:h-48">
                  <div className="w-full h-full rounded-full overflow-hidden bg-gray-100 ring-4 ring-white shadow-lg">
                    <Avatar className="w-full h-full">
                      <AvatarImage
                        src={initialData?.imgId || ''}
                        alt={`${initialData?.firstName || 'Expert'} ${initialData?.lastName || 'Profile'}`}
                        className="object-cover object-center"
                      />
                      <AvatarFallback className="text-2xl sm:text-3xl lg:text-4xl font-semibold bg-gradient-to-br from-blue-500 to-purple-600 text-white">
                        {expertInitials}
                      </AvatarFallback>
                    </Avatar>
                  </div>
                </div>
              </div>
            </div>

            <div className="grid grid-cols-1 gap-6 mt-8">
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-gray-900">{t('EXPERT.DETAILS.AGENT_DATA')}</h3>
                <div className="space-y-4">
                  <DetailRow label={t('EXPERT.DETAILS.FIRST_NAME_FIELD.LABEL')} value={initialData?.firstName} />
                  <DetailRow label={t('EXPERT.DETAILS.LAST_NAME_FIELD.LABEL')} value={initialData?.lastName} />
                  <DetailRow label={t('EXPERT.DETAILS.AGENCY_NAME_FIELD.LABEL')} value={initialData?.agencyName} />
                  <DetailRow
                    label={t('EXPERT.DETAILS.AGENCY_LEGAL_FORM_FIELD.LABEL')}
                    value={initialData?.agencyLegalForm}
                  />
                  <DetailRow label={t('EXPERT.DETAILS.IHK_NUMBER_FIELD.LABEL')} value={initialData?.ihkNumber} />
                </div>
              </div>
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-gray-900">{t('EXPERT.DETAILS.ADDRESS_FIELD.LABEL')}</h3>
                <div className="space-y-4">
                  <DetailRow
                    label={t('EXPERT.DETAILS.ADDRESS_FIELD.LABEL')}
                    value={`${initialData?.agencyStreet} ${initialData?.agencyStreetNumber}, ${initialData?.agencyZip} ${initialData?.agencyCity}`}
                  />
                </div>
              </div>
              <h3 className="text-lg font-semibold text-gray-900">{t('EXPERT.FORM.COMMUNICATION_INFO')}</h3>
              <div className="space-y-4">
                <div className="space-y-4">
                  <DetailRow label={t('EXPERT.DETAILS.PHONE_FIELD.LABEL')} value={initialData?.phone} />
                </div>
              </div>
              <div className="space-y-4">
                <div className="space-y-4">
                  <DetailRow label={t('EXPERT.DETAILS.MOBILE_PHONE_FIELD.LABEL')} value={initialData?.mobile} />
                </div>
              </div>
              <div className="space-y-4">
                <div className="space-y-4">
                  <DetailRow label={t('EXPERT.DETAILS.EMAIL_FIELD.LABEL')} value={initialData?.email} />
                </div>
              </div>
              <div className="space-y-4">
                <div className="space-y-4">
                  <DetailRow label="EXPERT.FORM.FAX" value={initialData?.fax} />
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
        <AcceptanceSection
          profileResponse={profileResponse?.data}
          template={template}
          apiUrl={apiUrl}
          acceptanceStatus={acceptanceStatus}
          acceptanceId={acceptanceId}
          acceptanceTime={acceptanceTime}
          acceptanceDocument={acceptanceDocument}
        />
      </div>
    </div>
  )
}
