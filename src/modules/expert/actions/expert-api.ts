import { getSession } from '@/modules/auth/actions/session'

import { getImageURL, IMAGE_SIZES } from '@/lib/actions/documents/get-image'
import { createDetailedError } from '@/lib/fetch-api'
import { fetchAuthenticatedApi } from '@/lib/fetch-with-auth-api'

import { Expert } from '../types/expert-types'
import { AGENT_TAGS } from './expert-tags'

export const getExpert = async () => {
  const session = await getSession()
  const agentId = session?.user.agentId
  const response = await fetchAuthenticatedApi<Expert>(`/agents/${agentId}`, {
    next: {
      tags: [AGENT_TAGS.AGENT],
    },
  })

  if (response.error) {
    const error = createDetailedError(response.error, response.details)
    return {
      serverError: error,
    }
  }
  let avatar = ''
  if (response?.data?.imgId && session?.accessToken) {
    avatar = getImageURL({
      documentId: response.data.imgId,
      accessToken: session.accessToken,
      dimensions: IMAGE_SIZES.THUMBNAIL,
    })
  }

  return {
    data: {
      ...response.data,
      imgId: avatar,
    },
  }
}
