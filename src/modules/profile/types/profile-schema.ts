import { GenderEnum } from '@/modules/auth/types/auth-schema'
import { z } from 'zod'

export const FamilyStatusEnum = z.enum([
  'SINGLE',
  'MARRIED',
  'DIVORCED',
  'WIDOWED',
  'SEPARATED',
  'PARTNERSHIP',
  'COMMUNITY',
  'UNKNOWN',
] as const)

export type FamilyStatusType = z.infer<typeof FamilyStatusEnum>

export const familyStatusOptions: Array<{ value: FamilyStatusType; label: string }> = [
  { value: 'SINGLE', label: 'PROFILE.EDIT_FORM.MY_DATA_FIELDSET.MARTIAL_STATUS_FIELD.OPTION.SINGLE' },
  { value: 'MARRIED', label: 'PROFILE.EDIT_FORM.MY_DATA_FIELDSET.MARTIAL_STATUS_FIELD.OPTION.MARRIED' },
  { value: 'DIVORCED', label: 'PROFILE.EDIT_FORM.MY_DATA_FIELDSET.MARTIAL_STATUS_FIELD.OPTION.DIVORCED' },
  { value: 'WIDOWED', label: 'PROFILE.EDIT_FORM.MY_DATA_FIELDSET.MARTIAL_STATUS_FIELD.OPTION.WIDOWED' },
  { value: 'SEPARATED', label: 'PROFILE.EDIT_FORM.MY_DATA_FIELDSET.MARTIAL_STATUS_FIELD.OPTION.SEPARATED' },
  { value: 'PARTNERSHIP', label: 'PROFILE.EDIT_FORM.MY_DATA_FIELDSET.MARTIAL_STATUS_FIELD.OPTION.PARTNERSHIP' },
  { value: 'COMMUNITY', label: 'PROFILE.EDIT_FORM.MY_DATA_FIELDSET.MARTIAL_STATUS_FIELD.OPTION.COMMUNITY' },
  { value: 'UNKNOWN', label: 'PROFILE.EDIT_FORM.MY_DATA_FIELDSET.MARTIAL_STATUS_FIELD.OPTION.UNKNOWN' },
]

// ============================================================================
// MODULAR SCHEMA COMPONENTS
// ============================================================================

/**
 * Client Information Schema
 * Contains personal identification and basic demographic information
 */
export const clientInformationSchema = z.object({
  salutation: GenderEnum.nullable(),
  title: z.string().optional(),
  firstName: z.string().min(1, 'PROFILE.VALIDATION.FIRST_NAME_REQUIRED'),
  lastName: z.string().min(1, 'PROFILE.VALIDATION.LAST_NAME_REQUIRED'),
  familyStatus: FamilyStatusEnum.nullable(),
  birthName: z.string().optional(),
  birthdate: z.date().optional(),
  birthPlace: z.string().optional(),
  birthCountry: z.string().optional(),
  nationality: z.string().optional(),
})

export type ClientInformationInputs = z.infer<typeof clientInformationSchema>

/**
 * Legitimation Schema
 * Contains official identification document information
 */
export const legitimationSchema = z.object({
  idCardNumber: z.string().optional(),
  idCardDate: z.date().optional(),
  idCardAuthority: z.string().optional(),
})

export type LegitimationInputs = z.infer<typeof legitimationSchema>

/**
 * Default Address Schema
 * Contains primary residential address information
 */
export const defaultAddressSchema = z.object({
  street: z.string().min(1, 'PROFILE.VALIDATION.STREET_REQUIRED'),
  streetNum: z.string().min(1, 'PROFILE.VALIDATION.STREET_NUM_REQUIRED'),
  zip: z.string().min(1, 'PROFILE.VALIDATION.ZIP_REQUIRED'),
  city: z.string().min(1, 'PROFILE.VALIDATION.CITY_REQUIRED'),
  country: z.string().min(1, 'PROFILE.VALIDATION.COUNTRY_REQUIRED'),
  district: z.string().optional(),
})

export type DefaultAddressInputs = z.infer<typeof defaultAddressSchema>

/**
 * Contact Data Schema
 * Contains communication preferences and contact methods
 */
export const contactDataSchema = z.object({
  email: z.string().email('not Valid').optional(),
  preferredContactType: z.string().optional(),
  preferredAppeal: z.string().optional(),
  phone: z.string(),
  mobile: z.string().min(1, 'not Valid'),
  businessPhone: z.string().optional(),
})

export type ContactDataInputs = z.infer<typeof contactDataSchema>

// ============================================================================
// COMBINED SCHEMAS
// ============================================================================

/**
 * Complete Profile Schema
 * Combines all modular schemas for backward compatibility and complete profile forms
 */
export const profileSchema = z.object({
  // Client Information
  ...clientInformationSchema.shape,

  // Legitimation (optional for profile, but available)
  ...legitimationSchema.shape,

  // Default Address
  ...defaultAddressSchema.shape,

  // Contact Data
  ...contactDataSchema.shape,

  // Additional profile-specific fields
  profileImage: z.array(z.any()).optional(),
})

export type ProfileFormInputs = z.infer<typeof profileSchema>

/**
 * Partial Profile Schema
 * For forms that only need specific sections
 */
export const partialProfileSchema = z.object({
  clientInformation: clientInformationSchema.optional(),
  legitimation: legitimationSchema.optional(),
  defaultAddress: defaultAddressSchema.optional(),
  contactData: contactDataSchema.optional(),
})

export type PartialProfileInputs = z.infer<typeof partialProfileSchema>
