'use client'

import { useState } from 'react'

import { genderOptions } from '@/modules/auth/types/auth-schema'
import { UserProfile } from '@/modules/auth/types/auth-types'
import { zodResolver } from '@hookform/resolvers/zod'
import { Camera, Loader2 } from 'lucide-react'
import { useTranslations } from 'next-intl'
import Image from 'next/image'
import { useForm } from 'react-hook-form'

import { fileToDocument } from '@/lib/actions/documents/file-to-document'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { Form } from '@/components/ui/form'
import { FormAddressInput, FormCalendar, FormInput, FormPhoneInput, FormSelect } from '@/components/form-inputs'
import { FormCountrySelect } from '@/components/form-inputs/form-country-select'

import { useProfileForm, useUpdateProfileImage } from '../hooks/use-profile'
import { familyStatusOptions, profileSchema, type ProfileFormInputs } from '../types/profile-schema'

interface ProfileFormProps {
  initialData?: UserProfile | null
}

export function ProfileForm({ initialData }: ProfileFormProps) {
  const t = useTranslations()
  const [previewImage, setPreviewImage] = useState<string | null>(null)

  const { executeAsync: updateUserProfile, isPending: isSubmitting } = useProfileForm()
  const { executeAsync: updateProfileImage, isPending: isImageSubmitting } = useUpdateProfileImage()

  const form = useForm<ProfileFormInputs>({
    resolver: zodResolver(profileSchema),
    defaultValues: {
      salutation: initialData?.salutation || undefined,
      firstName: initialData?.firstName || '',
      lastName: initialData?.lastName || '',
      birthdate: initialData?.birthdate ? new Date(initialData.birthdate) : undefined,
      nationality: initialData?.nationality || '',
      familyStatus: initialData?.familyStatus || undefined,
      email: initialData?.email || '',

      phone: initialData?.phone || '',
      mobile: initialData?.mobile || '',
      street: initialData?.street || '',
      streetNum: initialData?.streetNum || '',
      zip: initialData?.zip || '',
      city: initialData?.city || '',
      country: initialData?.country || '',
    },
  })

  const handleImageChange = async (files: FileList | null) => {
    if (files && files[0]) {
      const file = files[0]

      // Validate file size (max 5MB)
      const maxSize = 5 * 1024 * 1024 // 5MB in bytes
      if (file.size > maxSize) {
        // You might want to show a toast notification here
        console.error('File size too large. Maximum size is 5MB.')
        return
      }

      // Validate file type
      const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp']
      if (!allowedTypes.includes(file.type)) {
        console.error('Invalid file type. Please upload a JPEG, PNG, or WebP image.')
        return
      }

      try {
        // Create preview URL immediately for better UX
        const previewUrl = URL.createObjectURL(file)
        setPreviewImage(previewUrl)

        const document = await fileToDocument(file)

        // Update with the actual uploaded image URL
        setPreviewImage(document.image)
        await updateProfileImage({
          ...document,
          owner_type: 'CLIENT',
          type: 'AVATAR',
        })

        // Clean up the preview URL
        URL.revokeObjectURL(previewUrl)
      } catch (error) {
        console.error('Error uploading image:', error)
        // Reset preview on error
        setPreviewImage(null)
        // You might want to show an error toast here
      }
    }
  }

  const onSubmit = async (data: ProfileFormInputs) => {
    await updateUserProfile(data)
  }

  const selectedCountry = form.watch('country')

  return (
    <div className="max-w-4xl mx-auto space-y-8">
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
          <Card>
            <CardContent className="pt-6">
              {/* Profile Image Section */}
              <div className="flex flex-col items-center space-y-4">
                <div className="relative group">
                  {/* Main Profile Image Container */}
                  <div className="relative w-32 h-32 sm:w-40 sm:h-40 lg:w-48 lg:h-48">
                    {/* Circular Avatar Container */}
                    <div className="w-full h-full rounded-full overflow-hidden bg-gray-100 ring-4 ring-white shadow-lg">
                      <div className="w-full h-full relative">
                        <Image
                          src={previewImage || initialData?.imgId || ''}
                          alt={`${initialData?.firstName || 'User'} ${initialData?.lastName || 'Profile'}`}
                          className="object-cover object-center"
                          fill
                        />
                      </div>
                    </div>

                    {/* Upload Button Overlay */}
                    <label
                      htmlFor="profile-image"
                      className="absolute bottom-2 right-2 bg-white rounded-full p-2.5 shadow-lg cursor-pointer hover:bg-gray-50 border-2 border-gray-200 transition-all duration-200 hover:scale-105 group-hover:shadow-xl"
                      title={t('PROFILE.EDIT_FORM.UPLOAD_IMAGE', { defaultValue: 'Upload profile image' })}
                    >
                      {isImageSubmitting ? (
                        <Loader2 className="h-4 w-4 sm:h-5 sm:w-5 animate-spin text-gray-600" />
                      ) : (
                        <Camera className="h-4 w-4 sm:h-5 sm:w-5 text-gray-600" />
                      )}

                      <input
                        id="profile-image"
                        type="file"
                        accept="image/jpeg,image/jpg,image/png,image/webp"
                        className="hidden"
                        disabled={isImageSubmitting}
                        onChange={(e) => handleImageChange(e.target.files)}
                        aria-label={t('PROFILE.EDIT_FORM.UPLOAD_IMAGE', { defaultValue: 'Upload profile image' })}
                      />
                    </label>
                  </div>
                </div>
              </div>
              <div className="grid grid-cols-1 gap-6">
                {/* Personal Information */}
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold text-gray-900">
                    {t('PROFILE.EDIT_FORM.MY_DATA_FIELDSET.TITLE')}
                  </h3>
                  <div className="space-y-4">
                    <FormSelect<ProfileFormInputs>
                      name="salutation"
                      label="PROFILE.EDIT_FORM.MY_DATA_FIELDSET.SALUTATION_FIELD.LABEL"
                      options={genderOptions}
                      isRequired
                    />

                    <FormInput<ProfileFormInputs>
                      name="firstName"
                      label="PROFILE.EDIT_FORM.MY_DATA_FIELDSET.FIRSTNAME_FIELD.LABEL"
                      isRequired
                    />

                    <FormInput<ProfileFormInputs>
                      name="lastName"
                      label="PROFILE.EDIT_FORM.MY_DATA_FIELDSET.LASTNAME_FIELD.LABEL"
                      isRequired
                    />

                    <FormCalendar<ProfileFormInputs>
                      name="birthdate"
                      label="PROFILE.EDIT_FORM.MY_DATA_FIELDSET.BIRTHDAY_FIELD.LABEL"
                    />

                    <FormSelect<ProfileFormInputs>
                      name="familyStatus"
                      label="PROFILE.EDIT_FORM.MY_DATA_FIELDSET.MARTIAL_STATUS_FIELD.LABEL"
                      options={familyStatusOptions}
                      isRequired
                    />

                    <FormCountrySelect<ProfileFormInputs>
                      name="nationality"
                      isNationality
                      label="PROFILE.EDIT_FORM.MY_DATA_FIELDSET.NATIONALITY_FIELD.LABEL"
                    />
                  </div>
                </div>

                {/* Contact Information */}
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold text-gray-900">
                    {t('PROFILE.EDIT_FORM.CONTACT_FIELDSET.TITLE')}
                  </h3>
                  <div className="space-y-4">
                    <FormInput<ProfileFormInputs>
                      name="street"
                      label="PROFILE.EDIT_FORM.CONTACT_FIELDSET.STREET_FIELD.LABEL"
                      isRequired
                    />

                    <FormInput<ProfileFormInputs>
                      name="streetNum"
                      label="PROFILE.EDIT_FORM.CONTACT_FIELDSET.HOUSE_NUMBER_FIELD.LABEL"
                      isRequired
                    />

                    <FormCountrySelect<ProfileFormInputs>
                      name="country"
                      label="PROFILE.EDIT_FORM.CONTACT_FIELDSET.COUNTRY_FIELD.LABEL"
                      isRequired
                    />

                    <FormAddressInput<ProfileFormInputs>
                      zipFieldName="zip"
                      cityFieldName="city"
                      selectedCountry={selectedCountry}
                      zipLabel="PROFILE.EDIT_FORM.CONTACT_FIELDSET.ZIP_FIELD.LABEL"
                      cityLabel="PROFILE.EDIT_FORM.CONTACT_FIELDSET.CITY_FIELD.LABEL"
                      isRequired
                    />
                  </div>
                </div>

                {/* Address Information */}
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold text-gray-900">
                    {t('PROFILE.EDIT_FORM.ADDRESS_FIELDSET.TITLE')}
                  </h3>
                  <div className="space-y-4">
                    <FormPhoneInput<ProfileFormInputs>
                      name="phone"
                      label="PROFILE.EDIT_FORM.ADDRESS_FIELDSET.PHONE_FIELD.LABEL"
                    />

                    <FormPhoneInput<ProfileFormInputs>
                      name="mobile"
                      label="PROFILE.EDIT_FORM.ADDRESS_FIELDSET.MOBILE_FIELD.LABEL"
                    />

                    <FormInput<ProfileFormInputs>
                      name="email"
                      label="PROFILE.EDIT_FORM.ADDRESS_FIELDSET.EMAIL_FIELD.LABEL"
                      type="email"
                    />
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Submit Button */}
          <div className="flex justify-end">
            <Button type="submit" disabled={isSubmitting} className="min-w-[120px]">
              {isSubmitting ? t('Loading..') : t('GLOBAL.BTN.SAVE')}
            </Button>
          </div>
        </form>
      </Form>
    </div>
  )
}
