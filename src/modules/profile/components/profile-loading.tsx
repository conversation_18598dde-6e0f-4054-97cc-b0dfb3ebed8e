import { Card, CardContent } from '@/components/ui/card'
import { Skeleton } from '@/components/ui/skeleton'

export function ProfileLoading() {
  return (
    <div className="max-w-4xl mx-auto space-y-8 flex-1 w-full">
      <Card>
        <CardContent className="pt-6">
          {/* Profile Image Section */}
          <div className="flex flex-col items-center space-y-4">
            <div className="relative group">
              {/* Main Profile Image Container */}
              <div className="relative w-32 h-32 sm:w-40 sm:h-40 lg:w-48 lg:h-48">
                {/* Circular Avatar Skeleton */}
                <div className="w-full h-full rounded-full overflow-hidden bg-gray-100 ring-4 ring-white shadow-lg">
                  <Skeleton className="w-full h-full rounded-full" />
                </div>

                {/* Upload Button Skeleton */}
                <div className="absolute bottom-2 right-2 bg-white rounded-full p-2.5 shadow-lg border-2 border-gray-200">
                  <Skeleton className="h-4 w-4 sm:h-5 sm:w-5" />
                </div>
              </div>
            </div>
          </div>

          <div className="grid grid-cols-1 gap-6">
            {/* Personal Information */}
            <div className="space-y-4">
              <Skeleton className="h-6 w-32" /> {/* Section title */}
              <div className="space-y-4">
                {/* Salutation, First Name, Last Name, Birthdate, Family Status, Nationality */}
                {Array.from({ length: 6 }).map((_, i) => (
                  <div key={i} className="space-y-2">
                    <Skeleton className="h-4 w-24" />
                    <Skeleton className="h-10 w-full" />
                  </div>
                ))}
              </div>
            </div>

            {/* Contact Information */}
            <div className="space-y-4">
              <Skeleton className="h-6 w-40" /> {/* Section title */}
              <div className="space-y-4">
                {/* Street, Street Number, ZIP, City, Country */}
                {Array.from({ length: 5 }).map((_, i) => (
                  <div key={i} className="space-y-2">
                    <Skeleton className="h-4 w-24" />
                    <Skeleton className="h-10 w-full" />
                  </div>
                ))}
              </div>
            </div>

            {/* Address Information */}
            <div className="space-y-4">
              <Skeleton className="h-6 w-36" /> {/* Section title */}
              <div className="space-y-4">
                {/* Phone, Mobile, Email */}
                {Array.from({ length: 3 }).map((_, i) => (
                  <div key={i} className="space-y-2">
                    <Skeleton className="h-4 w-24" />
                    <Skeleton className="h-10 w-full" />
                  </div>
                ))}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Submit Button */}
      <div className="flex justify-end">
        <Skeleton className="h-10 w-[120px]" />
      </div>
    </div>
  )
}
