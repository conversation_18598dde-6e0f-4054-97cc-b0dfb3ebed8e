import { ErrorBoundary } from '@/components/error-boundary'

import { getUserProfile } from '../api/profile-api'
import { ProfileForm } from './profile-form'

export async function ProfileFormServer() {
  const profileResponse = await getUserProfile()

  if (profileResponse?.serverError || !profileResponse?.data) {
    return (
      <ErrorBoundary
        error={profileResponse?.serverError?.message || 'Unknown error'}
        title="Failed to load user profile"
        description="There was an error loading your user profile. Please try again."
      />
    )
  }

  return (
    <div className="flex-1 overflow-y-auto p-4">
      <ProfileForm initialData={profileResponse?.data} />
    </div>
  )
}
