import { useTranslations } from 'next-intl'
import { useAction } from 'next-safe-action/hooks'
import { toast } from 'sonner'

import { uploadDocumentAction } from '@/lib/actions/documents/upload-document-action'

import { updateUserProfile } from '../api/profile-actions'

/**
 * Hook for updating user profile
 * Follows the same pattern as useSignup from auth module
 * Provides loading states, success/error handling, and navigation
 */
export function useProfileForm() {
  const t = useTranslations('TOAST_NOTIFICATION.BODY')

  return useAction(updateUserProfile, {
    onSuccess: () => {
      toast.success(t('PROFILE_HAS_BEEN_UPDATED'))
    },
    onError: () => {
      toast.error(t('FAILED_TO_UPDATE_PROFILE'))
    },
  })
}

export function useUpdateProfileImage() {
  const t = useTranslations('TOAST_NOTIFICATION.BODY')

  return useAction(uploadDocumentAction, {
    onSuccess: () => {
      toast.success(t('PROFILE_HAS_BEEN_UPDATED'))
    },
    onError: () => {
      toast.error(t('FAILED_TO_UPDATE_PROFILE_AVATAR'))
    },
  })
}

