'use server'

import { revalidateTag } from 'next/cache'

import { createDetailedError, fetchApi } from '@/lib/fetch-api'
import { authenticatedAction } from '@/lib/safe-actions'

import { riskSchema, SmokerStatus, smokerValuesMap } from '../types/risk-schema'

export const updateUserRisk = authenticatedAction
  .schema(riskSchema)
  .action(async ({ parsedInput, ctx: { session } }) => {
    if (!session?.accessToken) {
      throw new Error('No session found')
    }

    const smokerValue = smokerValuesMap[parsedInput.smoker || SmokerStatus.UNKNOWN]

    const riskData = {
      weight: parsedInput.weight,
      height: parsedInput.height,
      smoker: smokerValue,
      healthInfo: parsedInput.healthInfo,
      additionalRisks: parsedInput.additionalRisks,
    }

    const response = await fetchApi(
      '/profile',
      {
        method: 'PUT',
        body: JSON.stringify({ profile: riskData }),
      },
      session
    )

    if (response.error) {
      throw createDetailedError(response.error, response.details)
    }

    revalidateTag('profile')

    return response.data
  })

export const revalidateRisk = async () => {
  return revalidateTag('profile')
}
