import { z } from 'zod'

export enum SmokerStatus {
  UNKNOWN = 'UNKNOWN',
  NO = 'NO',
  SOMETIMES = 'SOMETIMES',
  YES = 'YES',
}

export enum AdditionalRiskOptionsEnum {
  BOAT = 'RISK.BOAT',
  COLLECTIONS = 'RISK.COLLECTIONS',
  JUWELRY = 'RISK.JUWELRY',
  PARAGLIDING = 'RISK.PARAGLIDING',
  BUNGEE = 'RISK.BUNGEE',
  SKYDIVING = 'RISK.SKYDIVING',
  DIVING = 'RISK.DIVING',
  HEALTH_PROBLEMS = 'RISK.HEALTH-PROBLEMS',
  DRIVING = 'RISK.DRIVING',
  MOTORCYCLE = 'RISK.MOTORCYCLE',
  CREDIT_RATING = 'RISK.CREDIT-RATING',
  MORE_PERSONS = 'RISK.MORE-PERSONS',
  HOUSEHOLD = 'RISK.HOUSEHOLD',
  HABITATE = 'RISK.HABITATE',
  SELF_USED_ESTATE = 'RISK.SELF-USED-ESTATE',
  ANIMALS = 'RISK.ANIMALS',
  SPECIAL_DRIVER_LICENSE = 'RISK.SPECIAL-DRIVER-LICENSE',
  DRIVER_LICENSE_PERIOD = 'RISK.DRIVER-LICENSE-PERIOD',
  RENTED_ESTATE = 'RISK.RENTED-ESTATE',
  OTHERS = 'RISK.OTHERS',
}

export const SmokerEnum = z.enum([
  SmokerStatus.YES,
  SmokerStatus.NO,
  SmokerStatus.UNKNOWN,
  SmokerStatus.SOMETIMES,
] as const)

export const smokerValuesMap = {
  [SmokerStatus.UNKNOWN]: null,
  [SmokerStatus.NO]: 0,
  [SmokerStatus.SOMETIMES]: 50,
  [SmokerStatus.YES]: 100,
}

export type SmokerType = z.infer<typeof SmokerEnum>

export const smokerOptions: Array<{ value: SmokerType; label: string }> = [
  { value: SmokerStatus.NO, label: 'GLOBAL.NO' },
  { value: SmokerStatus.YES, label: 'GLOBAL.YES' },
  { value: SmokerStatus.UNKNOWN, label: 'GLOBAL.UNKNOWN' },
  { value: SmokerStatus.SOMETIMES, label: 'RISK_FORM.SMOKER_FIELD.OPTION.SOMETIMES' },
]

export const AdditionalRiskSchema = z.object({
  id: z.string().optional(),
  type: z.nativeEnum(AdditionalRiskOptionsEnum),
  data: z.string().min(1, 'RISK.VALIDATION.ADDITIONAL_RISK_DATA_REQUIRED').optional(),
})

export type AdditionalRisk = z.infer<typeof AdditionalRiskSchema>

export const riskSchema = z.object({
  weight: z.coerce
    .number({
      invalid_type_error: 'RISK.VALIDATION.WEIGHT_INVALID',
    })
    .min(30, 'RISK.VALIDATION.WEIGHT_MIN')
    .max(300, 'RISK.VALIDATION.WEIGHT_MAX')
    .optional(),
  height: z.coerce
    .number({
      invalid_type_error: 'RISK.VALIDATION.HEIGHT_INVALID',
    })
    .min(100, 'RISK.VALIDATION.HEIGHT_MIN')
    .max(250, 'RISK.VALIDATION.HEIGHT_MAX')
    .optional(),

  smoker: SmokerEnum.optional(),
  healthInfo: z.string().max(255, 'RISK.VALIDATION.HEALTH_INFO_MAX_LENGTH').optional(),
  additionalRisks: z.array(AdditionalRiskSchema).optional(),
})

export type RiskFormInputs = z.infer<typeof riskSchema>
