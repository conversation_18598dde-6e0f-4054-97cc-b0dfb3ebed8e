'use client'

import { useCallback, useEffect, useState } from 'react'

import { zodResolver } from '@hookform/resolvers/zod'
import { useTranslations } from 'next-intl'
import { useForm } from 'react-hook-form'

import { Button } from '@/components/ui/button'
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Form } from '@/components/ui/form'
import { FormSelect, FormTextarea } from '@/components/form-inputs'

import { AdditionalRiskOptionsEnum, AdditionalRiskSchema, type AdditionalRisk } from '../types/risk-schema'

const additionalRiskOptions = Object.values(AdditionalRiskOptionsEnum).map((value) => ({
  value: value,
  label: value,
}))

type AdditionalRiskFormData = AdditionalRisk

interface AdditionalRiskModalProps {
  currentRisk?: AdditionalRisk
  onAddRisk?: (risk: AdditionalRisk) => void
  onEditRisk?: (oldRisk: AdditionalRisk, newRisk: AdditionalRisk) => void
  onDeleteRisk?: (risk: AdditionalRisk) => void
  existingRisks: AdditionalRisk[]
  open?: boolean
  onOpenChange?: (open: boolean) => void
}

export function AdditionalRiskModal({
  currentRisk,
  onAddRisk,
  onEditRisk,
  onDeleteRisk,
  existingRisks,
  open: externalOpen,
  onOpenChange: externalOnOpenChange,
}: AdditionalRiskModalProps) {
  const t = useTranslations()
  const [internalOpen, setInternalOpen] = useState(false)

  const open = externalOpen ?? internalOpen
  const setOpen = externalOnOpenChange ?? setInternalOpen

  const isEditMode = Boolean(currentRisk?.id)

  const form = useForm<AdditionalRiskFormData>({
    resolver: zodResolver(AdditionalRiskSchema),
    defaultValues: isEditMode
      ? {
          id: currentRisk!.id,
          type: currentRisk!.type,
          data: currentRisk!.data || '',
        }
      : {
          type: undefined,
          data: '',
        },
  })

  const existingRiskTypes = existingRisks.map((risk) => risk.type)
  const availableRiskOptions = isEditMode
    ? additionalRiskOptions.filter(
        (option) => option.value === currentRisk!.type || !existingRiskTypes.includes(option.value)
      )
    : additionalRiskOptions.filter((option) => !existingRiskTypes.includes(option.value))

  useEffect(() => {
    if (isEditMode && currentRisk) {
      form.reset({
        id: currentRisk.id,
        type: currentRisk.type,
        data: currentRisk.data || '',
      })
    } else if (!isEditMode) {
      form.reset({
        type: undefined,
        data: '',
      })
    }
  }, [currentRisk, form, open, isEditMode])

  const onSubmit = useCallback(
    (data: AdditionalRiskFormData) => {
      if (isEditMode && currentRisk && onEditRisk) {
        const updatedRisk: AdditionalRisk = {
          id: currentRisk.id,
          type: data.type,
          data: data.data || '',
        }
        if (data.type !== currentRisk.type || data.data !== currentRisk.data) {
          onEditRisk(currentRisk, updatedRisk)
        }
      } else if (!isEditMode && onAddRisk) {
        const newRisk: AdditionalRisk = {
          id: Date.now().toString(), // Generate simple ID for now
          type: data.type,
          data: data.data || '',
        }
        onAddRisk(newRisk)
        form.reset()
      }
      setOpen(false)
    },
    [currentRisk, form, isEditMode, onAddRisk, onEditRisk, setOpen]
  )

  const handleDelete = useCallback(() => {
    if (isEditMode && currentRisk && onDeleteRisk) {
      onDeleteRisk(currentRisk)
      setOpen(false)
    }
  }, [currentRisk, isEditMode, onDeleteRisk, setOpen])

  const getTranslationKey = useCallback(
    (key: string) => {
      const baseKey = isEditMode
        ? 'RISK.EDIT.ADDITIONAL_RISKS_FIELDSET.EDIT_MODAL'
        : 'RISK.EDIT.ADDITIONAL_RISKS_FIELDSET.MODAL'
      return `${baseKey}.${key}`
    },
    [isEditMode]
  )

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogContent className="md:max-w-[50vw] max-w-[90vw]">
        <DialogHeader>
          <DialogTitle>{t(getTranslationKey('TITLE'))}</DialogTitle>
          <DialogDescription>{t(getTranslationKey('DESCRIPTION'))}</DialogDescription>
        </DialogHeader>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <FormSelect<AdditionalRiskFormData>
              name="type"
              label={getTranslationKey('SELECT_LABEL')}
              options={availableRiskOptions}
              placeholder={t(getTranslationKey('SELECT_PLACEHOLDER'))}
            />
            <FormTextarea<AdditionalRiskFormData>
              name="data"
              label={getTranslationKey('DATA_LABEL')}
              placeholder={t(getTranslationKey('DATA_PLACEHOLDER'))}
            />
            <DialogFooter className={isEditMode ? 'flex justify-between' : ''}>
              {isEditMode && (
                <Button type="button" variant="destructive" onClick={handleDelete}>
                  {t('RISK.EDIT.ADDITIONAL_RISKS_FIELDSET.EDIT_MODAL.DELETE_BUTTON')}
                </Button>
              )}
              <div className="flex gap-2">
                <DialogClose asChild>
                  <Button type="button" variant="outline">
                    {t('GLOBAL.CANCEL')}
                  </Button>
                </DialogClose>
                <Button
                  type="submit"
                  disabled={!form.formState.isValid || (!isEditMode && availableRiskOptions.length === 0)}
                >
                  {t(
                    isEditMode
                      ? 'RISK.EDIT.ADDITIONAL_RISKS_FIELDSET.EDIT_MODAL.SAVE_BUTTON'
                      : 'RISK.EDIT.ADDITIONAL_RISKS_FIELDSET.MODAL.ADD_BUTTON'
                  )}
                </Button>
              </div>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  )
}
