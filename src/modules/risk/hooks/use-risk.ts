import { useAction } from 'next-safe-action/hooks'
import { toast } from 'sonner'
import { useTranslations } from 'next-intl'

import { updateUserRisk } from '../api/risk-actions'

export function useRiskForm() {
  const t = useTranslations()

  return useAction(updateUserRisk, {
    onSuccess: () => {
      toast.success(t('TOAST_NOTIFICATION.BODY.RECORD_SAVED_SUCCESSFULLY'))
    },
    onError: ({ error }) => {
      console.error('Risk update error:', error)
      toast.error(t('TOAST_NOTIFICATION.BODY.SAVING_RECORD_FAILED'))
    },
  })
} 