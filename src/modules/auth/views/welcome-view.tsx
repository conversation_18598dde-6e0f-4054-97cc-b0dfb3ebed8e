import { getTranslations } from 'next-intl/server'
import Link from 'next/link'

import { But<PERSON> } from '@/components/ui/button'
import { <PERSON>, CardContent, CardFooter, CardTitle } from '@/components/ui/card'
import { Logo } from '@/components/logo'

export const WelcomeView = async () => {
  const t = await getTranslations()

  return (
    <Card className="w-full max-w-2xl">
      <CardTitle className="flex flex-col items-center justify-center gap-4">
        <Logo />
        <h1 className="text-2xl font-semibold text-gray-900">{t('LOGIN.WELCOME.TITLE')}</h1>
      </CardTitle>
      <CardContent className="text-center space-y-6  max-w-md mx-auto">
        <p className="text-sm text-gray-600">{t('LOGIN.WELCOME.DESCRIPTION')}</p>
      </CardContent>
      <CardFooter className="flex flex-col gap-4">
        <Button asChild className="w-full">
          <Link href={'/sign-up'}>{t('LOGIN.WELCOME.SIGNUP_BUTTON.TEXT')}</Link>
        </Button>

        <Button variant="ghost" asChild className="w-full">
          <Link href={'/login'}>{t('LOGIN.WELCOME.LOGIN_BUTTON.TEXT')}</Link>
        </Button>
      </CardFooter>
    </Card>
  )
}
