'use client'

import { zod<PERSON><PERSON>ol<PERSON> } from '@hookform/resolvers/zod'
import { ScrollArea } from '@radix-ui/react-scroll-area'
import { ChevronLeft } from 'lucide-react'
import { useTranslations } from 'next-intl'
import Link from 'next/link'
import { FormProvider, useForm } from 'react-hook-form'

import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { FormInput } from '@/components/form-inputs/form-input'
import { FormPhoneInput } from '@/components/form-inputs/form-phone-input'
import { FormSelect } from '@/components/form-inputs/form-select'

import { SuccessMessage } from '../components/success-message'
import { useSignup } from '../hooks/auth-hooks'
import { genderOptions, signupSchema, SignupSchemaInputs } from '../types/auth-schema'

export const SignupView = () => {
  const { executeAsync: signup, isPending: isSigningUp, hasSucceeded } = useSignup()
  const t = useTranslations()

  const form = useForm<SignupSchemaInputs>({
    resolver: zodResolver(signupSchema),
    mode: 'all',
    defaultValues: {
      first_name: '',
      last_name: '',
      phone: '',
      email: '',
      password: '',
      passwordConfirm: '',
      salutation: undefined,
      type: undefined,
    },
  })

  const {
    handleSubmit,
    formState: { isSubmitting },
  } = form

  const onSubmit = async (data: SignupSchemaInputs) => {
    await signup({
      ...data,
      type: data.salutation === 'COMPANY' ? 'COMPANY' : 'PERSON',
    })
  }

  return (
    <>
      {hasSucceeded ? (
        <SuccessMessage
          title={t('LOGIN.SIGNUP_SUCCESS.TITLE')}
          message={t('LOGIN.SIGNUP_SUCCESS.DESCRIPTION')}
          buttonText={t('LOGIN.SIGNUP_SUCCESS.CONFIRM_BUTTON.TEXT')}
          buttonHref="/login"
        />
      ) : (
        <Card className="flex flex-col w-full max-w-xl">
          <ScrollArea className="h-full max-h-[90dvh] overflow-y-auto">
            <Button variant="ghost" size="icon" asChild className="ml-2">
              <Link href="/welcome">
                <ChevronLeft className="size-6" />
              </Link>
            </Button>

            <CardContent className="max-w-md mx-auto">
              <FormProvider {...form}>
                <form onSubmit={handleSubmit(onSubmit)} className="w-full">
                  <div className="flex flex-col gap-4 pt-2 pb-6 px-6 w-full max-w-2xl mx-auto">
                    <h1 className="text-2xl font-semibold">{t('LOGIN.SIGNUP.TITLE')}</h1>

                    <FormSelect<SignupSchemaInputs>
                      name="salutation"
                      label="LOGIN.SIGNUP.SALUTATION_FIELD.LABEL"
                      options={genderOptions}
                      isRequired
                    />

                    <FormInput<SignupSchemaInputs>
                      name="first_name"
                      label="LOGIN.SIGNUP.FIRSTNAME_FIELD.LABEL"
                      isRequired
                    />

                    <FormInput<SignupSchemaInputs>
                      name="last_name"
                      label="LOGIN.SIGNUP.LASTNAME_FIELD.LABEL"
                      isRequired
                    />

                    <FormPhoneInput<SignupSchemaInputs>
                      name="phone"
                      label="LOGIN.SIGNUP.PHONE_FIELD.LABEL"
                      isRequired
                      className="flex-1"
                    />

                    <FormInput<SignupSchemaInputs>
                      name="email"
                      label="LOGIN.SIGNUP.EMAIL_FIELD.LABEL"
                      type="email"
                      isRequired
                    />

                    <FormInput<SignupSchemaInputs>
                      name="password"
                      label="LOGIN.SIGNUP.FIELD_PASSWORD.LABEL"
                      type="password"
                      isRequired
                    />

                    <FormInput<SignupSchemaInputs>
                      name="passwordConfirm"
                      label="LOGIN.SIGNUP.FIELD_PASSWORD_REPEAT.LABEL"
                      type="password"
                      isRequired
                    />

                    <Button type="submit" disabled={isSubmitting || isSigningUp}>
                      {isSubmitting ? t('LOADING_PLEASE_WAIT') : t('LOGIN.SIGNUP.SUBMIT_BUTTON.TEXT')}
                    </Button>

                    <div className="text-center text-sm">
                      <p>{t('LOGIN.SIGNUP.TERMS_AND_CONDITIONS.TEXT')}</p>
                    </div>
                  </div>
                </form>
              </FormProvider>
            </CardContent>
          </ScrollArea>
        </Card>
      )}
    </>
  )
}
