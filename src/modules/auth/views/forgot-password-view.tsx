'use client'

import { zod<PERSON><PERSON>olver } from '@hookform/resolvers/zod'
import { ChevronLeft } from 'lucide-react'
import { useTranslations } from 'next-intl'
import Link from 'next/link'
import { useForm } from 'react-hook-form'

import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { Form } from '@/components/ui/form'
import { FormInput } from '@/components/form-inputs/form-input'

import { SuccessMessage } from '../components/success-message'
import { useForgotPassword } from '../hooks/auth-hooks'
import { forgotPasswordSchema, ForgotPasswordSchemaInputs } from '../types/auth-schema'

export const ForgotPasswordView = () => {
  const { executeAsync, hasSucceeded, isPending } = useForgotPassword()
  const t = useTranslations()
  const form = useForm<ForgotPasswordSchemaInputs>({
    resolver: zodResolver(forgotPasswordSchema),
    mode: 'all',
    defaultValues: { username: '' },
  })

  const {
    handleSubmit,
    formState: { isSubmitting },
  } = form

  const onSubmit = async (inputs: ForgotPasswordSchemaInputs) => {
    await executeAsync(inputs)
  }

  return (
    <>
      {hasSucceeded ? (
        <SuccessMessage
          title="E-Mail gesendet"
          message={
            <div>
              <p>Der Link zum Zurücksetzen des Passworts wurde an Ihre E-Mail-Adresse gesendet.</p>
              <p>
                Überprüfen Sie Ihren Posteingang:
                <a className="text-blue-500 ml-2" href={`mailto:${form.getValues('username')}`}>
                  {form.getValues('username')}
                </a>
              </p>
            </div>
          }
          buttonText="Zurück zum Login"
          buttonHref="/login"
        />
      ) : (
        <Card className="flex flex-col w-full max-w-xl">
          <Button variant="ghost" size="icon" asChild className="ml-2">
            <Link href="/welcome">
              <ChevronLeft className="size-6" />
            </Link>
          </Button>
          <CardContent className="max-w-md mx-auto">
            <Form {...form}>
              <form onSubmit={handleSubmit(onSubmit)} className="w-full">
                <div className="flex flex-col gap-4 pt-2 pb-6 px-6 w-full max-w-2xl mx-auto">
                  <h1 className="text-2xl font-semibold">{t('LOGIN.FORGOT_PASSWORD.TITLE')}</h1>

                  <FormInput<ForgotPasswordSchemaInputs>
                    name="username"
                    label="LOGIN.FORGOT_PASSWORD.USERNAME_FIELD.LABEL"
                    isRequired
                    type="email"
                  />

                  <Button type="submit" disabled={isSubmitting || isPending}>
                    {isSubmitting || isPending
                      ? t('LOADING_PLEASE_WAIT')
                      : t('RESET_PASSWORD.RESET_PASSWORD.SUBMIT_BUTTON.TEXT')}
                  </Button>
                </div>
              </form>
            </Form>
          </CardContent>
        </Card>
      )}
    </>
  )
}
