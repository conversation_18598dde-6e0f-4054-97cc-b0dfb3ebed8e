'use client'

import { zod<PERSON><PERSON>ol<PERSON> } from '@hookform/resolvers/zod'
import { ChevronLeft } from 'lucide-react'
import { useTranslations } from 'next-intl'
import Link from 'next/link'
import { useForm } from 'react-hook-form'

import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { Form } from '@/components/ui/form'
import { FormInput } from '@/components/form-inputs/form-input'
import { Logo } from '@/components/logo'

import { useLogin } from '../hooks/auth-hooks'
import { loginSchema, LoginSchemaInputs } from '../types/auth-schema'

export const SignInView = () => {
  const { isPending: isLoggingIn, executeAsync: login } = useLogin()

  const t = useTranslations()

  const form = useForm<LoginSchemaInputs>({
    resolver: zodResolver(loginSchema),
    mode: 'all',
    defaultValues: {
      username: '',
      password: '',
    },
  })

  const {
    handleSubmit,
    formState: { isSubmitting },
  } = form

  const onSubmit = async (data: LoginSchemaInputs) => {
    await login(data)
  }

  return (
    <Card className="flex flex-col w-full max-w-xl">
      <Button variant="ghost" size="icon" asChild className="ml-2">
        <Link href="/welcome">
          <ChevronLeft className="size-6" />
        </Link>
      </Button>
      <CardContent className="max-w-md mx-auto">
        <div className="flex justify-center mb-8">
          <Logo />
        </div>

        <Form {...form}>
          <form onSubmit={handleSubmit(onSubmit)} className="w-full">
            <div className="flex flex-col gap-4 pt-2 pb-6 px-6 w-full max-w-2xl mx-auto">
              <h1 className="text-2xl font-semibold">{t('LOGIN.WELCOME.TITLE')}</h1>

              <FormInput<LoginSchemaInputs> name="username" label="LOGIN.LOGIN.USERNAME_FIELD.LABEL" isRequired />
              <FormInput<LoginSchemaInputs>
                name="password"
                label="LOGIN.LOGIN.PASSWORD_FIELD.LABEL"
                type="password"
                isRequired
              />

              <Button type="submit" disabled={isSubmitting || isLoggingIn}>
                {isSubmitting || isLoggingIn
                  ? t('LOGIN.WELCOME.LOGIN_BUTTON.TEXT')
                  : t('LOGIN.LOGIN.SUBMIT_BUTTON.TEXT')}
              </Button>

              <div className="text-center">
                <Link href="/forgot-password" className="text-sm hover:underline">
                  {t('LOGIN.LOGIN.FORGOT_PASSWORD_BUTTON.TEXT')}
                </Link>
              </div>
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  )
}
