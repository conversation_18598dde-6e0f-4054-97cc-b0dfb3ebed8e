import { useTranslations } from 'next-intl'
import { useAction } from 'next-safe-action/hooks'
import { useRouter } from 'next/navigation'
import { toast } from 'sonner'

import { forgotPassword, loginUser, signUp } from '../actions/auth-actions'

export const useLogin = () => {
  const router = useRouter()
  const t = useTranslations()
  return useAction(loginUser, {
    onSuccess: () => {
      router.push('/contracts')
    },
    onError: () => {
      toast.error(t('LOGIN.LOGIN.TOAST.FAILED.TITLE'), {
        description: t('LOGIN.LOGIN.TOAST.FAILED.MESSAGE'),
      })
    },
  })
}

export function useSignup() {
  const t = useTranslations('LOGIN.SIGNUP.TOAST')

  return useAction(signUp, {
    onSuccess: () => {
      toast.success(t('SUCCESS.MESSAGE'))
    },
    onError: () => {
      toast.error(t('ERROR.MESSAGE'))
    },
  })
}

export function useForgotPassword() {
  const t = useTranslations('LOGIN.FORGOT_PASSWORD.TOAST')

  return useAction(forgotPassword, {
    onError: () => {
      toast.error(t('FAILED.MESSAGE'))
    },
  })
}
