'use server'

import { getAppConfig } from '@/app-config'
import { cookies } from 'next/headers'
import { redirect } from 'next/navigation'
import qs from 'qs'

import { encryptSession } from '@/lib/encrypt-session'
import { createDetailedError, fetchApi } from '@/lib/fetch-api'
import { action } from '@/lib/safe-actions'

import {
  forgotPasswordSchema,
  loginSchema,
  loginWithTokenSchema,
  ManageClientDataManagementTokenSchemaInputs,
  signupSchema,
} from '../types/auth-schema'
import { AuthSession, LoginResponse, UserProfileResponse } from '../types/auth-types'

const MANAGE_CLIENT_DATA_MANAGEMENT_TOKEN_COOKIE_NAME = 'manage-client-data-management-token'

export const loginUser = action.schema(loginSchema).action(async ({ parsedInput: { username, password } }) => {
  const config = getAppConfig()
  if (!config.ssoUrl) {
    throw new Error('SSO URL is not defined')
  }

  const loginResponse = await fetchApi<LoginResponse>(`${config.ssoUrl}/oauth`, {
    method: 'POST',
    body: JSON.stringify({
      grant_type: 'password',
      scope: 'client',
      username,
      password,
    }),
  })

  if (loginResponse.error || !loginResponse.data) {
    throw createDetailedError(loginResponse.error || 'Login failed', loginResponse.details)
  }

  const userProfileResponse = await fetchApi<UserProfileResponse>(`/profile`, {
    headers: {
      Authorization: `Bearer ${loginResponse.data?.access_token}`,
    },
  })

  if (userProfileResponse.error || !userProfileResponse.data) {
    throw createDetailedError(userProfileResponse.error || 'Failed to fetch user profile', userProfileResponse.details)
  }

  const user = userProfileResponse.data?._embedded?.profile[0]

  if (!user) {
    throw new Error('User profile not found')
  }

  const session: AuthSession = {
    accessToken: loginResponse.data.access_token,
    refreshToken: loginResponse.data.refresh_token,
    // Use actual expiration from server
    expiresAt: Date.now() + loginResponse.data.expires_in * 1000,
    expiresIn: loginResponse.data.expires_in,
    user: {
      id: user.id,
      email: user.email,
      agentId: user.agentId,
      agencyId: user.agencyId,
    },
  }

  const encryptedSession = await encryptSession(session)
  const cookieStore = await cookies()

  cookieStore.set('session', encryptedSession, {
    httpOnly: config.environment === 'production',
    secure: config.environment === 'production',
    sameSite: 'lax',
    maxAge: 60 * 60 * 24 * 7, // 7 days
  })

  return session
})

export const manageClientDataManagementToken = async ({
  action,
  token,
}: ManageClientDataManagementTokenSchemaInputs) => {
  const cookieStore = await cookies()
  if (action === 'set') {
    if (!token) {
      throw new Error('Token is required')
    }
    cookieStore.set(MANAGE_CLIENT_DATA_MANAGEMENT_TOKEN_COOKIE_NAME, token)
  }
  if (action === 'get') {
    const token = cookieStore.get(MANAGE_CLIENT_DATA_MANAGEMENT_TOKEN_COOKIE_NAME)
    if (!token) {
      throw new Error('Token is not set')
    }
    return token.value
  }
}

export const loginWithToken = action.schema(loginWithTokenSchema).action(async ({ parsedInput: { token } }) => {
  const queryParams: Record<string, string | number> = {
    token,
  }

  const queryString = qs.stringify(queryParams, {
    addQueryPrefix: true,
    encode: false,
  })

  const loginUrl = `/login_by_token?${queryString}`

  // TODO remove this after BE implements the new login_by_token endpoint
  const loginResponse = await loginUser({
    username: '<EMAIL>',
    password: 'Test1234!',
  })

  if (loginResponse?.data) {
    await manageClientDataManagementToken({
      action: 'set',
      token,
    })
    return {
      data: loginResponse.data,
    }
  }

  return {
    serverError: 'Failed to login with token',
  }
})

export const logout = action.action(async () => {
  const cookieStore = await cookies()
  cookieStore.delete('session')

  console.log('🚪 [logout] Session cleared, redirecting to login')
  redirect('/login')
})

export const forgotPassword = action.schema(forgotPasswordSchema).action(async ({ parsedInput: { username } }) => {
  const response = await fetchApi(`${getAppConfig().ssoUrl}/b2c/forgot-password`, {
    method: 'POST',
    body: JSON.stringify({ username }),
  })

  if (response.error) {
    throw createDetailedError(response.error, response.details)
  }

  return { success: true }
})

export const signUp = action.schema(signupSchema).action(async ({ parsedInput }) => {
  const response = await fetchApi(`/profile`, {
    method: 'POST',
    body: JSON.stringify(parsedInput),
  })

  if (response.error) {
    throw createDetailedError(response.error, response.details)
  }

  return { success: true }
})
