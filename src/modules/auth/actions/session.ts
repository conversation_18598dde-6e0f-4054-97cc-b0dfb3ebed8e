'use server'

import { getAppConfig } from '@/app-config'
import { cookies } from 'next/headers'
import { z } from 'zod'

import { decryptSession, setEncryptedSessionCookie } from '@/lib/encrypt-session'
import { createDetailedError, fetchApi } from '@/lib/fetch-api'
import { action } from '@/lib/safe-actions'

import { AuthSession, LoginResponse } from '../types/auth-types'
import { logout } from './auth-actions'

// Simple session management without Promise mutex
let isRefreshing = false
let lastRefreshTime = 0
const REFRESH_COOLDOWN = 5000 // 5 seconds cooldown

export const getSessionCookie = async () => {
  const cookieStore = await cookies()
  const encryptedSession = cookieStore.get('session')?.value
  return encryptedSession
}

export async function getSession() {
  const encryptedSession = await getSessionCookie()

  if (!encryptedSession) {
    return null
  }

  const session = await decryptSession(encryptedSession)

  if (!session?.accessToken || !session?.refreshToken || !session?.expiresAt) {
    return null
  }

  return session
}

export const refreshToken = action
  .schema(z.object({ refreshToken: z.string() }))
  .action(async ({ parsedInput: { refreshToken } }) => {
    const now = Date.now()

    // Simple cooldown check
    if (now - lastRefreshTime < REFRESH_COOLDOWN) {
      console.log('🚫 Refresh cooldown active, skipping...')
      return await getSession()
    }

    // Simple flag check
    if (isRefreshing) {
      console.log('🔄 Refresh already in progress, skipping...')
      return null
    }

    isRefreshing = true
    lastRefreshTime = now

    try {
      const session = await getSession()

      if (!session) {
        return await logout()
      }

      const config = getAppConfig()
      const response = await fetchApi<LoginResponse>(`${config.ssoUrl}/oauth`, {
        method: 'POST',
        body: JSON.stringify({
          grant_type: 'refresh_token',
          refresh_token: refreshToken,
        }),
      })

      if (response.error || !response.data) {
        await logout()
        throw createDetailedError(response.error || 'Token refresh failed', response.details)
      }
      const newSession: AuthSession = {
        accessToken: response.data.access_token,
        refreshToken: response.data.refresh_token,
        expiresAt: Date.now() + response.data.expires_in * 1000,
        expiresIn: response.data.expires_in,
        user: { ...session.user },
      }

      await setEncryptedSessionCookie(newSession)
      console.log('✅ Token refreshed successfully')

      return newSession
    } catch (error) {
      console.error('❌ Token refresh failed:', error)
      throw error
    } finally {
      isRefreshing = false
    }
  })

export const removeSession = async () => {
  const cookieStore = await cookies()
  cookieStore.delete('session')
}
