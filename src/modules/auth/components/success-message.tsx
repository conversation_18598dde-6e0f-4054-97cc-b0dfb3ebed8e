import Image from 'next/image'
import Link from 'next/link'

import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'

interface SuccessMessageProps {
  title: string
  message: string | React.ReactNode
  buttonText: string
  buttonHref: string
}

export const SuccessMessage = ({ title, message, buttonText, buttonHref }: SuccessMessageProps) => {
  return (
    <Card className="flex flex-col w-full max-w-2xl py-20">
      <CardContent className="flex flex-col gap-12 items-center justify-center">
        <Image
          src="/images/success.png"
          alt="Success Register"
          className="w-[200px] h-[170px]"
          width={200}
          height={170}
        />
        <h1 className="text-2xl font-semibold text-gray-900">{title}</h1>
        {typeof message === 'string' ? <p>{message}</p> : message}
        <Button asChild className="w-fit">
          <Link href={buttonHref}>{buttonText}</Link>
        </Button>
      </CardContent>
    </Card>
  )
}
