import React from 'react'

import Image from 'next/image'

import LocaleSwitcher from '@/components/locale-switcher'

interface AuthLayoutProps {
  children: React.ReactNode
}

export function AuthLayout({ children }: AuthLayoutProps) {
  return (
    <div className="relative min-h-screen flex flex-col items-center justify-center overflow-hidden">
      <LocaleSwitcher justFlag className="absolute top-4 right-4" />
      <Image
        priority
        width={1920}
        height={1080}
        src="/images/auth-background.png"
        alt="Background"
        className="absolute inset-0 w-full h-full object-cover  lg:object-cover object-left lg:object-center -z-10"
      />
      <div className="container flex flex-col items-center justify-center relative p-4 md:p-0">{children}</div>
    </div>
  )
}
