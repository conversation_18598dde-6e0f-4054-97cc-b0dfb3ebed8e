'use client'

import { useState } from 'react'

import { Camera } from 'lucide-react'
import { useTranslations } from 'next-intl'
import { FieldPath, FieldValues, useFormContext } from 'react-hook-form'

import { cn } from '@/lib/utils'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Button } from '@/components/ui/button'
import { FormControl, FormField, FormItem, FormMessage } from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'

interface ProfileImageUploadProps<TFieldValues extends FieldValues> {
  name: FieldPath<TFieldValues>
  label?: string
  currentImageUrl?: string
  userInitials?: string
  className?: string
}

export const ProfileImageUpload = <TFieldValues extends FieldValues>({
  name,
  label,
  currentImageUrl,
  userInitials = 'AM',
  className,
}: ProfileImageUploadProps<TFieldValues>) => {
  const { control } = useFormContext<TFieldValues>()
  const [previewUrl, setPreviewUrl] = useState<string | null>(null)
  const t = useTranslations()

  const handleFileChange = (files: FileList | null, onChange: (files: File[]) => void) => {
    if (files && files.length > 0) {
      const file = files[0]
      
      // Validate file type
      if (!file.type.startsWith('image/')) {
        return
      }

      // Validate file size (max 5MB)
      if (file.size > 5 * 1024 * 1024) {
        return
      }

      // Create preview URL
      const url = URL.createObjectURL(file)
      setPreviewUrl(url)
      
      // Update form value
      onChange([file])
    }
  }

  const displayImageUrl = previewUrl || currentImageUrl

  return (
    <FormField
      control={control}
      name={name}
      render={({ field }) => (
        <FormItem className={cn('flex flex-col items-center gap-4', className)}>
          {label && (
            <Label className="text-sm font-medium">
              {t(label)}
            </Label>
          )}
          
          <div className="relative">
            {/* Profile Avatar */}
            <Avatar className="w-64 h-64 border-4 border-white shadow-lg">
              <AvatarImage 
                src={displayImageUrl} 
                alt="Profile" 
                className="object-cover"
              />
              <AvatarFallback className="text-4xl font-semibold bg-gradient-to-br from-blue-600 to-blue-800 text-white">
                {userInitials}
              </AvatarFallback>
            </Avatar>

            {/* Camera Button */}
            <div className="absolute bottom-4 right-4">
              <FormControl>
                <div className="relative">
                  <Input
                    type="file"
                    accept="image/*"
                    className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                    onChange={(e) => handleFileChange(e.target.files, field.onChange)}
                  />
                  <Button
                    type="button"
                    size="icon"
                    className="w-12 h-12 rounded-full bg-white border-2 border-gray-200 shadow-lg hover:bg-gray-50"
                    variant="outline"
                  >
                    <Camera className="w-6 h-6 text-gray-600" />
                  </Button>
                </div>
              </FormControl>
            </div>
          </div>

          <FormMessage />
        </FormItem>
      )}
    />
  )
}
