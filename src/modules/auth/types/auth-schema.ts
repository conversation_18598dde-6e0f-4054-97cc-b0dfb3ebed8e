import { z } from 'zod'

export const searchSchema = z.object({
  redirect: z.string().optional().catch(''),
})

export const loginSchema = z.object({
  username: z.string().min(1, 'this field is required'),
  password: z.string().min(1, 'this field is required'),
  // .regex(/[A-Z]/, 'Password must contain at least one uppercase letter')
  // .regex(/[a-z]/, 'Password must contain at least one lowercase letter')
  // .regex(/[0-9]/, 'Password must contain at least one number'),
})

export type LoginSchemaInputs = z.infer<typeof loginSchema>

export const loginWithTokenSchema = z.object({
  token: z.string().min(1, 'this field is required'),
})

export type LoginWithTokenSchemaInputs = z.infer<typeof loginWithTokenSchema>

// Define enum schemas using z.enum (best practice)
export const GenderEnum = z.enum(['MISTER', 'MISSIS', 'UNKNOWN', 'COMPANY'] as const)
export const RegistrationTypeEnum = z.enum(['PERSON', 'COMPANY'] as const)

// Export inferred types (following documents-types.ts pattern)
export type GenderType = z.infer<typeof GenderEnum>
export type RegistrationType = z.infer<typeof RegistrationTypeEnum>

// Define salutation options with type safety
export const genderOptions: Array<{ value: GenderType; label: string }> = [
  { value: 'MISTER', label: 'LOGIN.SIGNUP.SALUTATION_FIELD.OPTION.MISTER' },
  { value: 'MISSIS', label: 'LOGIN.SIGNUP.SALUTATION_FIELD.OPTION.MISSIS' },
  { value: 'UNKNOWN', label: 'LOGIN.SIGNUP.SALUTATION_FIELD.OPTION.UNKNOWN' },
  { value: 'COMPANY', label: 'LOGIN.SIGNUP.SALUTATION_FIELD.OPTION.COMPANY' },
] as const

export const signupSchema = z
  .object({
    salutation: GenderEnum.optional(),
    type: RegistrationTypeEnum.optional(),
    first_name: z.string().min(1, 'this field is required'),
    last_name: z.string().min(1, 'this field is required'),
    phone: z.string().min(1, 'this field is required'),
    email: z.string().email('this field is required'),
    password: z.string().min(1, 'this field is required'),
    // .regex(/[A-Z]/, 'Passwort muss mindestens einen Großbuchstaben enthalten')
    // .regex(/[a-z]/, 'Passwort muss mindestens einen Kleinbuchstaben enthalten')
    // .regex(/[0-9]/, 'Passwort muss mindestens eine Zahl enthalten'),
    passwordConfirm: z.string().min(1, 'this field is required'),
  })
  .refine((data) => data.password === data.passwordConfirm, {
    message: 'LOGIN.SIGNUP.FIELD_PASSWORD_REPEAT.INVALID.MESSAGE',
    path: ['passwordConfirm'],
  })

export type SignupSchemaInputs = z.infer<typeof signupSchema>

export const forgotPasswordSchema = z.object({
  username: z.string().email('this field is required'),
})

export type ForgotPasswordSchemaInputs = z.infer<typeof forgotPasswordSchema>

export const manageClientDataManagementTokenSchema = z.object({
  action: z.enum(['set', 'get']),
  token: z.string().optional(),
})

export type ManageClientDataManagementTokenSchemaInputs = z.infer<typeof manageClientDataManagementTokenSchema>
