import { z } from 'zod'

// Define enum schema using z.enum (best practice)
const PaymentPeriodEnum = z.enum(['ONCE', 'WEEKLY', 'MONTHLY', 'QUARTERLY', 'HALFYEARLY', 'YEARLY', 'FREE'] as const)

// Export inferred type (following documents-types.ts pattern)
export type PaymentPeriodType = z.infer<typeof PaymentPeriodEnum>

// Define payment period options with type safety
export const paymentPeriodOptions: Array<{ value: PaymentPeriodType; label: string }> = [
  { value: 'ONCE', label: 'CONTRACTS.EDIT_FORM.PAYMENT_PERIOD_FIELD.OPTION.ONCE' },
  { value: 'WEEKLY', label: 'CONTRACTS.EDIT_FORM.PAYMENT_PERIOD_FIELD.OPTION.WEEKLY' },
  { value: 'MONTHLY', label: 'CONTRACTS.EDIT_FORM.PAYMENT_PERIOD_FIELD.OPTION.MONTHLY' },
  { value: 'QUARTERLY', label: 'CONTRACTS.EDIT_FORM.PAYMENT_PERIOD_FIELD.OPTION.QUARTERLY' },
  { value: 'HALFYEARLY', label: 'CONTRACTS.EDIT_FORM.PAYMENT_PERIOD_FIELD.OPTION.HALFYEARLY' },
  { value: 'YEARLY', label: 'CONTRACTS.EDIT_FORM.PAYMENT_PERIOD_FIELD.OPTION.YEARLY' },
  { value: 'FREE', label: 'CONTRACTS.EDIT_FORM.PAYMENT_PERIOD_FIELD.OPTION.FREE' },
] as const

// Base schema for contract fields
export const contractSchema = z
  .object({
    category_id: z.string({
      required_error: 'CONTRACTS.VALIDATION.CATEGORY_REQUIRED',
    }),
    provider_id: z.string({
      required_error: 'CONTRACTS.VALIDATION.PROVIDER_REQUIRED',
    }),
    product_id: z.string({
      required_error: 'CONTRACTS.VALIDATION.PRODUCT_REQUIRED',
    }),
    insurance_number: z.string({
      required_error: 'CONTRACTS.VALIDATION.INSURANCE_NUMBER_REQUIRED',
    }),
    cost: z.coerce
      .number({
        required_error: 'CONTRACTS.VALIDATION.COST_REQUIRED',
        invalid_type_error: 'CONTRACTS.VALIDATION.COST_INVALID',
      })
      .min(0, 'CONTRACTS.VALIDATION.COST_POSITIVE'),
    period: PaymentPeriodEnum,
    info: z.string().optional(),
    start_date: z.date({
      required_error: 'CONTRACTS.VALIDATION.START_DATE_REQUIRED',
      invalid_type_error: 'CONTRACTS.VALIDATION.START_DATE_INVALID',
    }),
    end_date: z.date({
      required_error: 'CONTRACTS.VALIDATION.END_DATE_REQUIRED',
      invalid_type_error: 'CONTRACTS.VALIDATION.END_DATE_INVALID',
    }),
    files: z.array(z.any()).optional(),
    editForm: z.boolean().optional(),
    contract_id: z.string().optional(),
  })
  .refine((data) => data.end_date >= data.start_date, {
    message: 'CONTRACTS.VALIDATION.END_DATE_AFTER_START',
    path: ['end_date'],
  })

// Type for form inputs
export type ContractFormInputs = z.infer<typeof contractSchema>
