import { useTranslations } from 'next-intl'
import { useAction } from 'next-safe-action/hooks'
import { useRouter } from 'next/navigation'
import { toast } from 'sonner'

import { createOrUpdateContract } from '../api/contracts-actions'

/**
 * Hook for creating/editing a contract
 * Follows the same pattern as useSignup from auth module
 * Provides loading states, success/error handling, and navigation
 */

export function useContractForm() {
  const router = useRouter()
  const t = useTranslations()

  return useAction(createOrUpdateContract, {
    onSuccess: ({ data }) => {
      toast.success(t('CONTRACTS.EDIT_FORM.EDIT_MODE.TOAST.SUCCESS.MESSAGE'))
      // Redirect to contracts list after successful creation/editing
      router.push(`/contracts/${data?.id}`)
    },
    onError: () => {
      toast.error(t('CONTRACTS.EDIT_FORM.EDIT_MODE.TOAST.ERROR.MESSAGE'))
    },
  })
}
