import qs from 'qs'

import { getImageURL, IMAGE_SIZES } from '@/lib/actions/documents/get-image'
import { createDetailedError } from '@/lib/fetch-api'
import { fetchAuthenticatedApi } from '@/lib/fetch-with-auth-api'
import { BaseDocument } from '@/components/documents/types'
import { PaginationSearchParams } from '@/components/pagination/pagination-search-params'

import { Category, CategoryResponse, Contract, ContractsResponse, ProductData, Provider } from '../libs/contract-types'
import { CONTRACTS_TAGS } from './contracts-tags'

export const getContracts = async ({ page, perPage, search, status }: PaginationSearchParams) => {
  // check props
  // Build query parameters object
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const queryParams: Record<string, any> = {}

  // Calculate start parameter for API pagination
  // API uses start (offset) and limit parameters
  const start = (page - 1) * perPage

  queryParams.page = page
  queryParams.start = start
  queryParams.limit = perPage

  // Add optional filters
  if (search && search.trim()) {
    queryParams.search = search.trim()
  }
  if (status && status.trim()) {
    queryParams.status = status.trim()
  }

  const queryString = qs.stringify(queryParams, { addQueryPrefix: false })

  const response = await fetchAuthenticatedApi<ContractsResponse>(`/contracts-simple?${queryString}`, {
    next: {
      revalidate: 60,
      tags: [CONTRACTS_TAGS.ALL, CONTRACTS_TAGS.ALL + '-' + page],
    },
  })

  if (response.error) {
    const error = createDetailedError(response.error, response.details)
    return {
      serverError: error,
    }
  }

  if (!response.data || !response.data._embedded || !response.data._embedded.contracts) {
    return {
      data: {
        contracts: [],
        pagination: {
          page,
          perPage,
          totalItems: 0,
          totalPages: 0,
        },
      },
    }
  }

  const contracts = response.data._embedded.contracts
  const accessToken = response.data.session.accessToken

  // Process contracts to include image URLs
  const contractsWithImages = contracts.map((contract) => {
    const providerImgId = contract.provider_img_id
      ? getImageURL({
          documentId: contract.provider_img_id,
          accessToken: accessToken,
          dimensions: IMAGE_SIZES.MEDIUM,
        })
      : ''
    const categoryImgId = contract.category_img_id
      ? getImageURL({
          documentId: contract.category_img_id,
          accessToken: accessToken,
          dimensions: IMAGE_SIZES.MEDIUM,
        })
      : ''

    return {
      ...contract,
      provider_img_id: providerImgId,
      category_img_id: categoryImgId,
    }
  })

  // Extract pagination information from response
  const totalItems = response.data.total_items || 0
  const totalPages = Math.ceil(totalItems / perPage)

  return {
    data: {
      contracts: contractsWithImages,
      pagination: {
        page,
        perPage,
        totalItems,
        totalPages,
      },
    },
  }
}

export const getAllContracts = async () => {
  const response = await fetchAuthenticatedApi<ContractsResponse>(`/contracts-simple`, {
    next: {
      revalidate: 60,
      tags: [CONTRACTS_TAGS.ALL],
    },
  })

  if (response.error) {
    const error = createDetailedError(response.error, response.details)
    return {
      serverError: error,
    }
  }

  if (!response.data || !response.data._embedded || !response.data._embedded.contracts) {
    return {
      data: [],
    }
  }

  const contracts = response.data._embedded.contracts
  const accessToken = response.data.session.accessToken

  // Process contracts to include image URLs
  const contractsWithImages = contracts.map((contract) => {
    const providerImgId = contract.provider_img_id
      ? getImageURL({
          documentId: contract.provider_img_id,
          accessToken: accessToken,
          dimensions: IMAGE_SIZES.MEDIUM,
        })
      : ''
    const categoryImgId = contract.category_img_id
      ? getImageURL({
          documentId: contract.category_img_id,
          accessToken: accessToken,
          dimensions: IMAGE_SIZES.MEDIUM,
        })
      : ''

    return {
      ...contract,
      provider_img_id: providerImgId,
      category_img_id: categoryImgId,
    }
  })

  return { data: contractsWithImages }
}

export const getCategories = async () => {
  const response = await fetchAuthenticatedApi<CategoryResponse>('/categories', {
    next: {
      revalidate: 60,
      tags: [CONTRACTS_TAGS.CATEGORIES],
    },
  })

  if (response.error) {
    const error = createDetailedError(response.error, response.details)
    return {
      serverError: error,
    }
  }

  if (!response.data || !response.data._embedded || !response.data._embedded.categories) {
    return {
      data: {
        categories: [],
        categoryOptions: [],
      },
    }
  }

  const categories = response.data._embedded.categories

  // Filter active categories
  const activeCategories = categories.filter((category: Category) => category.status === 'ACTIVE')
  const categoryOptions = activeCategories.map((category: Category) => ({
    value: category.id,
    label: category.name,
  }))

  return {
    data: {
      categories: activeCategories,
      categoryOptions,
    },
  }
}

export const getProviders = async () => {
  const response = await fetchAuthenticatedApi<{ _embedded: { providers: Provider[] } }>('/providers', {
    next: {
      revalidate: 60,
      tags: [CONTRACTS_TAGS.PROVIDERS],
    },
  })

  if (response.error) {
    const error = createDetailedError(response.error, response.details)
    return {
      serverError: error,
    }
  }

  if (!response.data || !response.data._embedded || !response.data._embedded.providers) {
    return {
      data: [],
    }
  }

  const providers = response.data._embedded.providers

  // Process providers to split categories
  const providersWithCategories = providers.map((provider: Provider) => {
    if (typeof provider.categories === 'string') {
      provider.categories = provider.categories.split(',')
    }
    return provider
  })

  return { data: providersWithCategories }
}

export const getProducts = async () => {
  const response = await fetchAuthenticatedApi<{ data: ProductData[] }>('/products/all', {
    next: {
      revalidate: 60,
      tags: [CONTRACTS_TAGS.PRODUCTS],
    },
  })

  if (response.error) {
    const error = createDetailedError(response.error, response.details)
    return {
      serverError: error,
    }
  }

  if (!response.data || !response.data.data) {
    return {
      data: [],
    }
  }

  const products = response.data.data

  // Map products to the expected format
  const mappedProducts = products.map((product: ProductData) => ({
    id: product.i,
    name: product.n,
    categoryId: product.c,
    providerId: product.p,
  }))

  return { data: mappedProducts }
}

export const getContractById = async (id: string) => {
  const response = await fetchAuthenticatedApi<Contract>(`/contracts/${id}`, {
    next: {
      revalidate: 60,
      tags: [`${CONTRACTS_TAGS.ALL}-${id}`],
    },
  })

  if (response.error) {
    const error = createDetailedError(response.error, response.details)
    return {
      serverError: error,
    }
  }

  if (!response.data) {
    return {
      data: null,
    }
  }
  const session = response.data?.session

  const providerImgId = response.data?.provider_img_id
  const providerImage = providerImgId
    ? getImageURL({
        documentId: providerImgId,
        accessToken: session!.accessToken,
        dimensions: IMAGE_SIZES.MEDIUM,
      })
    : ''

  return { data: { ...response.data, provider_img_id: providerImage } }
}

export const getContractsByIds = async (contractIds: number[]) => {
  try {
    const contractPromises = contractIds.map((id) => getContractById(id.toString()))
    const contractResults = await Promise.all(contractPromises)

    const errors = contractResults.filter((result) => result.serverError)
    if (errors.length > 0) {
      return {
        serverError: `Failed to fetch ${errors.length} contract(s): ${errors[0].serverError}`,
      }
    }

    const contracts = contractResults.map((result) => result.data).filter((contract) => contract !== null)

    return {
      data: contracts,
    }
  } catch (error) {
    return {
      serverError: `Error fetching contracts: ${error instanceof Error ? error.message : 'Unknown error'}`,
    }
  }
}

export const getContractDocuments = async (id: string) => {
  const response = await fetchAuthenticatedApi<{ data: BaseDocument[] }>(
    `/contracts/${id}/documents?limit=15&page=1&params=CLIENT`,
    {
      next: {
        revalidate: 60,
        tags: [`${CONTRACTS_TAGS.DOCUMENTS}-${id}`],
      },
    }
  )

  if (response.error) {
    const error = createDetailedError(response.error, response.details)
    return {
      serverError: error,
    }
  }

  if (!response.data || !response.data.data) {
    return {
      data: [],
    }
  }

  const documents = response.data?.data || []
  const session = response.data?.session

  const documentsWithUrls: BaseDocument[] = documents.map((document: BaseDocument) => {
    return {
      ...document,
      documentUrl: getImageURL({ documentId: document.id.toString(), accessToken: session!.accessToken }),
    }
  })

  return { data: documentsWithUrls }
}
