'use server'

import { format } from 'date-fns'
import { revalidateTag } from 'next/cache'

import { createDetailedError, fetchApi } from '@/lib/fetch-api'
import { authenticatedAction } from '@/lib/safe-actions'

import { Contract } from '../libs/contract-types'
import { contractSchema } from '../types/contract-schema'
import { CONTRACTS_TAGS } from './contracts-tags'

export const createOrUpdateContract = authenticatedAction
  .schema(contractSchema)
  .action(async ({ parsedInput, ctx: { session } }) => {
    // Format dates for API - exclude files from API data
    const apiData = {
      category: parsedInput.category_id,
      provider_id: parsedInput.provider_id,
      product_id: parsedInput.product_id,
      insurance_number: parsedInput.insurance_number,
      payment_period: parsedInput.period,
      payment: parsedInput.cost,
      info: parsedInput.info || '',
      start: format(parsedInput.start_date as Date, 'yyyy-MM-dd'),
      end: format(parsedInput.end_date as Date, 'yyyy-MM-dd'),
    }

    const url = parsedInput.editForm ? `/contracts/${parsedInput.contract_id}` : '/contracts/'

    const response = await fetchApi<Contract>(
      url,
      {
        method: parsedInput.editForm ? 'PUT' : 'POST',

        body: JSON.stringify(apiData),
      },
      session
    )

    if (response.error) {
      throw createDetailedError(response.error, response.details)
    }

    if (!response.data) {
      throw new Error('No data returned from contract creation')
    }

    if (parsedInput.files) {
      const mappedFiles = parsedInput.files.map((file) => {
        return {
          ...file,
          type: 'PICTURE',
          owner_type: 'CONTRACT',
          owner_id: response?.data?.id || '',
        }
      })
      for (const file of mappedFiles) {
        const response = await fetchApi<Contract>(
          `/documents_base64`,
          {
            method: 'POST',
            headers: {
              'Api-Proxy': 'graphql',
            },
            body: JSON.stringify(file),
          },
          session
        )

        if (response.error) {
          throw createDetailedError(response.error, response.details)
        }
      }
    }

    revalidateTag(CONTRACTS_TAGS.ALL)
    revalidateTag(`${CONTRACTS_TAGS.ALL}-${response.data.id}`)

    return response.data
  })
