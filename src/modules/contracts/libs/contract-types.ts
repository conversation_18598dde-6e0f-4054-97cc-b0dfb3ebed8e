export interface ContractsResponse {
  _embedded: { contracts: Contract[] }
  page_count: number
  page_size: number
  total_items: number
  page: number
}

export interface PaginationInfo {
  page: number
  perPage: number
  totalItems: number
  totalPages: number
}

export interface PaginatedContractsResponse {
  contracts: Contract[]
  pagination: PaginationInfo
}

export interface Contract {
  id: string
  created?: string
  modified?: string
  type?: string
  status: ContractStatus | string
  provider_id: string
  provider_name: string
  provider_img_id: string
  module?: string
  category: string
  category_img_id?: string
  category_type?: string
  category_group?: string
  category_code?: string
  category_name: string
  category_description?: string
  product_id: string
  product_name: string
  insurance_number: string
  start: string
  end: string
  due_date?: string | null
  coverage?: string
  deductible?: number
  currency: string
  payment: number
  payment_period: PaymentPeriod
  payment_type?: string
  score?: string | null
  info: string
  agency_id?: string
  agency_name?: string
  agent_id?: string
  risk?: string | null
  agent_name?: string
  has_damage_allowed?: boolean | null
}

export type PaymentPeriod = 'ONCE' | 'WEEKLY' | 'MONTHLY' | 'QUARTERLY' | 'HALFYEARLY' | 'YEARLY' | 'FREE'

export type ContractStatus = 'ACTIVE' | 'INACTIVE' | 'PENDING' | 'CANCELLED' | 'CONTRACT'

export interface UpdateContractPayload {
  insurance_number: string
  payment_period: PaymentPeriod
  payment: number
  info: string
  start: string
  end: string
}

export interface Category {
  id: string
  name: string
  description: string
  display: string
  group: string
  img_id: string
  level: number
  status: string
  tax_percent: string
  type: string
}

export interface CategoryResponse {
  _embedded: { categories: Category[] }
  page_count: number
  page_size: number
  total_items: number
  page: number
}

export interface Provider {
  id: string
  name: string
  name2: string
  agencyId: number
  categories: string | string[]
  code: string
  imgId: string
}

export interface Product {
  id: string
  name: string
  categoryId: string
  providerId: string
}

export interface ProductData {
  i: string // id
  n: string // name
  c: string // categoryId
  p: string // providerId
}
