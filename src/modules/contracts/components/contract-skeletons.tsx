import { PlusIcon } from 'lucide-react'
import { getTranslations } from 'next-intl/server'

import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { Skeleton } from '@/components/ui/skeleton'
import { FormFieldSkeleton } from '@/components/skeletons'

export const ContractHeaderSkeleton = () => {
  return (
    <div className="hidden md:flex justify-between items-center pb-6 flex-shrink-0 px-4 pt-4">
      <div />
      <Skeleton className="h-8 w-48" />
      <div />
    </div>
  )
}

export const ContractListSkeleton = async () => {
  const t = await getTranslations()
  return (
    <div className="h-full flex flex-col">
      {/* Header - matching ContractList structure */}
      <div className="hidden md:flex justify-between items-center pb-6 flex-shrink-0 px-4 pt-4">
        <div />
        <h1 className="text-2xl font-semibold text-[#111827]">{t('Contracts')}</h1>
        <Button variant="ghost" className="text-success">
          <PlusIcon />
          {t('CONTRACTS.LIST.ADD_BUTTON.TEXT')}
        </Button>
      </div>

      {/* Add Contract Button - Mobile */}
      <div className="md:hidden flex justify-end p-4">
        <Skeleton className="h-10 w-40 rounded-md" />
      </div>

      {/* Scrollable Content Area - matching ContractList structure */}
      <div className="flex-1 overflow-y-auto p-4">
        <div className="max-w-4xl grid grid-cols-1 mx-auto gap-3 md:gap-6">
          {Array.from({ length: 2 }).map((_, i) => (
            <Card
              key={i}
              className="h-full overflow-hidden rounded-[20px] border border-[#E5E7EB] shadow-[0px_10px_30px_0px_rgba(0,66,117,0.12)]"
            >
              <CardContent className="p-6">
                <div className="flex items-center h-full">
                  <Skeleton className="size-[160px] mr-4 flex-shrink-0 rounded-md" />
                  <div className="flex flex-col h-full w-full justify-between">
                    <Skeleton className="h-6 w-3/4" />
                    <hr className="border-t border-b my-3 border-[#F3F4F6]" />
                    <Skeleton className="h-5 w-1/2" />
                    <Skeleton className="h-4 w-1/3 mt-1" />
                    <hr className="border-t border-b my-3 border-[#F3F4F6]" />
                    <div className="flex justify-between items-center">
                      <Skeleton className="h-4 w-16" />
                      <Skeleton className="h-4 w-24" />
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>

      {/* Fixed Pagination at Bottom - matching ContractList structure */}
      <div className="justify-center flex-shrink-0 pt-4 border-t border-gray-200 bg-white sticky bottom-0 px-4 pb-2">
        <div className="flex md:flex-row flex-col items-center gap-3 justify-between w-full">
          <div className="flex items-center gap-1 flex-1">
            <Skeleton className="w-8 h-8 rounded" />
            <Skeleton className="w-8 h-8 rounded" />
            <div className="flex items-center gap-1 text-sm text-gray-700 mx-2">
              <span>Page</span>
              <Skeleton className="w-10 h-8 rounded" />
              <span>of</span>
              <Skeleton className="w-10 h-8 rounded" />
            </div>
            <Skeleton className="w-8 h-8 rounded" />
            <Skeleton className="w-8 h-8 rounded" />
            <Skeleton className="w-8 h-8 rounded ml-1" />
          </div>
          <span className="text-xs md:text-sm text-gray-700 md:ml-4">
            Displaying <Skeleton className="w-10 h-4 inline-block" /> of <Skeleton className="w-10 h-4 inline-block" />
          </span>
        </div>
      </div>
    </div>
  )
}

export const ContractDetailsSkeleton = () => {
  return (
    <div className="w-full mt-6 pb-6 max-w-4xl mx-auto shadow-2xl rounded-lg px-6  py-10">
      {/* Provider Info Card */}
      <div className="flex md:flex-row flex-col gap-8">
        {/* Provider Image - larger and centered */}
        <div className="rounded-xl overflow-hidden border relative h-fit w-fit mx-auto">
          <Skeleton className="size-[248px]" />
        </div>

        {/* Information sections with separators */}
        <div className="flex-1 flex flex-col gap-6">
          {/* Title at the top */}
          <div className="text-center md:text-left mb-8">
            <Skeleton className="h-7 w-64 mx-auto md:mx-0" />
          </div>

          {/* Insurance Number */}
          <div className="pb-6 border-b border-gray-200">
            <Skeleton className="h-4 w-32 mb-2" />
            <Skeleton className="h-5 w-48" />
          </div>

          {/* Product */}
          <div className="pb-6 border-b border-gray-200">
            <Skeleton className="h-4 w-16 mb-2" />
            <Skeleton className="h-5 w-24" />
          </div>

          {/* Sports */}
          <div className="pb-7 border-b border-gray-200">
            <Skeleton className="h-4 w-12 mb-2" />
            <Skeleton className="h-5 w-72" />
          </div>

          {/* Notes */}
          <div className="pb-7 border-b border-gray-200">
            <Skeleton className="h-4 w-12 mb-2" />
            <Skeleton className="h-5 w-0" /> {/* Empty notes */}
          </div>

          {/* Payment and Status row */}
          <div className="pb-7 border-b border-gray-200">
            <div className="grid grid-cols-2 gap-8">
              {/* Payment */}
              <div>
                <Skeleton className="h-4 w-16 mb-2" />
                <Skeleton className="h-6 w-20 mb-1" />
                <Skeleton className="h-4 w-16" />
              </div>

              {/* Status */}
              <div>
                <Skeleton className="h-4 w-12 mb-2" />
                <Skeleton className="h-6 w-16 rounded-full" />
              </div>
            </div>
          </div>

          {/* Valid from date - no border at bottom */}
          <div>
            <Skeleton className="h-12 w-64" />
          </div>
        </div>
      </div>
    </div>
  )
}

export function CreateContractLoading() {
  return (
    <div className="h-full flex flex-col w-full">
      <div className="space-y-8 max-w-4xl mx-auto w-full">
        <Card className="w-full">
          <CardContent className="pt-6">
            <div className="grid grid-cols-1 gap-4">
              {/* Form field skeletons */}
              {Array.from({ length: 8 }).map((_, index) => (
                <FormFieldSkeleton key={index} />
              ))}

              {/* Textarea skeleton */}
              <div className="mt-6">
                <Skeleton className="h-4 w-40 mb-2" />
                <Skeleton className="h-24 w-full" />
              </div>

              {/* File upload skeleton */}
              <div className="mt-6">
                <Skeleton className="h-20 w-full" />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Submit button skeleton */}
        <div className="flex justify-end">
          <Skeleton className="h-10 w-32" />
        </div>
      </div>
    </div>
  )
}
