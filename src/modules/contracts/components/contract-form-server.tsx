import { ErrorBoundary } from '@/components/error-boundary'

import { getCategories, getContractById, getProducts, getProviders } from '../api/contracts-api'
import { Contract } from '../libs/contract-types'
import { ContractForm } from './contract-form'

interface ContractFormServerProps {
  editForm?: boolean
  id?: string
}

export async function ContractFormServer({ editForm, id }: ContractFormServerProps) {
  const [categories, providers, products] = await Promise.all([getCategories(), getProviders(), getProducts()])

  let contract = undefined

  if (editForm && id) {
    const contractResponse = await getContractById(id)

    if (contractResponse?.serverError || !contractResponse?.data || !contractResponse.data.id) {
      return (
        <ErrorBoundary
          error={contractResponse?.serverError?.message || 'Unknown error'}
          title="Failed to load contract"
          description="There was an error loading the contract. Please try again."
        />
      )
    }

    contract = contractResponse.data as Contract
  }

  if (categories?.serverError || providers?.serverError || products?.serverError) {
    return (
      <ErrorBoundary
        error={
          categories?.serverError?.message ||
          providers?.serverError?.message ||
          products?.serverError?.message ||
          'Unknown error'
        }
        title="Failed to load contracts"
        description="There was an error loading the contracts. Please try again."
      />
    )
  }

  return (
    <div className="flex-1 overflow-y-auto p-4">
      <ContractForm
        categoryOptions={categories?.data?.categoryOptions || []}
        providers={providers?.data || []}
        products={products?.data || []}
        editForm={editForm}
        contract={contract}
      />
    </div>
  )
}
