'use client'

import { useMemo } from 'react'

import { zodResolver } from '@hookform/resolvers/zod'
import { Loader2 } from 'lucide-react'
import { useTranslations } from 'next-intl'
import { useForm } from 'react-hook-form'

import { fileToDocument } from '@/lib/actions/documents/file-to-document'
import { cn } from '@/lib/utils'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { Form } from '@/components/ui/form'
import { FormCalendar, FormInput, FormSelect, FormTextarea, FormUpload, SelectOption } from '@/components/form-inputs'

import { useContractForm } from '../hooks/use-contracts'
import { Contract, PaymentPeriod, Product, Provider } from '../libs/contract-types'
import { ContractFormInputs, contractSchema, paymentPeriodOptions } from '../types/contract-schema'

interface ContractFormProps {
  categoryOptions: SelectOption[]
  onComplete?: (contract: ContractFormInputs) => Promise<void>
  providers: Provider[]
  products: Product[]
  editForm?: boolean
  contract?: Contract
  className?: string
  isLoading?: boolean // External loading state for save operations
}

export function ContractForm({
  categoryOptions,
  providers,
  products,
  editForm,
  contract,
  className,
  onComplete,
  isLoading = false,
}: ContractFormProps) {
  const t = useTranslations()
  const { executeAsync, isPending: isCreatingContract } = useContractForm()

  const form = useForm<ContractFormInputs>({
    resolver: zodResolver(contractSchema),
    defaultValues: {
      category_id: contract?.category ?? '',
      provider_id: contract?.provider_id ?? '',
      product_id: contract?.product_id ?? '',
      insurance_number: contract?.insurance_number ?? '',
      cost: contract?.payment ?? 0,
      period: contract?.payment_period ?? ('MONTHLY' as PaymentPeriod),
      start_date: contract?.start ? new Date(contract.start) : undefined,
      end_date: contract?.end ? new Date(contract.end) : undefined,
      info: contract?.info ?? '',
      files: undefined,
      editForm: editForm,
      contract_id: contract?.id ?? '',
    },
  })

  const { watch, setValue } = form
  const categoryId = watch('category_id')
  const providerId = watch('provider_id')

  const providerOptions = useMemo(() => {
    if (!categoryId) {
      return []
    }
    const filteredProviders = providers
      .filter((provider) => {
        if (contract?.provider_id) {
          return provider.id === contract?.provider_id
        }
        return provider?.categories?.includes(categoryId)
      })
      .map((provider) => ({
        value: provider.id,
        label: provider.name,
      }))
    if (filteredProviders.length === 0) {
      return [
        {
          value: 'n.n',
          label: 'n.n',
        },
      ]
    }
    return filteredProviders
  }, [categoryId, providers, contract])

  const productOptions = useMemo(() => {
    if (!providerId) {
      return []
    }
    const filteredProducts = products
      .filter((product) => {
        if (contract?.product_id) {
          return product.id === contract?.product_id
        }
        return product.providerId === providerId && product.categoryId === categoryId
      })
      .map((product) => ({
        value: product.id,
        label: product.name,
      }))
    if (filteredProducts.length === 0) {
      return [
        {
          value: 'n.n',
          label: 'n.n',
        },
      ]
    }
    return filteredProducts
  }, [providerId, products, categoryId, contract])

  const onSubmit = async (data: ContractFormInputs) => {
    let files: any[] = []

    if (data.files) {
      files = await Promise.all(data.files.map(async (file) => await fileToDocument(file)))
    }

    if (onComplete) {
      console.log('Submitting contract data:', data)

      await onComplete(data)
    } else {
      await executeAsync({
        ...data,
        files: files,
      })
    }
  }

  const safePaymentPeriodOptions = paymentPeriodOptions.map((option) => ({
    value: option.value,
    label: t(option.label),
  }))

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className={cn('space-y-8 max-w-4xl mx-auto', className)}>
        <Card>
          <CardContent className="pt-6">
            <div className="grid grid-cols-1  gap-4">
              {/* Category Selection */}
              <FormSelect<ContractFormInputs>
                name="category_id"
                label="CONTRACTS.EDIT_FORM.CATEGORY_FIELD.LABEL"
                options={categoryOptions}
                isRequired
                autoComplete
                disabled={editForm}
                onChange={(value) => {
                  setValue('provider_id', '')
                  setValue('product_id', '')
                  setValue('category_id', value)
                }}
              />
              {/* Provider Selection */}
              <FormSelect<ContractFormInputs>
                name="provider_id"
                label="CONTRACTS.EDIT_FORM.PROVIDER_FIELD.LABEL"
                options={providerOptions}
                isRequired
                disabled={!categoryId || editForm}
              />

              {/* Product Selection */}
              <FormSelect<ContractFormInputs>
                name="product_id"
                label="CONTRACTS.EDIT_FORM.PRODUCT_FIELD.LABEL"
                options={productOptions}
                isRequired
                disabled={!providerId || editForm}
              />

              {/* Insurance Number */}
              <FormInput<ContractFormInputs>
                name="insurance_number"
                label="CONTRACTS.EDIT_FORM.INSURANCE_NUMBER_FIELD.LABEL"
                isRequired
              />

              {/* Cost */}
              <FormInput<ContractFormInputs>
                name="cost"
                label="CONTRACTS.EDIT_FORM.COST_FIELD.LABEL"
                isRequired
                type="number"
              />

              {/* Payment Period */}
              <FormSelect<ContractFormInputs>
                name="period"
                label="CONTRACTS.EDIT_FORM.PAYMENT_PERIOD_FIELD.LABEL"
                options={safePaymentPeriodOptions}
                isRequired
              />

              {/* Start Date */}
              <FormCalendar<ContractFormInputs>
                name="start_date"
                label="CONTRACTS.EDIT_FORM.STARTDATE_FIELD.LABEL"
                isRequired
              />

              {/* End Date */}

              <FormCalendar<ContractFormInputs>
                name="end_date"
                label="CONTRACTS.EDIT_FORM.ENDDATE_FIELD.LABEL"
                isRequired
              />
            </div>

            {/* Comments/Info */}
            <div className="mt-6">
              <FormTextarea<ContractFormInputs> name="info" label="CONTRACTS.EDIT_FORM.COMMENTS_FIELD.LABEL" />
            </div>

            {/* File Upload */}
            {!editForm && (
              <div className="mt-6">
                <FormUpload<ContractFormInputs>
                  name="files"
                  label=""
                  accept=".pdf,.jpg,.jpeg,.png"
                  multiple={true}
                  maxFiles={10}
                  maxSize={10}
                />
              </div>
            )}
          </CardContent>
        </Card>

        {/* Submit Button */}

        <div className="flex justify-end">
          <Button type="submit" disabled={isCreatingContract || isLoading} className="w-full md:w-auto">
            {isCreatingContract || isLoading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                {t('Loading..')}
              </>
            ) : (
              t('CONTRACTS.EDIT_FORM.SUBMIT_BUTTON.TEXT')
            )}
          </Button>
        </div>
      </form>
    </Form>
  )
}
