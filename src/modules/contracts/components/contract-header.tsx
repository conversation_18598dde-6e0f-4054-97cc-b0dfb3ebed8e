import { Edit } from 'lucide-react'
import { getTranslations } from 'next-intl/server'
import Link from 'next/link'

import { cn } from '@/lib/utils'
import { Button } from '@/components/ui/button'
import { NavigationBackButton } from '@/components/navigation-back-button'

interface ContractHeaderProps {
  id: string
  activeUrl: 'details' | 'documents'
  backUrl?: string
}

export const ContractHeader = async ({ id, activeUrl, backUrl }: ContractHeaderProps) => {
  const t = await getTranslations()

  return (
    <div>
      {/* TOP NAVIGATION */}

      <div className=" justify-between items-center px-6 py-8 hidden md:flex">
        {/* Left side - Back button */}
        <NavigationBackButton onlyIcon fallbackUrl={backUrl || '/contracts'} />

        {/* Center - Title */}
        <h1 className="text-xl font-bold text-[#142A3A] leading-[1.4] font-mulish">{t('Contract Details')}</h1>

        {/* Right side - Edit button */}
        <div className="flex items-center h-[25px]">
          <Button asChild variant="ghost" className="flex items-center gap-[9px] p-0 h-auto hover:bg-transparent">
            <Link href={`/contracts/${id}/edit`}>
              <Edit className="w-6 h-6 text-[#3BCBBF]" />
              <span className="text-sm font-semibold text-[#3BCBBF] leading-[1.57] font-mulish">{t('Edit')}</span>
            </Link>
          </Button>
        </div>
      </div>

      {/* BOTTOM NAVIGATION */}
      <div className="flex items-center gap-4 mt-2 md:mt-0 md:px-6">
        <Button variant="ghost" className={cn(activeUrl === 'details' && 'bg-gray-50 text-black')}>
          <Link href={`/contracts/${id}`}>{t('Details')}</Link>
        </Button>
        <Button variant="ghost" className={cn(activeUrl === 'documents' && 'bg-gray-50 text-black')}>
          <Link href={`/contracts/${id}/documents`}>{t('Documents')}</Link>
        </Button>
      </div>
    </div>
  )
}
