'use server'

import { getSession } from '@/modules/auth/actions/session'

import { DocumentList } from '@/components/documents'
import { ErrorBoundary } from '@/components/error-boundary'

import { getContractDocuments } from '../api/contracts-api'

interface ContractDocumentListProps {
  contractId: string
}

export async function ContractDocumentList({ contractId }: ContractDocumentListProps) {
  const session = await getSession()

  if (!session) {
    return <ErrorBoundary error="Authentication required" />
  }

  const documentsResponse = await getContractDocuments(contractId)

  // Handle server error
  if (documentsResponse?.serverError) {
    return <ErrorBoundary error={documentsResponse.serverError.message} />
  }

  // Extract documents array from response
  const responseData = documentsResponse?.data
  const documents = Array.isArray(responseData) ? responseData : responseData || []

  return <DocumentList documents={documents} accessToken={session.accessToken} />
}
