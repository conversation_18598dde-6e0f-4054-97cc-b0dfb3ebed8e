'use client'

import { useRef, useState } from 'react'

import { FilePlus, Trash2, Upload, X } from 'lucide-react'
import { useTranslations } from 'next-intl'

import { Button } from '@/components/ui/button'
import { FormControl, FormDescription, FormItem, FormLabel, FormMessage } from '@/components/ui/form'
import { Input } from '@/components/ui/input'

interface FileUploadProps {
  onChange: (files: File[]) => void
  value?: File[]
  label: string
  description?: string
  accept?: string
  multiple?: boolean
  maxFiles?: number
  maxSize?: number // in MB
}

export function FileUpload({
  onChange,
  value = [],
  label,
  description,
  accept = '.pdf,.jpg,.jpeg,.png',
  multiple = true,
  maxFiles = 10,
  maxSize = 10, // 10MB default
}: FileUploadProps) {
  const t = useTranslations()
  const inputRef = useRef<HTMLInputElement>(null)
  const [files, setFiles] = useState<File[]>(value || [])
  const [error, setError] = useState<string | null>(null)

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (!e.target.files?.length) return

    setError(null)
    const newFiles = Array.from(e.target.files)

    // Check file size
    const oversizedFiles = newFiles.filter((file) => file.size > maxSize * 1024 * 1024)
    if (oversizedFiles.length > 0) {
      setError(
        t('CONTRACTS.EDIT_FORM.DOCUMENTS_FIELD.ERROR.FILE_TOO_LARGE', {
          maxSize,
          fileName: oversizedFiles[0].name,
        })
      )
      return
    }

    // Check max files
    if (files.length + newFiles.length > maxFiles) {
      setError(
        t('CONTRACTS.EDIT_FORM.DOCUMENTS_FIELD.ERROR.TOO_MANY_FILES', {
          maxFiles,
        })
      )
      return
    }

    const updatedFiles = [...files, ...newFiles]
    setFiles(updatedFiles)
    onChange(updatedFiles)

    // Reset input value to allow selecting the same file again
    if (inputRef.current) {
      inputRef.current.value = ''
    }
  }

  const removeFile = (index: number) => {
    const updatedFiles = [...files]
    updatedFiles.splice(index, 1)
    setFiles(updatedFiles)
    onChange(updatedFiles)
  }

  const clearFiles = () => {
    setFiles([])
    onChange([])
    if (inputRef.current) {
      inputRef.current.value = ''
    }
  }

  return (
    <FormItem className="space-y-3">
      <FormLabel>{label}</FormLabel>
      <FormControl>
        <div className="space-y-4">
          {/* Upload area */}
          <div
            className="flex flex-col items-center justify-center w-full h-32 px-4 transition bg-white border-2 border-gray-300 border-dashed rounded-md appearance-none cursor-pointer hover:border-gray-400 focus:outline-none"
            onClick={() => inputRef.current?.click()}
          >
            <div className="flex flex-col items-center space-y-2">
              <Upload className="w-6 h-6 text-gray-500" />
              <span className="font-medium text-gray-600">
                {t('CONTRACTS.EDIT_FORM.DOCUMENTS_FIELD.DRAG_DROP')}
              </span>
              <span className="text-xs text-gray-500">
                {t('CONTRACTS.EDIT_FORM.DOCUMENTS_FIELD.FILE_TYPES')}
              </span>
            </div>
            <Input
              ref={inputRef}
              type="file"
              className="hidden"
              multiple={multiple}
              accept={accept}
              onChange={handleFileChange}
            />
          </div>

          {/* Error message */}
          {error && <p className="text-sm font-medium text-destructive">{error}</p>}

          {/* File list */}
          {files.length > 0 && (
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <h4 className="text-sm font-medium">
                  {t('CONTRACTS.EDIT_FORM.DOCUMENTS_FIELD.SELECTED_FILES')} ({files.length})
                </h4>
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  onClick={clearFiles}
                  className="h-8 px-2 text-destructive"
                >
                  <Trash2 className="w-4 h-4 mr-1" />
                  {t('CONTRACTS.EDIT_FORM.DOCUMENTS_FIELD.CLEAR_ALL')}
                </Button>
              </div>

              <ul className="space-y-2 max-h-60 overflow-y-auto">
                {files.map((file, index) => (
                  <li key={index} className="flex items-center justify-between p-2 bg-gray-50 rounded-md">
                    <div className="flex items-center overflow-hidden">
                      <FilePlus className="w-4 h-4 mr-2 flex-shrink-0 text-gray-500" />
                      <span className="text-sm truncate">{file.name}</span>
                      <span className="text-xs text-gray-500 ml-2">
                        ({(file.size / 1024 / 1024).toFixed(2)} MB)
                      </span>
                    </div>
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      onClick={() => removeFile(index)}
                      className="h-6 w-6 p-0 text-destructive"
                    >
                      <X className="w-4 h-4" />
                    </Button>
                  </li>
                ))}
              </ul>
            </div>
          )}
        </div>
      </FormControl>
      {description && <FormDescription>{description}</FormDescription>}
      <FormMessage />
    </FormItem>
  )
}
