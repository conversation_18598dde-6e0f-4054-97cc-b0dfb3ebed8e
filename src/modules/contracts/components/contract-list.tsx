import { FilePlus, PlusIcon } from 'lucide-react'
import { getTranslations } from 'next-intl/server'
import Link from 'next/link'

import { Button } from '@/components/ui/button'
import { EmptyState } from '@/components/empty-state'
import { ErrorBoundary } from '@/components/error-boundary'
import { PaginationControls } from '@/components/pagination/pagination-controls'

import { PaginationSearchParams } from '../../../components/pagination/pagination-search-params'
import { getContracts } from '../api/contracts-api'
import { Contract } from '../libs/contract-types'
import { ContractCard } from './contract-card'

interface Props {
  filters: PaginationSearchParams
}

export async function ContractList({ filters }: Props) {
  const t = await getTranslations()

  // Get pagination parameters from URL
  const { page, perPage, search, status } = filters

  // Fetch contracts with all filters
  const contractsResponse = await getContracts({
    page,
    perPage,
    search,
    status,
  })

  if (contractsResponse?.serverError) {
    return (
      <ErrorBoundary
        error={contractsResponse?.serverError.message || 'Unknown error'}
        title={t('CONTRACTS.ERROR.TITLE', { defaultValue: 'Failed to load contracts' })}
        description={t('CONTRACTS.ERROR.DESCRIPTION', {
          defaultValue: 'There was an error loading your contracts. Please try again.',
        })}
      />
    )
  }

  const contracts = contractsResponse?.data.contracts

  const pagination = contractsResponse?.data.pagination
  const totalItems = pagination?.totalItems || 0
  const totalPages = pagination?.totalPages || 0

  if (pagination?.totalItems === 0) {
    return (
      <EmptyState
        pageTitle={t('Contracts')}
        actions={
          <Button variant="ghost" asChild className="text-success">
            <Link href="/contracts/create">
              <FilePlus className="w-4 h-4 " />
              {t('CONTRACTS.LIST.ADD_BUTTON.TEXT')}
            </Link>
          </Button>
        }
        title={t('CONTRACTS.LIST.EMPTY_TEXT')}
      />
    )
  }

  return (
    <div className="h-full flex flex-col">
      {/* Header */}
      <div className="hidden md:flex justify-between items-center pb-6 flex-shrink-0 px-4 pt-4">
        <div />
        <h1 className="text-2xl font-semibold text-[#111827]">{t('Contracts')}</h1>
        <Button asChild variant="ghost" className="text-success">
          <Link href="/contracts/create">
            <PlusIcon />
            {t('CONTRACTS.LIST.ADD_BUTTON.TEXT')}
          </Link>
        </Button>
      </div>

      {/* Add Contract Button - Mobile */}
      <div className="md:hidden flex justify-end p-4">
        <Button asChild variant="ghost" className="text-success">
          <Link href="/contracts/create">
            <PlusIcon />
            {t('CONTRACTS.LIST.ADD_BUTTON.TEXT')}
          </Link>
        </Button>
      </div>

      {/* Scrollable Content Area */}
      <div className="flex-1 overflow-y-auto p-4">
        <div className="max-w-4xl grid grid-cols-1 mx-auto gap-3 md:gap-6">
          {contracts?.map((contract: Contract, index: number) => (
            <ContractCard key={contract.id} contract={contract} index={index} />
          ))}
        </div>
      </div>

      {/* Fixed Pagination at Bottom */}
      {totalPages > 1 && (
        <PaginationControls
          totalPages={totalPages}
          totalItems={totalItems}
          currentPage={page}
          itemsPerPage={perPage}
          className="justify-center flex-shrink-0 pt-4 border-t border-gray-200 bg-white sticky bottom-0 px-4 pb-2"
        />
      )}
    </div>
  )
}
