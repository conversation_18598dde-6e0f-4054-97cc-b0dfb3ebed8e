import { FileIcon } from 'lucide-react'
import { getTranslations } from 'next-intl/server'
import Image from 'next/image'
import Link from 'next/link'

import { Card, CardContent } from '@/components/ui/card'

import { Contract } from '../libs/contract-types'

interface ContractCardProps {
  contract: Contract
  index: number
}

export async function ContractCard({ contract, index }: ContractCardProps) {
  const t = await getTranslations()

  // Translate status to German
  const getStatusText = (status: string) => {
    // Based on the Figma design, we're showing "Angebot" for pending contracts
    switch (status) {
      case 'PENDING':
        return 'Angebot'
      case 'ACTIVE':
        return 'Active'
      case 'CONTRACT':
        return 'Active' // Treating CONTRACT status as ACTIVE
      default:
        return t(`CONTRACTS.DETAILS.DETAILS_TAB.STATUS_FIELD.${status}.TEXT`)
    }
  }

  return (
    <Link href={`/contracts/${contract.id}`}>
      <Card
        className={`h-full py-0 md:py-6 border border-[#E5E7EB] overflow-hidden rounded-[20px] shadow-[0px_10px_30px_0px_rgba(0,66,117,0.12)] transition-all duration-200  ${
          contract.status === 'PENDING'
            ? 'focus:border-[#4896DA] hover:border-[#4896DA]'
            : contract.status === 'ACTIVE' || contract.status === 'CONTRACT'
              ? 'focus:border-[#3BCBBF] hover:border-[#3BCBBF]'
              : ''
        }`}
      >
        <CardContent className="p-6">
          <div className="flex items-center h-full">
            <div className="hidden md:flex md:size-[160px] mr-4 flex-shrink-0 overflow-hidden rounded-md bg-[#F9FAFB] relative">
              {contract.provider_img_id ? (
                <Image
                  src={contract.provider_img_id}
                  alt={contract.provider_name}
                  width={160}
                  height={160}
                  className="object-contain size-[160px]"
                  priority={index < 3}
                  sizes="(max-width: 768px) 60px, 160px"
                />
              ) : (
                <div className="w-full h-full flex items-center justify-center">
                  <FileIcon className="size-12 text-[#9CA3AF]" />
                </div>
              )}
            </div>
            <div className="flex flex-col h-full w-full justify-between">
              <h3 className="font-semibold text-base text-[#111827] ">{contract.category_name}</h3>
              <hr className="border-t border-b my-3 border-[#F3F4F6]" />
              <div className="flex items-center">
                <div className=" md:hidden size-[60px] mr-4 flex-shrink-0 overflow-hidden rounded-md bg-[#F9FAFB] relative">
                  {contract.provider_img_id ? (
                    <Image
                      src={contract.provider_img_id}
                      alt={contract.provider_name}
                      fill
                      className="object-contain"
                    />
                  ) : (
                    <div className="w-full h-full flex items-center justify-center">
                      <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path
                          d="M4 16L8.586 11.414C8.96106 11.0391 9.46967 10.8284 10 10.8284C10.5303 10.8284 11.0389 11.0391 11.414 11.414L16 16M14 14L15.586 12.414C15.9611 12.0391 16.4697 11.8284 17 11.8284C17.5303 11.8284 18.0389 12.0391 18.414 12.414L20 14M14 8H14.01M6 20H18C18.5304 20 19.0391 19.7893 19.4142 19.4142C19.7893 19.0391 20 18.5304 20 18V6C20 5.46957 19.7893 4.96086 19.4142 4.58579C19.0391 4.21071 18.5304 4 18 4H6C5.46957 4 4.96086 4.21071 4.58579 4.58579C4.21071 4.96086 4 5.46957 4 6V18C4 18.5304 4.21071 19.0391 4.58579 19.4142C4.96086 19.7893 5.46957 20 6 20Z"
                          stroke="#9CA3AF"
                          strokeWidth="1.5"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                        />
                      </svg>
                    </div>
                  )}
                </div>
                <div className="flex-1 flex flex-col">
                  <h4 className="font-medium text-sm text-[#111827]">{contract.provider_name}</h4>
                  <p className="text-[#6B7280] text-xs mt-1">{t('Insurance number')}</p>
                </div>
              </div>

              <hr className="border-t border-b my-3 border-[#F3F4F6]" />
              <div className="flex justify-between items-center">
                <span
                  className={`text-xs font-medium ${contract.status === 'PENDING' ? 'text-[#4896DA]' : 'text-[#3BCBBF]'}`}
                >
                  {getStatusText(contract.status)}
                </span>
                <span className="text-[#6B7280] text-xs">{contract.insurance_number || '13505ssiok'}</span>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </Link>
  )
}
