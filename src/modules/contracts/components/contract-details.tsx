import { getTranslations } from 'next-intl/server'
import Image from 'next/image'

import { formatDateShort } from '@/lib/format-date-locale'

import { getContractById } from '../api/contracts-api'

interface ContractDetailsProps {
  id: string
}

export async function ContractDetails({ id }: ContractDetailsProps) {
  const t = await getTranslations()
  const contractsResponse = await getContractById(id)

  const contract = contractsResponse?.data

  // Format dates with locale awareness
  const formattedStartDate = contract?.start ? await formatDateShort(new Date(contract.start)) : ''
  const formattedEndDate = contract?.end ? await formatDateShort(new Date(contract.end)) : ''

  return (
    <div className="w-full mt-6 pb-6 max-w-4xl mx-auto shadow-2xl rounded-lg px-6  py-10">
      {/* Provider Info Card */}
      <div className="flex md:flex-row flex-col gap-8">
        {/* Provider Image - larger and centered */}
        {contract?.provider_img_id && (
          <div className=" rounded-xl overflow-hidden border  relative h-fit w-fit mx-auto">
            <Image
              src={contract.provider_img_id}
              alt={contract.provider_name || ''}
              width={248}
              height={248}
              className="object-contain size-[248px]"
              priority
              sizes="(max-width: 768px) 248px, 248px"
            />
          </div>
        )}

        {/* Information sections with separators */}
        <div className="flex-1 flex flex-col gap-6">
          {/* Title at the top */}
          <h2 className="text-xl font-bold text-center md:text-left text-[#142A3A] mb-8">{contract?.provider_name}</h2>
          {/* Insurance Number */}
          <div className="pb-6 border-b border-gray-200">
            <h3 className="text-sm text-gray-500 mb-2">
              {t('CONTRACTS.DETAILS.DETAILS_TAB.INSURANCE_NUMBER_FIELD.LABEL')}
            </h3>
            <p className="text-base font-medium text-[#142A3A]">
              {contract?.insurance_number || '1350539834874368G4398'}
            </p>
          </div>

          {/* Product */}
          <div className="pb-6 border-b border-gray-200">
            <h3 className="text-sm text-gray-500 mb-2">{t('CONTRACTS.DETAILS.DETAILS_TAB.PRODUCT_FIELD.LABEL')}</h3>
            <p className="text-base font-medium text-[#142A3A]">{contract?.product_name || '13505ssiok'}</p>
          </div>

          {/* Sports */}
          <div className="pb-6 border-b border-gray-200">
            <h3 className="text-sm text-gray-500 mb-2">{t('CONTRACTS.DETAILS.DETAILS_TAB.CATEGORY_FIELD.LABEL')}</h3>
            <p className="text-base font-medium text-[#142A3A]">
              {contract?.category_name || 'Reuse - Rücktrittskosten-Versicherung'}
            </p>
          </div>
          {/* Notes */}
          <div className="pb-6 border-b border-gray-200">
            <h3 className="text-sm text-gray-500 mb-2">{t('CONTRACTS.EDIT_FORM.COMMENTS_FIELD.LABEL')}</h3>
            <p className="text-base font-medium text-[#142A3A]">{contract?.info || ''}</p>
          </div>

          {/* Payment and Status row */}
          <div className="pb-6 border-b border-gray-200">
            <div className="grid grid-cols-2 gap-8">
              {/* Payment */}
              <div>
                <h3 className="text-sm text-gray-500 mb-2">{t('CONTRACTS.DETAILS.DETAILS_TAB.COST_FIELD.LABEL')}</h3>
                <p className="text-lg font-bold text-[#142A3A]">
                  {contract?.payment || '55,99'} {contract?.currency || '$'}
                </p>
                {contract?.payment_period && (
                  <p className="text-sm text-gray-500 mt-1">
                    {t(`CONTRACTS.EDIT_FORM.PAYMENT_PERIOD_FIELD.OPTION.${contract.payment_period}`)}
                  </p>
                )}
              </div>

              {/* Status */}
              <div>
                <h3 className="text-sm text-gray-500 mb-2">{t('CONTRACTS.DETAILS.DETAILS_TAB.STATUS_FIELD.LABEL')}</h3>
                <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-[#3BCBBF]/10 text-[#3BCBBF]">
                  {contract?.status && t(contract?.status)}
                </span>
              </div>
            </div>
          </div>

          {/* Valid from date - no border at bottom */}
          <div>
            <p className="text-sm text-gray-500">
              {contract?.start && contract?.end
                ? `${t('CONTRACTS.DETAILS.DETAILS_TAB.VALID_FIELD.VALID_FROM_TEXT')} ${formattedStartDate} ${t('CONTRACTS.DETAILS.DETAILS_TAB.VALID_FIELD.VALID_TO_TEXT')} ${formattedEndDate}`
                : ''}
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}
