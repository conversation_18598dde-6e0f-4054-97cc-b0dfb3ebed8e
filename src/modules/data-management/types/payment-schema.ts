import { z } from 'zod'

export const PaymentMethodTypeEnum = z.enum(['creditCard', 'sepa', 'cash', 'cheque'] as const)
export type PaymentMethodType = z.infer<typeof PaymentMethodTypeEnum>

export const CardTypeEnum = z.enum(['visa', 'mastercard', 'amex', 'discover', 'unknown'] as const)
export type CardType = z.infer<typeof CardTypeEnum>

export const AccountTypeEnum = z.enum(['private', 'business'] as const)
export type AccountType = z.infer<typeof AccountTypeEnum>

export const AccountHolderTypeEnum = z.enum(['private', 'business'] as const)
export type AccountHolderType = z.infer<typeof AccountHolderTypeEnum>

// Payment address schema
export const paymentAddressSchema = z.object({
  firstName: z.string().min(1, 'PAYMENT.VALIDATION.FIRST_NAME_REQUIRED'),
  lastName: z.string().min(1, 'PAYMENT.VALIDATION.LAST_NAME_REQUIRED'),
  street: z.string().min(1, 'PAYMENT.VALIDATION.STREET_REQUIRED'),
  city: z.string().min(1, 'PAYMENT.VALIDATION.CITY_REQUIRED'),
  zipCode: z.string().min(1, 'PAYMENT.VALIDATION.ZIP_CODE_REQUIRED'),
  country: z.string().min(1, 'PAYMENT.VALIDATION.COUNTRY_REQUIRED'),
})

export type PaymentAddressInputs = z.infer<typeof paymentAddressSchema>

// Credit card schema
export const creditCardSchema = z.object({
  id: z.string(),
  type: z.literal('creditCard'),
  cardNumber: z.string()
    .min(1, 'PAYMENT.VALIDATION.CARD_NUMBER_REQUIRED')
    .refine((val) => {
      // Remove all spaces and non-digits
      const cleanNumber = val.replace(/\D/g, '')
      // Check if it's between 13-19 digits (standard card number length)
      return cleanNumber.length >= 13 && cleanNumber.length <= 19
    }, 'PAYMENT.VALIDATION.CARD_NUMBER_INVALID')
    .refine((val) => {
      // Modern Luhn algorithm validation (2024)
      const cleanNumber = val.replace(/\D/g, '')
      if (cleanNumber.length < 13) return false
      
      const arr = cleanNumber.split('').reverse().map(x => parseInt(x))
      const lastDigit = arr.shift() || 0
      let sum = arr.reduce((acc, val, i) => 
        i % 2 !== 0 ? 
          acc + val : 
          acc + ((val *= 2) > 9 ? val - 9 : val), 0
      )
      sum += lastDigit
      return sum % 10 === 0
    }, 'PAYMENT.VALIDATION.CARD_NUMBER_INVALID'),
  cardHolderName: z.string().min(1, 'PAYMENT.VALIDATION.CARD_HOLDER_NAME_REQUIRED'),
  expiryDate: z.string().regex(/^(0[1-9]|1[0-2])\/\d{2}$/, 'PAYMENT.VALIDATION.EXPIRY_DATE_INVALID'),
  cvv: z.string().min(3, 'PAYMENT.VALIDATION.CVV_INVALID').max(4, 'PAYMENT.VALIDATION.CVV_INVALID'),
  cardType: CardTypeEnum,
  useSameAddress: z.boolean(),
  billingAddress: paymentAddressSchema.optional(),
})

export type CreditCardInputs = z.infer<typeof creditCardSchema>
export type CreditCardData = CreditCardInputs

// SEPA schema
export const sepaSchema = z.object({
  id: z.string(),
  type: z.literal('sepa'),
  accountType: AccountTypeEnum,
  accountOwner: z.string().min(1, 'PAYMENT.VALIDATION.ACCOUNT_OWNER_REQUIRED'),
  accountHolderType: AccountHolderTypeEnum,
  iban: z.string().min(15, 'PAYMENT.VALIDATION.IBAN_INVALID').max(34, 'PAYMENT.VALIDATION.IBAN_INVALID'),
  bankName: z.string().min(1, 'PAYMENT.VALIDATION.BANK_NAME_REQUIRED'),
  bic: z.string().min(8, 'PAYMENT.VALIDATION.BIC_INVALID').max(11, 'PAYMENT.VALIDATION.BIC_INVALID'),
  bankNumber: z.string().optional(),
  accountNumber: z.string().optional(),
  last4Numbers: z.string().regex(/^[0-9]{4}$/, 'PAYMENT.VALIDATION.LAST_4_NUMBERS_INVALID').optional(),
})

export type SEPAInputs = z.infer<typeof sepaSchema>
export type SEPAData = SEPAInputs

// Cash schema
export const cashSchema = z.object({
  id: z.string(),
  type: z.literal('cash'),
  notes: z.string().optional(),
})

export type CashInputs = z.infer<typeof cashSchema>
export type CashData = CashInputs

// Cheque schema
export const chequeSchema = z.object({
  id: z.string(),
  type: z.literal('cheque'),
  chequeNumber: z.string().optional(),
  bankName: z.string().optional(),
  notes: z.string().optional(),
})

export type ChequeInputs = z.infer<typeof chequeSchema>
export type ChequeData = ChequeInputs

// Union schema for all payment methods
export const paymentMethodSchema = z.discriminatedUnion('type', [
  creditCardSchema,
  sepaSchema,
  cashSchema,
  chequeSchema,
])

export type PaymentMethodInputs = z.infer<typeof paymentMethodSchema>
export type PaymentMethod = PaymentMethodInputs
export type PaymentAddress = PaymentAddressInputs

// Main payment data schema
export const paymentDataSchema = z.object({
  paymentMethods: z.array(paymentMethodSchema),
  lastUpdate: z.string().optional(),
})

export type PaymentDataInputs = z.infer<typeof paymentDataSchema>

// Options for dropdowns
export const paymentMethodTypeOptions = [
  { value: 'creditCard' as const, label: 'PAYMENT.EDIT.PAYMENT_TYPE.CREDIT_CARD' },
  { value: 'sepa' as const, label: 'PAYMENT.EDIT.PAYMENT_TYPE.SEPA' },
  { value: 'cash' as const, label: 'PAYMENT.EDIT.PAYMENT_TYPE.CASH' },
  { value: 'cheque' as const, label: 'PAYMENT.EDIT.PAYMENT_TYPE.CHEQUE' },
]

export const accountTypeOptions = [
  { value: 'private' as const, label: 'PAYMENT.EDIT.ACCOUNT_TYPE.PRIVATE' },
  { value: 'business' as const, label: 'PAYMENT.EDIT.ACCOUNT_TYPE.BUSINESS' },
]

export const accountHolderTypeOptions = [
  { value: 'private' as const, label: 'PAYMENT.EDIT.ACCOUNT_HOLDER_TYPE.PRIVATE' },
  { value: 'business' as const, label: 'PAYMENT.EDIT.ACCOUNT_HOLDER_TYPE.BUSINESS' },
]