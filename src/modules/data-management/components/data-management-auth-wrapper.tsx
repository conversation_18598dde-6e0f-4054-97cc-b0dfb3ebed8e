'use client'

import { useEffect } from 'react'

import { loginWithToken } from '@/modules/auth/actions/auth-actions'
import { AuthSession } from '@/modules/auth/types/auth-types'
import { useAction } from 'next-safe-action/hooks'
import { useRouter } from 'next/navigation'

interface UpdateClientSearchParams {
  token: string
  session: AuthSession | null
  children: React.ReactNode
}

export const DataManagementAuthWrapper = ({ token, children, session }: UpdateClientSearchParams) => {
  const router = useRouter()
  const { executeAsync, isPending, isExecuting, hasErrored } = useAction(loginWithToken, {
    onSuccess: () => {
      // router.push('/data-management/form')
    },
    onError: (error) => {
      console.error('Error updating client:', error)
      router.push('/login')
    },
  })

  useEffect(() => {
    const updateClient = async () => {
      if (!token) {
        console.error('Token is required for client update')
        return
      }
      try {
        await executeAsync({ token })
      } catch (error) {
        console.error('Error updating client:', error)
      }
    }
    updateClient()
  }, [token, executeAsync])

  if (isPending || isExecuting) {
    return <div>Loading...</div>
  }

  if (hasErrored) {
    return <div>Error updating client. Please try again.</div>
  }

  if (!session) {
    return <div>Session expired. Please log in again.</div>
  }

  return <>{children}</>
}
