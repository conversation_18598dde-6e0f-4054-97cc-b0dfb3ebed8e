'use client'

import { UserProfile } from '@/modules/auth/types/auth-types'
import { Product, Provider } from '@/modules/contracts/libs/contract-types'

import { Card, CardContent } from '@/components/ui/card'
import { SelectOption } from '@/components/form-inputs'

import { ClientDataManagement } from '../types/data-management-types'
import { DataManagementStepToRender } from './data-management-step-to-render'
import { DataManagementStepper } from './data-management-stepper'

export interface DataManagementFormProps {
  userProfile: UserProfile
  clientDataManagement: ClientDataManagement
  providers: Provider[]
  products: Product[]
  categoryOptions: SelectOption[]
}

export function DataManagementForm({
  userProfile,
  clientDataManagement,
  providers,
  products,
  categoryOptions,
}: DataManagementFormProps) {
  return (
    <div className="space-y-6">
      <Card>
        <CardContent className="space-y-6">
          <DataManagementStepper navigationState={clientDataManagement.navigationState} />

          <DataManagementStepToRender
            userProfile={userProfile}
            clientDataManagement={clientDataManagement}
            providers={providers}
            products={products}
            categoryOptions={categoryOptions}
          />
        </CardContent>
      </Card>
    </div>
  )
}
