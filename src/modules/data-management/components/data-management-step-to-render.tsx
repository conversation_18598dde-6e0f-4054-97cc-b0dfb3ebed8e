'use client'

import { UserProfile } from '@/modules/auth/types/auth-types'
import { Product, Provider } from '@/modules/contracts/libs/contract-types'

import { SelectOption } from '@/components/form-inputs'

import { ClientDataManagement } from '../types/data-management-types'
import { ClientInformationStep } from './steps/client-information/client-information-step'
import { ContactDataStep } from './steps/contact-data/contact-data-step'
import { ContractsStep } from './steps/contracts/contracts-step'
import { DefaultAddressStep } from './steps/default-address/default-address-step'
import { HealthAndRisksStep } from './steps/health-and-risks/health-and-risks-step'
import { LegitimationStep } from './steps/legitimation/legitimation-form-step'
import { PaymentForm } from './steps/payment/payment-form'
import { ProfessionStep } from './steps/profession/profession-step'

interface DataManagementStepToRenderProps {
  userProfile: UserProfile
  clientDataManagement: ClientDataManagement
  providers: Provider[]
  products: Product[]
  categoryOptions: SelectOption[]
}

export const DataManagementStepToRender = ({
  userProfile,
  clientDataManagement,
  providers,
  products,
  categoryOptions,
}: DataManagementStepToRenderProps) => {
  const currentStepName = clientDataManagement?.navigationState?.currentStep

  switch (currentStepName) {
    case 'clientInformation':
      return <ClientInformationStep clientDataManagement={clientDataManagement} />

    case 'legitimation':
      return <LegitimationStep clientDataManagement={clientDataManagement} />

    case 'defaultAddress':
      return <DefaultAddressStep clientDataManagement={clientDataManagement} />

    case 'contactData':
      return <ContactDataStep clientDataManagement={clientDataManagement} />

    case 'profession':
      return <ProfessionStep clientDataManagement={clientDataManagement} />

    case 'healthAndRisks':
      return <HealthAndRisksStep clientDataManagement={clientDataManagement} />

    case 'payment':
      return <PaymentForm userProfile={userProfile} clientDataManagement={clientDataManagement} />

    case 'contracts':
      return (
        <ContractsStep
          clientDataManagement={clientDataManagement}
          providers={providers}
          products={products}
          categoryOptions={categoryOptions}
        />
      )

    default:
      return <div>Step not implemented yet: {currentStepName}</div>
  }
}
