'use client'

import React, { useState, useEffect, forwardRef } from 'react'
import { cn } from '@/lib/utils'
import { Check, X, Building2 } from 'lucide-react'

interface EnhancedIBANInputProps extends Omit<React.InputHTMLAttributes<HTMLInputElement>, 'onChange'> {
  value?: string
  onChange?: (value: string, isValid: boolean) => void
  label?: string
  error?: string
  placeholder?: string
}

// Simple IBAN validation (basic format check)
const validateIBAN = (iban: string): boolean => {
  const cleanIBAN = iban.replace(/\s/g, '').toUpperCase()
  
  // Basic length check (15-34 characters)
  if (cleanIBAN.length < 15 || cleanIBAN.length > 34) {
    return false
  }
  
  // Check if it starts with 2 letters followed by 2 digits
  const ibanRegex = /^[A-Z]{2}[0-9]{2}[A-Z0-9]+$/
  return ibanRegex.test(cleanIBAN)
}

// Format IBAN with spaces every 4 characters
const formatIBAN = (iban: string): string => {
  const cleanIBAN = iban.replace(/\s/g, '').toUpperCase()
  return cleanIBAN.replace(/(.{4})/g, '$1 ').trim()
}

// Get country name from IBAN country code
const getCountryFromIBAN = (iban: string): string | null => {
  const countryCode = iban.replace(/\s/g, '').slice(0, 2).toUpperCase()
  
  const countries: Record<string, string> = {
    'DE': 'Germany',
    'AT': 'Austria', 
    'CH': 'Switzerland',
    'FR': 'France',
    'IT': 'Italy',
    'ES': 'Spain',
    'NL': 'Netherlands',
    'BE': 'Belgium',
    'LU': 'Luxembourg',
    'PT': 'Portugal',
    'FI': 'Finland',
    'IE': 'Ireland',
    'GR': 'Greece',
    'SI': 'Slovenia',
    'SK': 'Slovakia',
    'EE': 'Estonia',
    'LV': 'Latvia',
    'LT': 'Lithuania',
    'CY': 'Cyprus',
    'MT': 'Malta',
    'PL': 'Poland',
    'CZ': 'Czech Republic',
    'HU': 'Hungary',
    'RO': 'Romania',
    'BG': 'Bulgaria',
    'HR': 'Croatia'
  }
  
  return countries[countryCode] || null
}

export const EnhancedIBANInput = forwardRef<HTMLInputElement, EnhancedIBANInputProps>(
  ({ 
    value = '', 
    onChange, 
    label, 
    error, 
    placeholder = 'DE89 3704 0044 0532 0130 00',
    className,
    disabled,
    ...props 
  }, ref) => {
    const [formattedValue, setFormattedValue] = useState('')
    const [isValid, setIsValid] = useState(false)
    const [country, setCountry] = useState<string | null>(null)

    useEffect(() => {
      if (value) {
        const formatted = formatIBAN(value)
        const valid = validateIBAN(value)
        const detectedCountry = getCountryFromIBAN(value)
        
        setFormattedValue(formatted)
        setIsValid(valid)
        setCountry(detectedCountry)
      } else {
        setFormattedValue('')
        setIsValid(false)
        setCountry(null)
      }
    }, [value])

    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      const inputValue = e.target.value
      const cleanValue = inputValue.replace(/[^A-Z0-9]/gi, '').toUpperCase()
      
      if (cleanValue.length <= 34) { // Maximum IBAN length
        const formatted = formatIBAN(cleanValue)
        const valid = validateIBAN(cleanValue)
        const detectedCountry = getCountryFromIBAN(cleanValue)
        
        setFormattedValue(formatted)
        setIsValid(valid)
        setCountry(detectedCountry)
        
        onChange?.(cleanValue, valid)
      }
    }

    const getValidationIcon = () => {
      if (!formattedValue) {
        return <Building2 className="w-4 h-4 text-gray-400" />
      }
      
      if (isValid) {
        return <Check className="w-4 h-4 text-green-500" />
      }
      
      if (formattedValue.length >= 15) {
        return <X className="w-4 h-4 text-red-500" />
      }
      
      return <Building2 className="w-4 h-4 text-gray-400" />
    }

    return (
      <div className="space-y-1">
        {label && (
          <label className="text-sm font-medium text-gray-700">
            {label}
          </label>
        )}
        <div className="relative">
          <input
            ref={ref}
            type="text"
            value={formattedValue}
            onChange={handleInputChange}
            placeholder={placeholder}
            disabled={disabled}
            className={cn(
              'flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50',
              'pr-10 font-mono', // Make room for validation icon and use monospace font
              error && 'border-red-500 focus-visible:ring-red-500',
              isValid && formattedValue && 'border-green-500',
              className
            )}
            {...props}
          />
          
          {/* Validation Icon */}
          <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
            {getValidationIcon()}
          </div>
        </div>
        
        {/* Country indicator */}
        {country && !error && (
          <div className="flex items-center gap-1 text-xs text-gray-600">
            <span className="w-2 h-2 rounded-full bg-blue-500"></span>
            <span>{country}</span>
          </div>
        )}
        
        {error && (
          <div className="flex items-center gap-1 mt-1">
            <X className="w-4 h-4 text-red-500 flex-shrink-0" />
            <p className="text-sm text-red-600 font-medium">{error}</p>
          </div>
        )}
        
        {/* Helper text */}
        {!error && !country && (
          <p className="text-xs text-gray-500">
            Enter your International Bank Account Number
          </p>
        )}
      </div>
    )
  }
)

EnhancedIBANInput.displayName = 'EnhancedIBANInput'