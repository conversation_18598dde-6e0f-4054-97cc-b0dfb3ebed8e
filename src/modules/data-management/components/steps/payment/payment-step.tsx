'use client'

import { ClientDataManagement } from '../../../types/data-management-types'
import { UserProfile } from '@/modules/auth/types/auth-types'
import { PaymentForm } from './payment-form'

interface PaymentStepProps {
  userProfile: UserProfile
  clientDataManagement: ClientDataManagement
}

export const PaymentStep = ({ userProfile, clientDataManagement }: PaymentStepProps) => {
  return <PaymentForm userProfile={userProfile} clientDataManagement={clientDataManagement} />
}