'use client'

import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { useTranslations } from 'next-intl'

import { Card, CardContent, CardForm, CardHeader, CardTitle } from '@/components/ui/card'
import { Form } from '@/components/ui/form'
import { FormInput, FormSelect } from '@/components/form-inputs'
import { Button } from '@/components/ui/button'

import { SEPAInputs, sepaSchema, accountTypeOptions, accountHolderTypeOptions } from '../../../types/payment-schema'
import { EnhancedIBANInput } from './enhanced-iban-input'

interface SEPAFormProps {
  onSubmit: (data: SEPAInputs) => void
  onCancel: () => void
  initialData?: Partial<SEPAInputs>
}

export function SEPAForm({ onSubmit, onCancel, initialData }: SEPAFormProps) {
  const t = useTranslations()

  const form = useForm<SEPAInputs>({
    resolver: zodResolver(sepaSchema),
    defaultValues: {
      id: initialData?.id || '',
      type: 'sepa',
      accountType: initialData?.accountType || 'private',
      accountOwner: initialData?.accountOwner || '',
      accountHolderType: initialData?.accountHolderType || 'private',
      iban: initialData?.iban || '',
      bankName: initialData?.bankName || '',
      bic: initialData?.bic || '',
      bankNumber: initialData?.bankNumber || '',
      accountNumber: initialData?.accountNumber || '',
      last4Numbers: initialData?.last4Numbers || '',
    },
  })

  const handleFormSubmit = (data: SEPAInputs) => {
    // Generate ID if not provided
    const finalData = {
      ...data,
      id: data.id || `sepa-${Date.now()}`,
    }
    onSubmit(finalData)
  }

  const handleIBANChange = (value: string) => {
    form.setValue('iban', value)
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleFormSubmit)} className="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle>{t('PAYMENT.EDIT.SEPA.TITLE')}</CardTitle>
          </CardHeader>
          <CardContent>
            <CardForm>
              <FormSelect<SEPAInputs>
                name="accountType"
                label={t('PAYMENT.EDIT.ACCOUNT_TYPE_FIELD.LABEL')}
                options={accountTypeOptions}
              />

              <FormInput<SEPAInputs>
                name="accountOwner"
                label={t('PAYMENT.EDIT.ACCOUNT_OWNER_FIELD.LABEL')}
                placeholder={t('PAYMENT.EDIT.ACCOUNT_OWNER_FIELD.PLACEHOLDER')}
              />

              <FormSelect<SEPAInputs>
                name="accountHolderType"
                label={t('PAYMENT.EDIT.ACCOUNT_HOLDER_TYPE_FIELD.LABEL')}
                options={accountHolderTypeOptions}
              />

              <div className="md:col-span-2">
                <EnhancedIBANInput
                  label={t('PAYMENT.EDIT.IBAN_FIELD.LABEL')}
                  value={form.watch('iban')}
                  onChange={handleIBANChange}
                  placeholder="DE89 3704 0044 0532 0130 00"
                  error={form.formState.errors.iban?.message}
                />
              </div>

              <div className="md:col-span-2">
                <FormInput<SEPAInputs>
                  name="bankName"
                  label={t('PAYMENT.EDIT.BANK_NAME_FIELD.LABEL')}
                  placeholder={t('PAYMENT.EDIT.BANK_NAME_FIELD.PLACEHOLDER')}
                />
              </div>

              <FormInput<SEPAInputs>
                name="bic"
                label={t('PAYMENT.EDIT.BIC_FIELD.LABEL')}
                placeholder="DEUTDEFF"
              />

              <FormInput<SEPAInputs>
                name="bankNumber"
                label={t('PAYMENT.EDIT.BANK_NUMBER_FIELD.LABEL')}
                placeholder="********"
              />

              <FormInput<SEPAInputs>
                name="accountNumber"
                label={t('PAYMENT.EDIT.ACCOUNT_NUMBER_FIELD.LABEL')}
                placeholder="*********"
              />

              <FormInput<SEPAInputs>
                name="last4Numbers"
                label={t('PAYMENT.EDIT.LAST_4_NUMBERS_FIELD.LABEL')}
                placeholder="3000"
                maxLength={4}
              />
            </CardForm>
          </CardContent>
        </Card>

        {/* Form Actions */}
        <div className="flex gap-3 justify-end">
          <Button type="button" variant="outline" onClick={onCancel}>
            {t('PAYMENT.EDIT.ACTIONS.CANCEL')}
          </Button>
          <Button type="submit">
            {t('PAYMENT.EDIT.ACTIONS.SAVE')}
          </Button>
        </div>
      </form>
    </Form>
  )
}