'use client'

import { useEffect, useState } from 'react'

import { UserProfile } from '@/modules/auth/types/auth-types'
import { zodResolver } from '@hookform/resolvers/zod'
import { useTranslations } from 'next-intl'
import { useForm } from 'react-hook-form'

import { Button } from '@/components/ui/button'
import { Card, CardContent, CardForm, CardHeader, CardTitle } from '@/components/ui/card'
import { Form } from '@/components/ui/form'
import { FormCardInput, FormCheckbox, FormCVVInput, FormExpiryInput, FormInput } from '@/components/form-inputs'
import { FormCountrySelect } from '@/components/form-inputs/form-country-select'

import { CardType, CreditCardInputs, creditCardSchema, PaymentAddressInputs } from '../../../types/payment-schema'
import { getCardTypeDisplayName } from './card-detection.utils'
import { CardTypeIcon } from './card-type-icon'

interface CreditCardFormProps {
  userProfile: UserProfile
  billingAddress: PaymentAddressInputs
  onSubmit: (data: CreditCardInputs) => void
  onCancel: () => void
  initialData?: Partial<CreditCardInputs>
}

export function CreditCardForm({ userProfile, billingAddress, onSubmit, onCancel, initialData }: CreditCardFormProps) {
  const t = useTranslations()
  const [detectedCardType, setDetectedCardType] = useState(initialData?.cardType || 'unknown')

  const form = useForm<CreditCardInputs>({
    mode: 'onChange',
    shouldFocusError: true,
    resolver: zodResolver(creditCardSchema),
    defaultValues: {
      id: initialData?.id || '',
      type: 'creditCard',
      cardNumber: initialData?.cardNumber || '',
      cardHolderName: initialData?.cardHolderName || '',
      expiryDate: initialData?.expiryDate || '',
      cvv: initialData?.cvv || '',
      cardType: initialData?.cardType || 'unknown',
      useSameAddress: initialData?.useSameAddress ?? true,
      billingAddress: initialData?.billingAddress || {
        firstName: '',
        lastName: '',
        street: '',
        city: '',
        zipCode: '',
        country: '',
      },
    },
  })

  const { watch, setValue } = form
  const useSameAddress = watch('useSameAddress')

  // Auto-fill billing address when useSameAddress changes
  useEffect(() => {
    if (useSameAddress && billingAddress) {
      // Populate billing address from main billing form
      setValue('billingAddress.firstName', billingAddress.firstName || '')
      setValue('billingAddress.lastName', billingAddress.lastName || '')
      setValue('billingAddress.street', billingAddress.street || '')
      setValue('billingAddress.city', billingAddress.city || '')
      setValue('billingAddress.zipCode', billingAddress.zipCode || '')
      setValue('billingAddress.country', billingAddress.country || '')
    }
  }, [useSameAddress, billingAddress, setValue])

  const handleCardTypeChange = (cardType: CardType) => {
    setDetectedCardType(cardType)
    setValue('cardType', cardType)
  }

  const handleFormSubmit = (data: CreditCardInputs) => {
    // Generate ID if not provided
    const finalData = {
      ...data,
      id: data.id || `card-${Date.now()}`,
    }
    onSubmit(finalData)
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleFormSubmit)} className="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-3">
              {t('PAYMENT.EDIT.CREDIT_CARD.TITLE')}
              {detectedCardType !== 'unknown' && (
                <div className="flex items-center gap-2">
                  <CardTypeIcon cardType={detectedCardType} className="w-10 h-6" />
                  <span className="text-sm font-normal text-gray-600">{getCardTypeDisplayName(detectedCardType)}</span>
                </div>
              )}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <CardForm>
              {/* Card Number */}
              <div className="md:col-span-2">
                <FormCardInput<CreditCardInputs>
                  name="cardNumber"
                  label="PAYMENT.EDIT.CREDIT_CARD.CARD_NUMBER"
                  placeholder="1234 5678 9012 3456"
                  inputType="cardNumber"
                  onCardTypeChange={handleCardTypeChange}
                  isRequired
                />
              </div>

              {/* Card Holder Name */}
              <div className="md:col-span-2">
                <FormInput<CreditCardInputs>
                  name="cardHolderName"
                  label={t('PAYMENT.EDIT.CREDIT_CARD.CARD_HOLDER_NAME')}
                  placeholder="John Doe"
                />
              </div>

              {/* Expiry Date */}
              <FormExpiryInput<CreditCardInputs>
                name="expiryDate"
                label="PAYMENT.EDIT.CREDIT_CARD.EXPIRY_DATE"
                placeholder="MM/YY"
                description="PAYMENT.EDIT.CREDIT_CARD.EXPIRY_DATE_DESCRIPTION"
                isRequired
              />

              {/* CVV */}
              <FormCVVInput<CreditCardInputs>
                name="cvv"
                label="PAYMENT.EDIT.CREDIT_CARD.CVV"
                cardType={detectedCardType}
                description="PAYMENT.EDIT.CREDIT_CARD.CVV_DESCRIPTION"
                isRequired
              />
            </CardForm>
          </CardContent>
        </Card>

        {/* Billing Address */}
        <Card>
          <CardHeader>
            <CardTitle>{t('PAYMENT.EDIT.CREDIT_CARD.BILLING_ADDRESS')}</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <FormCheckbox<CreditCardInputs>
                name="useSameAddress"
                label={t('PAYMENT.EDIT.CREDIT_CARD.USE_SAME_ADDRESS')}
              />

              <CardForm>
                <FormInput<CreditCardInputs>
                  name="billingAddress.firstName"
                  label={t('PAYMENT.EDIT.BILLING_ADDRESS.FIRST_NAME')}
                  disabled={useSameAddress}
                />

                <FormInput<CreditCardInputs>
                  name="billingAddress.lastName"
                  label={t('PAYMENT.EDIT.BILLING_ADDRESS.LAST_NAME')}
                  disabled={useSameAddress}
                />

                <div className="md:col-span-2">
                  <FormInput<CreditCardInputs>
                    name="billingAddress.street"
                    label={t('PAYMENT.EDIT.BILLING_ADDRESS.STREET')}
                    disabled={useSameAddress}
                  />
                </div>

                <FormInput<CreditCardInputs>
                  name="billingAddress.city"
                  label={t('PAYMENT.EDIT.BILLING_ADDRESS.CITY')}
                  disabled={useSameAddress}
                />

                <FormInput<CreditCardInputs>
                  name="billingAddress.zipCode"
                  label={t('PAYMENT.EDIT.BILLING_ADDRESS.ZIP_CODE')}
                  disabled={useSameAddress}
                />

                <div className="md:col-span-2">
                  <FormCountrySelect<CreditCardInputs>
                    name="billingAddress.country"
                    label={t('PAYMENT.EDIT.BILLING_ADDRESS.COUNTRY')}
                    disabled={useSameAddress}
                  />
                </div>
              </CardForm>
            </div>
          </CardContent>
        </Card>

        {/* Form Actions */}
        <div className="flex gap-3 justify-end">
          <Button type="button" variant="outline" onClick={onCancel}>
            {t('PAYMENT.EDIT.ACTIONS.CANCEL')}
          </Button>
          <Button type="submit">{t('PAYMENT.EDIT.ACTIONS.SAVE')}</Button>
        </div>
      </form>
    </Form>
  )
}
