import React from 'react'
import Image from 'next/image'
import { CardType } from '../../../types/payment-schema'

interface CardTypeIconProps {
  cardType: CardType
  className?: string
}

export const CardTypeIcon = ({ cardType, className = 'w-8 h-5' }: CardTypeIconProps) => {
  switch (cardType) {
    case 'visa':
      return (
        <Image
          src="/icons/visa.svg"
          alt="Visa"
          width={32}
          height={20}
          className={className}
        />
      )
    case 'mastercard':
      return (
        <Image
          src="/icons/mastercard.svg"
          alt="MasterCard"
          width={32}
          height={20}
          className={className}
        />
      )
    case 'amex':
      return (
        <Image
          src="/icons/amex.svg"
          alt="American Express"
          width={32}
          height={20}
          className={className}
        />
      )
    case 'discover':
      return (
        <Image
          src="/icons/discover.svg"
          alt="Discover"
          width={32}
          height={20}
          className={className}
        />
      )
    case 'unknown':
    default:
      return (
        <div className={`${className} flex items-center justify-center bg-gray-100 border border-gray-300 rounded`}>
          <svg viewBox="0 0 24 16" fill="none" className="w-6 h-4">
            <rect width="24" height="16" rx="2" fill="#9CA3AF"/>
            <rect x="2" y="4" width="20" height="1.5" rx="0.75" fill="white"/>
            <rect x="2" y="7" width="12" height="1" rx="0.5" fill="white"/>
            <rect x="2" y="9" width="8" height="1" rx="0.5" fill="white"/>
            <rect x="2" y="11" width="14" height="1" rx="0.5" fill="white"/>
          </svg>
        </div>
      )
  }
}

interface CardTypeIndicatorProps {
  cardType: CardType
  isValid?: boolean
}

export const CardTypeIndicator = ({ cardType, isValid = false }: CardTypeIndicatorProps) => {
  return (
    <div className="flex items-center gap-2">
      <CardTypeIcon cardType={cardType} />
      {cardType !== 'unknown' && (
        <div className={`w-2 h-2 rounded-full ${isValid ? 'bg-green-500' : 'bg-gray-300'}`} />
      )}
    </div>
  )
}