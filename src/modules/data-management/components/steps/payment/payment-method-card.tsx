import { Banknote, Building2, Credit<PERSON>ard, FileText, Trash2 } from 'lucide-react'

import { cn } from '@/lib/utils'
import { Button } from '@/components/ui/button'

import { PaymentMethod } from '../../../types/data-management-types'
import { getCardTypeDisplayName } from './card-detection.utils'
import { CardTypeIcon } from './card-type-icon'

interface PaymentMethodCardProps {
  paymentMethod: PaymentMethod
  active?: boolean
  onSelect?: () => void
  onDelete?: () => void
}

export function PaymentMethodCard({ paymentMethod, active, onSelect, onDelete }: PaymentMethodCardProps) {
  const getIcon = () => {
    switch (paymentMethod.type) {
      case 'creditCard':
        return paymentMethod.cardType !== 'unknown' ? (
          <CardTypeIcon cardType={paymentMethod.cardType} className="w-8 h-5" />
        ) : (
          <CreditCard className="w-5 h-5" />
        )
      case 'sepa':
        return <Building2 className="w-5 h-5" />
      case 'cash':
        return <Banknote className="w-5 h-5" />
      case 'cheque':
        return <FileText className="w-5 h-5" />
      default:
        return <CreditCard className="w-5 h-5" />
    }
  }

  const getTitle = () => {
    switch (paymentMethod.type) {
      case 'creditCard':
        return `•••• •••• •••• ${paymentMethod.cardNumber?.slice(-4)}`
      case 'sepa':
        return `${paymentMethod.bankName || 'Bank Account'}`
      case 'cash':
        return 'Cash Payment'
      case 'cheque':
        return 'Cheque Payment'
      default:
        return 'Payment Method'
    }
  }

  const getSubtitle = () => {
    switch (paymentMethod.type) {
      case 'creditCard':
        return `${paymentMethod.cardHolderName}`
      case 'sepa':
        return `${paymentMethod.accountOwner}`
      case 'cash':
        return 'Cash payment'
      case 'cheque':
        return 'Cheque payment'
      default:
        return ''
    }
  }

  const getCardType = () => {
    if (paymentMethod.type === 'creditCard') {
      return getCardTypeDisplayName(paymentMethod.cardType)
    }
    return null
  }

  const getExpiryDate = () => {
    if (paymentMethod.type === 'creditCard') {
      return paymentMethod.expiryDate
    }
    return null
  }

  return (
    <div
      className={cn(
        'relative flex flex-col border rounded-xl p-2 md:p-4 transition-all duration-150 shadow-sm cursor-pointer focus:outline-none focus:ring-2 focus:ring-primary/50  min-h-[100px] w-full',
        {
          'border-primary bg-gradient-to-br from-primary/5 to-primary/10 shadow-md ring-1 ring-primary/20': active,
          'border-gray-200 hover:bg-gradient-to-br hover:from-gray-50 hover:to-gray-100 hover:border-gray-300': !active,
        }
      )}
      onClick={onSelect}
      role="button"
      tabIndex={0}
      aria-pressed={active}
    >
      {/* Header with icon and delete button */}
      <div className="flex items-start justify-between mb-1">
        <div className="flex items-center gap-2">
          <div className="flex items-center justify-center">{getIcon()}</div>
          {getCardType() && <span className="text-xs font-medium text-gray-600 uppercase">{getCardType()}</span>}
        </div>

        {onDelete && (
          <Button
            variant="ghost"
            size="sm"
            onClick={(e) => {
              e.stopPropagation()
              onDelete()
            }}
            className="h-5 w-5 p-0 hover:bg-red-50 hover:text-red-600"
          >
            <Trash2 className="w-2.5 h-2.5" />
          </Button>
        )}
      </div>

      {/* Card number */}
      <div className="mb-1 flex-1">
        <span className="text-sm font-mono text-gray-800 font-medium tracking-wider">{getTitle()}</span>
      </div>

      {/* Card holder name and expiry */}
      <div className="flex flex-col gap-0.5 mt-auto">
        <span className="text-xs text-gray-800 truncate font-bold uppercase tracking-wide">{getSubtitle()}</span>
        {getExpiryDate() && <span className="text-xs text-gray-500 font-mono">{getExpiryDate()}</span>}
      </div>
    </div>
  )
}
