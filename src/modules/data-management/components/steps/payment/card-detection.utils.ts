export type CardType = 'visa' | 'mastercard' | 'amex' | 'discover' | 'unknown'

/**
 * Detects credit card type based on card number
 * @param cardNumber - The credit card number (with or without spaces/dashes)
 * @returns CardType
 */
export const detectCardType = (cardNumber: string): CardType => {
  // Remove all non-digit characters
  const cleanNumber = cardNumber.replace(/\D/g, '')
  
  // Visa: starts with 4
  if (/^4/.test(cleanNumber)) {
    return 'visa'
  }
  
  // MasterCard: starts with 5 or 2221-2720
  if (/^5[1-5]/.test(cleanNumber) || /^2(22[1-9]|2[3-9]|[3-6]|7[0-1]|720)/.test(cleanNumber)) {
    return 'mastercard'
  }
  
  // American Express: starts with 34 or 37
  if (/^3[47]/.test(cleanNumber)) {
    return 'amex'
  }
  
  // Discover: starts with 6011, 622126-622925, 644-649, 65
  if (/^6011|^622(12[6-9]|1[3-9]|[2-8]|9[01]|92[0-5])|^64[4-9]|^65/.test(cleanNumber)) {
    return 'discover'
  }
  
  return 'unknown'
}

/**
 * Validates credit card number using Luhn algorithm
 * @param cardNumber - The credit card number
 * @returns boolean
 */
export const validateCardNumber = (cardNumber: string): boolean => {
  const cleanNumber = cardNumber.replace(/\D/g, '')
  
  if (cleanNumber.length < 13 || cleanNumber.length > 19) {
    return false
  }
  
  let sum = 0
  let shouldDouble = false
  
  // Loop through digits from right to left
  for (let i = cleanNumber.length - 1; i >= 0; i--) {
    let digit = parseInt(cleanNumber[i])
    
    if (shouldDouble) {
      digit *= 2
      if (digit > 9) {
        digit -= 9
      }
    }
    
    sum += digit
    shouldDouble = !shouldDouble
  }
  
  return sum % 10 === 0
}

/**
 * Formats credit card number with spaces
 * @param cardNumber - The credit card number
 * @returns Formatted card number
 */
export const formatCardNumber = (cardNumber: string): string => {
  const cleanNumber = cardNumber.replace(/\D/g, '')
  const cardType = detectCardType(cleanNumber)
  
  switch (cardType) {
    case 'amex':
      // Format: 1234 567890 12345
      return cleanNumber.replace(/(\d{4})(\d{6})(\d{5})/, '$1 $2 $3')
    case 'visa':
    case 'mastercard':
    case 'discover':
    default:
      // Format: 1234 5678 9012 3456
      return cleanNumber.replace(/(\d{4})(?=\d)/g, '$1 ')
  }
}

/**
 * Validates expiry date format (MM/YY)
 * @param expiryDate - The expiry date string
 * @returns boolean
 */
export const validateExpiryDate = (expiryDate: string): boolean => {
  const regex = /^(0[1-9]|1[0-2])\/\d{2}$/
  
  if (!regex.test(expiryDate)) {
    return false
  }
  
  const [month, year] = expiryDate.split('/')
  const currentDate = new Date()
  const currentYear = currentDate.getFullYear() % 100
  const currentMonth = currentDate.getMonth() + 1
  
  const expYear = parseInt(year, 10)
  const expMonth = parseInt(month, 10)
  
  // Check if the card is expired
  if (expYear < currentYear || (expYear === currentYear && expMonth < currentMonth)) {
    return false
  }
  
  return true
}

/**
 * Formats expiry date input (MM/YY)
 * @param input - The input string
 * @returns Formatted expiry date
 */
export const formatExpiryDate = (input: string): string => {
  const cleanInput = input.replace(/\D/g, '')
  
  if (cleanInput.length <= 2) {
    return cleanInput
  }
  
  return `${cleanInput.slice(0, 2)}/${cleanInput.slice(2, 4)}`
}

/**
 * Gets card type display name
 * @param cardType - The card type
 * @returns Display name
 */
export const getCardTypeDisplayName = (cardType: CardType): string => {
  switch (cardType) {
    case 'visa':
      return 'Visa'
    case 'mastercard':
      return 'MasterCard'
    case 'amex':
      return 'American Express'
    case 'discover':
      return 'Discover'
    default:
      return 'Unknown'
  }
}

/**
 * Masks credit card number for display
 * @param cardNumber - The credit card number
 * @returns Masked card number
 */
export const maskCardNumber = (cardNumber: string): string => {
  const cleanNumber = cardNumber.replace(/\D/g, '')
  
  if (cleanNumber.length < 4) {
    return cleanNumber
  }
  
  const last4 = cleanNumber.slice(-4)
  const masked = '*'.repeat(cleanNumber.length - 4)
  
  return formatCardNumber(masked + last4)
}