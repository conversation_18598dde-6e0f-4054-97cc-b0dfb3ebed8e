'use client'

import { useEffect, useState } from 'react'

import { UserProfile } from '@/modules/auth/types/auth-types'
import { zodResolver } from '@hookform/resolvers/zod'
import { CreditCard, PlusIcon } from 'lucide-react'
import { useTranslations } from 'next-intl'
import { useForm } from 'react-hook-form'

import { Button } from '@/components/ui/button'
import { Card, CardContent, CardForm, CardHeader, CardTitle } from '@/components/ui/card'
import { Form } from '@/components/ui/form'
import { FormInput } from '@/components/form-inputs'
import { FormCountrySelect } from '@/components/form-inputs/form-country-select'

import { useDataManagement } from '../../../hooks/use-data-management'
import { ClientDataManagement, PaymentMethod } from '../../../types/data-management-types'
import {
  PaymentAddressInputs,
  paymentAddressSchema,
  PaymentMethodType,
  paymentMethodTypeOptions,
} from '../../../types/payment-schema'
import { DataManagementNavigation } from '../../data-management-navigation'
import { CreditCardForm } from './credit-card-form'
import { PaymentMethodCard } from './payment-method-card'
import { SEPAForm } from './sepa-form'

interface PaymentFormProps {
  userProfile: UserProfile
  clientDataManagement: ClientDataManagement
}

export const PaymentForm = ({ userProfile, clientDataManagement }: PaymentFormProps) => {
  const t = useTranslations()
  const { handleFormSubmission, isSaving, isFirstStep, goToPreviousStep, isLastStep } =
    useDataManagement(clientDataManagement)

  // Get existing payment data if available
  const existingPayment = clientDataManagement?.steps?.payment

  const [paymentMethods, setPaymentMethods] = useState<PaymentMethod[]>(existingPayment?.paymentMethods || [])

  // Update paymentMethods when clientDataManagement changes
  useEffect(() => {
    const updatedPayment = clientDataManagement?.steps?.payment
    if (updatedPayment?.paymentMethods) {
      console.log('🔄 [PaymentForm] Updating payment methods:', {
        newPaymentMethods: updatedPayment.paymentMethods,
        newLength: updatedPayment.paymentMethods.length,
      })
      setPaymentMethods(updatedPayment.paymentMethods)
    }
  }, [clientDataManagement?.steps?.payment?.paymentMethods])

  // Form state
  const [isAddingNew, setIsAddingNew] = useState(false)
  const [editingMethod, setEditingMethod] = useState<PaymentMethod | null>(null)
  const [newPaymentType, setNewPaymentType] = useState<PaymentMethodType | null>(null)
  const [selectedMethodId, setSelectedMethodId] = useState<string | null>(null)

  // Main billing address form
  const billingForm = useForm<PaymentAddressInputs>({
    resolver: zodResolver(paymentAddressSchema),
    defaultValues: {
      firstName: '',
      lastName: '',
      street: '',
      city: '',
      zipCode: '',
      country: '',
    },
  })

  const { watch: watchBilling, setValue: setBillingValue } = billingForm

  // Initialize billing address with userProfile data on mount
  useEffect(() => {
    if (userProfile) {
      setBillingValue('firstName', userProfile.firstName || '')
      setBillingValue('lastName', userProfile.lastName || '')
      setBillingValue('street', `${userProfile.street || ''} ${userProfile.streetNum || ''}`.trim())
      setBillingValue('city', userProfile.city || '')
      setBillingValue('zipCode', userProfile.zip || '')
      setBillingValue('country', userProfile.country || '')
    }
  }, [userProfile, setBillingValue])

  // Get current billing address values
  const billingAddress = watchBilling()

  // Add new payment method
  const handleAddPaymentMethod = () => {
    setIsAddingNew(true)
    setNewPaymentType('creditCard') // Default to credit card
    setEditingMethod(null)
  }

  // Delete payment method
  const handleDeletePaymentMethod = (methodId: string) => {
    setPaymentMethods((prev) => prev.filter((method) => method.id !== methodId))
    if (selectedMethodId === methodId) {
      setSelectedMethodId(null)
    }
  }

  // Save payment method (new or edited)
  const handleSavePaymentMethod = (method: PaymentMethod) => {
    if (editingMethod) {
      // Update existing method
      setPaymentMethods((prev) => prev.map((m) => (m.id === method.id ? method : m)))
    } else {
      // Add new method
      setPaymentMethods((prev) => [...prev, method])
    }

    // Reset form state
    setIsAddingNew(false)
    setEditingMethod(null)
    setNewPaymentType(null)
    setSelectedMethodId(method.id)
  }

  // Cancel add/edit
  const handleCancelForm = () => {
    setIsAddingNew(false)
    setEditingMethod(null)
    setNewPaymentType(null)
  }

  // Submit all payment data
  const handleSubmit = async () => {
    const paymentData = {
      paymentMethods,
      lastUpdate: new Date().toISOString(),
    }

    console.log('🔄 [PaymentForm] Submitting payment data:', {
      paymentData,
    })

    await handleFormSubmission(paymentData)
  }

  // Previous step
  const handlePrevious = async () => {
    await goToPreviousStep()
  }

  // Render payment method form based on type
  const renderPaymentMethodForm = () => {
    if (!newPaymentType) return null

    switch (newPaymentType) {
      case 'creditCard':
        return (
          <CreditCardForm
            userProfile={userProfile}
            billingAddress={billingAddress}
            onSubmit={handleSavePaymentMethod}
            onCancel={handleCancelForm}
            initialData={editingMethod?.type === 'creditCard' ? editingMethod : undefined}
          />
        )
      case 'sepa':
        return (
          <SEPAForm
            onSubmit={handleSavePaymentMethod}
            onCancel={handleCancelForm}
            initialData={editingMethod?.type === 'sepa' ? editingMethod : undefined}
          />
        )
      case 'cash':
        // For now, just add basic cash method
        return (
          <Card>
            <CardHeader>
              <CardTitle>{t('PAYMENT.EDIT.CASH.TITLE')}</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex gap-3 justify-end">
                <Button type="button" variant="outline" onClick={handleCancelForm}>
                  {t('PAYMENT.EDIT.ACTIONS.CANCEL')}
                </Button>
                <Button
                  onClick={() =>
                    handleSavePaymentMethod({
                      id: `cash-${Date.now()}`,
                      type: 'cash',
                      notes: 'Cash payment method',
                    })
                  }
                >
                  {t('PAYMENT.EDIT.ACTIONS.SAVE')}
                </Button>
              </div>
            </CardContent>
          </Card>
        )
      case 'cheque':
        // For now, just add basic cheque method
        return (
          <Card>
            <CardHeader>
              <CardTitle>{t('PAYMENT.EDIT.CHEQUE.TITLE')}</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex gap-3 justify-end">
                <Button type="button" variant="outline" onClick={handleCancelForm}>
                  {t('PAYMENT.EDIT.ACTIONS.CANCEL')}
                </Button>
                <Button
                  onClick={() =>
                    handleSavePaymentMethod({
                      id: `cheque-${Date.now()}`,
                      type: 'cheque',
                      notes: 'Cheque payment method',
                    })
                  }
                >
                  {t('PAYMENT.EDIT.ACTIONS.SAVE')}
                </Button>
              </div>
            </CardContent>
          </Card>
        )
      default:
        return null
    }
  }

  return (
    <div className="mx-auto space-y-8">
      {!isAddingNew && !editingMethod && (
        <>
          <Card>
            <CardHeader>
              <div className="flex flex-row items-center justify-between">
                <CardTitle>{t('DATA.MANAGEMENT.STEP.PAYMENT.TITLE')}</CardTitle>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleAddPaymentMethod}
                  className="flex items-center gap-2"
                >
                  <PlusIcon className="w-4 h-4" />
                  {t('PAYMENT.ADD_METHOD')}
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              {/* Payment Methods Grid */}
              {paymentMethods.length > 0 ? (
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-3 ">
                  {paymentMethods.map((method) => (
                    <PaymentMethodCard
                      key={method.id}
                      paymentMethod={method}
                      active={selectedMethodId === method.id}
                      onSelect={() => setSelectedMethodId(method.id)}
                      onDelete={() => handleDeletePaymentMethod(method.id)}
                    />
                  ))}
                </div>
              ) : (
                <div className="text-center py-6 mb-6">
                  <div className="mx-auto w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mb-3">
                    <CreditCard className="w-6 h-6 text-gray-400" />
                  </div>
                  <h3 className="text-base font-medium text-gray-900 mb-1">{t('PAYMENT.EMPTY_STATE.TITLE')}</h3>
                  <p className="text-sm text-gray-500 mb-3">{t('PAYMENT.EMPTY_STATE.DESCRIPTION')}</p>
                  <Button onClick={handleAddPaymentMethod} size="sm" className="inline-flex items-center gap-2">
                    <PlusIcon className="w-4 h-4" />
                    {t('PAYMENT.ADD_METHOD')}
                  </Button>
                </div>
              )}

              {/* Payment Type Selection for New Method */}
              {isAddingNew && (
                <div className="mb-6">
                  <h3 className="text-lg font-semibold mb-3">{t('PAYMENT.EDIT.SELECT_PAYMENT_TYPE')}</h3>
                  <div className="flex gap-2 flex-wrap">
                    {paymentMethodTypeOptions.map((option) => (
                      <Button
                        key={option.value}
                        variant={newPaymentType === option.value ? 'default' : 'outline'}
                        onClick={() => setNewPaymentType(option.value)}
                      >
                        {t(option.label)}
                      </Button>
                    ))}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Billing Address */}
          <Card>
            <CardHeader>
              <CardTitle>{t('PAYMENT.BILLING_ADDRESS.TITLE')}</CardTitle>
            </CardHeader>
            <CardContent>
              <Form {...billingForm}>
                <CardForm>
                  <FormInput<PaymentAddressInputs> name="firstName" label={t('PAYMENT.BILLING_ADDRESS.FIRST_NAME')} />

                  <FormInput<PaymentAddressInputs> name="lastName" label={t('PAYMENT.BILLING_ADDRESS.LAST_NAME')} />

                  <div className="md:col-span-2">
                    <FormInput<PaymentAddressInputs> name="street" label={t('PAYMENT.BILLING_ADDRESS.STREET')} />
                  </div>

                  <FormInput<PaymentAddressInputs> name="city" label={t('PAYMENT.BILLING_ADDRESS.CITY')} />

                  <FormInput<PaymentAddressInputs> name="zipCode" label={t('PAYMENT.BILLING_ADDRESS.ZIP_CODE')} />

                  <div className="md:col-span-2">
                    <FormCountrySelect<PaymentAddressInputs>
                      name="country"
                      label={t('PAYMENT.BILLING_ADDRESS.COUNTRY')}
                    />
                  </div>
                </CardForm>
              </Form>
            </CardContent>
          </Card>
        </>
      )}

      {/* Payment Method Form */}
      {(isAddingNew || editingMethod) && renderPaymentMethodForm()}

      {/* Navigation */}
      {!isAddingNew && !editingMethod && (
        <DataManagementNavigation
          submitButtonDisabled={isSaving || paymentMethods.length === 0}
          nextStepDisabled={isSaving || paymentMethods.length === 0}
          previousStepDisabled={isFirstStep}
          clientDataManagement={clientDataManagement}
          isSubmitting={isSaving}
          showSubmitButton={isLastStep}
          onNext={handleSubmit}
          onPrevious={handlePrevious}
          onSubmit={handleSubmit}
        />
      )}
    </div>
  )
}
