import { useEffect } from 'react'

import { useDataManagement } from '@/modules/data-management/hooks/use-data-management'
import {
  churchTaxOptions,
  jobTypeOptions,
  ProfessionFormInputs,
  professionSchema,
  taxClassOptions,
} from '@/modules/profession/types/profession-schema'
import { zodResolver } from '@hookform/resolvers/zod'
import { useTranslations } from 'next-intl'
import { useForm } from 'react-hook-form'

import { Card, CardContent, CardForm, CardHeader, CardTitle } from '@/components/ui/card'
import { Form } from '@/components/ui/form'
import { FormCalendar, FormInput, FormSelect } from '@/components/form-inputs'

import { ClientDataManagement } from '../../../types/data-management-types'
import { DataManagementNavigation } from '../../data-management-navigation'

interface ProfessionStepProps {
  clientDataManagement: ClientDataManagement
}

export const ProfessionStep = ({ clientDataManagement }: ProfessionStepProps) => {
  const t = useTranslations()
  const { handleFormSubmission, isSaving, isFirstStep, goToPreviousStep, isLastStep } =
    useDataManagement(clientDataManagement)

  // Get existing contact data if available
  const existingProfessionData = clientDataManagement?.steps?.profession

  const form = useForm<ProfessionFormInputs>({
    mode: 'all',
    shouldFocusError: true,
    resolver: zodResolver(professionSchema),
    defaultValues: {
      workPercentageOffice: existingProfessionData?.workPercentageOffice || undefined,
      profession: existingProfessionData?.profession || '',
      jobType: (existingProfessionData?.jobType as any) || undefined,
      workPercentagePhysical: existingProfessionData?.workPercentagePhysical || undefined,
      incomeYearlyBrutto: existingProfessionData?.incomeYearlyBrutto?.toString() || '',
      incomeYearlyNetto: existingProfessionData?.incomeYearlyNetto?.toString() || '',
      incomeYearlySalaries: existingProfessionData?.incomeYearlySalaries?.toString() || '',
      retirementSavingSince: existingProfessionData?.retirementSavingSince
        ? new Date(existingProfessionData.retirementSavingSince)
        : undefined,

      capitalFormingPayments: existingProfessionData?.capitalFormingPayments?.toString() || '',
      taxOfficeName: existingProfessionData?.taxOfficeName || '',
      taxNumber: existingProfessionData?.taxNumber || '',
      taxId: existingProfessionData?.taxId || '',
      taxClass: (existingProfessionData?.taxClass as any) || undefined,
      churchTaxPercentage: (existingProfessionData?.churchTaxPercentage?.toString() as any) || undefined,
      healthInsurance: existingProfessionData?.healthInsurance || '',
      socialInsuranceNumber: existingProfessionData?.socialInsuranceNumber || '',
    },
  })

  const workPercentagePhysical = form.watch('workPercentagePhysical')

  useEffect(() => {
    if (workPercentagePhysical !== undefined && workPercentagePhysical >= 0 && workPercentagePhysical <= 100) {
      form.setValue('workPercentageOffice', 100 - +workPercentagePhysical)
    }
  }, [workPercentagePhysical, form])

  const onSubmit = async (data: ProfessionFormInputs) => {
    // Transform data to match the expected format for data management
    const transformedData = {
      ...data,
      lastUpdate: new Date().toISOString(),
    }

    await handleFormSubmission(transformedData)
  }

  // Navigation handlers
  const handleFormValidationAndSubmit = async () => {
    // Trigger form validation and submission (used for both Next and Submit)
    await form.handleSubmit(onSubmit)()
  }

  const handlePrevious = async () => {
    await goToPreviousStep()
  }

  return (
    <div className=" mx-auto space-y-8">
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
          <Card>
            <CardHeader>
              <CardTitle>{t('DATA.MANAGEMENT.STEP.CONTACT_DATA.TITLE')}</CardTitle>
            </CardHeader>
            <CardContent>
              <CardForm>
                <div>
                  <h3 className="text-lg font-semibold text-gray-900">
                    {t('PROFESSION.EDIT.JOB_INFO_FIELDSET.TITLE')}
                  </h3>
                  <div className=" flex flex-col gap-4 mt-5">
                    <FormInput<ProfessionFormInputs> name="profession" label="PROFESSION.EDIT.PROFESSION_FIELD.LABEL" />

                    <FormSelect<ProfessionFormInputs>
                      name="jobType"
                      label="PROFESSION.EDIT.JOB_TYPE_FIELD.LABEL"
                      options={jobTypeOptions}
                    />

                    <FormInput<ProfessionFormInputs>
                      name="workPercentagePhysical"
                      label="PROFESSION.EDIT.PHYSICAL_FIELD.LABEL"
                      type="number"
                      min={0}
                      max={100}
                      suffix="%"
                    />
                    <FormInput<ProfessionFormInputs>
                      name="workPercentageOffice"
                      label="PROFESSION.EDIT.OFFICE_FIELD.LABEL"
                      type="number"
                      disabled
                      readOnly
                      inputClassName="cursor-not-allowed bg-gray-100"
                      min={0}
                      max={100}
                      suffix="%"
                    />
                  </div>
                </div>

                {/* Income Information */}
                <div>
                  <h3 className="text-lg font-semibold text-gray-900">{t('PROFESSION.EDIT.INCOME_FIELDSET.TITLE')}</h3>
                  <div className=" flex flex-col gap-4 mt-5">
                    <FormInput<ProfessionFormInputs>
                      name="incomeYearlyBrutto"
                      label="PROFESSION.EDIT.YEARLY_BRUTTO_FIELD.LABEL"
                      placeholder="0"
                      suffix="&euro;"
                    />

                    <FormInput<ProfessionFormInputs>
                      name="incomeYearlyNetto"
                      label="PROFESSION.EDIT.YEARLY_NETTO_FIELD.LABEL"
                      placeholder="0"
                      suffix="&euro;"
                    />

                    <FormInput<ProfessionFormInputs>
                      name="incomeYearlySalaries"
                      label="PROFESSION.EDIT.YEARLY_SALARIES_FIELD.LABEL"
                    />

                    <FormCalendar<ProfessionFormInputs>
                      name="retirementSavingSince"
                      label="PROFESSION.EDIT.SAVING_SINCE_FIELD.LABEL"
                    />

                    <FormInput<ProfessionFormInputs>
                      name="capitalFormingPayments"
                      label="PROFESSION.EDIT.CAP_FORM_PAYMENTS_FIELD.LABEL"
                      placeholder="0"
                      suffix="&euro;"
                    />
                  </div>
                </div>

                {/* Tax Information */}
                <div>
                  <h3 className="text-lg font-semibold text-gray-900">
                    {t('PROFESSION.EDIT.TAX_INFO_FIELDSET.TITLE')}
                  </h3>
                  <div className=" flex flex-col gap-4 mt-5">
                    <FormInput<ProfessionFormInputs>
                      name="taxOfficeName"
                      label="PROFESSION.EDIT.TAX_OFFICE_FIELD.LABEL"
                    />

                    <FormInput<ProfessionFormInputs> name="taxNumber" label="PROFESSION.EDIT.TAX_NUMBER_FIELD.LABEL" />

                    <FormInput<ProfessionFormInputs> name="taxId" label="PROFESSION.EDIT.TAX_ID_FIELD.LABEL" />

                    <FormSelect<ProfessionFormInputs>
                      name="taxClass"
                      label="PROFESSION.EDIT.TAX_CLASS_FIELD.LABEL"
                      options={taxClassOptions}
                    />

                    <FormSelect<ProfessionFormInputs>
                      name="churchTaxPercentage"
                      label="PROFESSION.EDIT.CHURCH_TAX_FIELD.LABEL"
                      options={churchTaxOptions}
                    />
                  </div>
                </div>

                {/* Additional Information */}
                <div>
                  <h3 className="text-lg font-semibold text-gray-900">
                    {t('PROFESSION.EDIT.ADDITIONAL_INFO_FIELDSET.TITLE')}
                  </h3>
                  <div className="flex flex-col gap-4 mt-5">
                    <FormInput<ProfessionFormInputs>
                      name="healthInsurance"
                      label="PROFESSION.EDIT.HEALTH_INSURANCE_FIELD.LABEL"
                    />

                    <FormInput<ProfessionFormInputs>
                      name="socialInsuranceNumber"
                      label="PROFESSION.EDIT.SOCIAL_INSURANCE_NUMBER_FIELD.LABEL"
                    />
                  </div>
                </div>
              </CardForm>
            </CardContent>
          </Card>
        </form>

        <DataManagementNavigation
          submitButtonDisabled={isSaving}
          nextStepDisabled={isSaving}
          previousStepDisabled={isFirstStep}
          clientDataManagement={clientDataManagement}
          isSubmitting={isSaving}
          showSubmitButton={isLastStep}
          onNext={handleFormValidationAndSubmit}
          onPrevious={handlePrevious}
          onSubmit={handleFormValidationAndSubmit}
        />
      </Form>
    </div>
  )
}
