'use client'

import { genderOptions } from '@/modules/auth/types/auth-schema'
import {
  clientInformationSchema,
  familyStatusOptions,
  type ClientInformationInputs,
} from '@/modules/profile/types/profile-schema'
import { zodResolver } from '@hookform/resolvers/zod'
import { useTranslations } from 'next-intl'
import { useForm } from 'react-hook-form'

import { Card, CardContent, CardForm, CardHeader, CardTitle } from '@/components/ui/card'
import { Form } from '@/components/ui/form'
import { FormCalendar, FormInput, FormSelect } from '@/components/form-inputs'
import { FormCountrySelect } from '@/components/form-inputs/form-country-select'

import { useDataManagement } from '../../../hooks/use-data-management'
import { ClientDataManagement } from '../../../types/data-management-types'
import { DataManagementNavigation } from '../../data-management-navigation'

interface ClientInformationStepProps {
  clientDataManagement: ClientDataManagement
}

export const ClientInformationStep = ({ clientDataManagement }: ClientInformationStepProps) => {
  const t = useTranslations()
  const { handleFormSubmission, isSaving, isFirstStep, goToPreviousStep, isLastStep } =
    useDataManagement(clientDataManagement)

  // Get existing client information data if available
  const existingClientInformation = clientDataManagement?.steps?.clientInformation

  const form = useForm<ClientInformationInputs>({
    mode: 'all',
    shouldFocusError: true,
    resolver: zodResolver(clientInformationSchema),
    defaultValues: {
      salutation: (existingClientInformation?.salutation as any) || undefined,
      title: existingClientInformation?.title || '',
      firstName: existingClientInformation?.firstName || '',
      lastName: existingClientInformation?.lastName || '',
      birthName: existingClientInformation?.birthName || '',
      birthdate: existingClientInformation?.birthdate ? new Date(existingClientInformation.birthdate) : undefined,
      familyStatus: (existingClientInformation?.familyStatus as any) || undefined,
      birthPlace: existingClientInformation?.birthPlace || '',
      birthCountry: existingClientInformation?.birthCountry || '',
      nationality: existingClientInformation?.nationality || '',
    },
  })

  const onSubmit = async (data: ClientInformationInputs) => {
    // Transform data to match the expected format for data management
    const transformedData = {
      ...data,
      lastUpdate: new Date().toISOString(),
    }

    await handleFormSubmission(transformedData)
  }

  // Navigation handlers
  const handleFormValidationAndSubmit = async () => {
    // Trigger form validation and submission (used for both Next and Submit)
    await form.handleSubmit(onSubmit)()
  }

  const handlePrevious = async () => {
    await goToPreviousStep()
  }

  return (
    <div className=" mx-auto space-y-8">
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
          <Card>
            <CardHeader>
              <CardTitle>{t('DATA.MANAGEMENT.STEP.CLIENT_INFORMATION.TITLE')}</CardTitle>
            </CardHeader>
            <CardContent>
              <CardForm>
                <FormSelect<ClientInformationInputs>
                  name="salutation"
                  label={t('PROFILE.EDIT_FORM.MY_DATA_FIELDSET.SALUTATION_FIELD.LABEL')}
                  options={genderOptions}
                />

                <FormInput<ClientInformationInputs>
                  name="title"
                  label={t('PROFILE.EDIT_FORM.MY_DATA_FIELDSET.TITLE')}
                />

                <FormInput<ClientInformationInputs>
                  name="firstName"
                  label={t('PROFILE.EDIT_FORM.MY_DATA_FIELDSET.FIRSTNAME_FIELD.LABEL')}
                />

                <FormInput<ClientInformationInputs>
                  name="lastName"
                  label={t('PROFILE.EDIT_FORM.MY_DATA_FIELDSET.LASTNAME_FIELD.LABEL')}
                />

                <FormInput<ClientInformationInputs>
                  name="birthName"
                  label={t('PROFILE.EDIT_FORM.MY_DATA_FIELDSET.BIRTH_NAME_FIELD.LABEL')}
                />

                <FormCalendar<ClientInformationInputs>
                  name="birthdate"
                  label={t('PROFILE.EDIT_FORM.MY_DATA_FIELDSET.BIRTHDAY_FIELD.LABEL')}
                />

                <FormSelect<ClientInformationInputs>
                  name="familyStatus"
                  label={t('PROFILE.EDIT_FORM.MY_DATA_FIELDSET.MARTIAL_STATUS_FIELD.LABEL')}
                  options={familyStatusOptions}
                />

                <FormInput<ClientInformationInputs>
                  name="birthPlace"
                  label={t('PROFILE.EDIT_FORM.MY_DATA_FIELDSET.BIRTH_PLACE_FIELD.LABEL')}
                />

                <FormCountrySelect<ClientInformationInputs>
                  name="birthCountry"
                  label={t('PROFILE.EDIT_FORM.MY_DATA_FIELDSET.BIRTH_COUNTRY_FIELD.LABEL')}
                />

                <FormCountrySelect<ClientInformationInputs>
                  name="nationality"
                  label={t('PROFILE.EDIT_FORM.MY_DATA_FIELDSET.NATIONALITY_FIELD.LABEL')}
                  isNationality={true}
                />
              </CardForm>
            </CardContent>
          </Card>
        </form>

        <DataManagementNavigation
          submitButtonDisabled={isSaving}
          nextStepDisabled={isSaving}
          previousStepDisabled={isFirstStep}
          clientDataManagement={clientDataManagement}
          isSubmitting={isSaving}
          showSubmitButton={isLastStep}
          onNext={handleFormValidationAndSubmit}
          onPrevious={handlePrevious}
          onSubmit={handleFormValidationAndSubmit}
        />
      </Form>
    </div>
  )
}
