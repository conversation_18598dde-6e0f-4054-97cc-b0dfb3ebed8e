'use client'

import { legitimationSchema, type LegitimationInputs } from '@/modules/profile/types/profile-schema'
import { zodResolver } from '@hookform/resolvers/zod'
import { useTranslations } from 'next-intl'
import { useForm } from 'react-hook-form'

import { Card, CardContent, CardForm, CardHeader, CardTitle } from '@/components/ui/card'
import { Form } from '@/components/ui/form'
import { FormCalendar, FormInput } from '@/components/form-inputs'

import { useDataManagement } from '../../../hooks/use-data-management'
import { ClientDataManagement } from '../../../types/data-management-types'
import { DataManagementNavigation } from '../../data-management-navigation'

interface LegitimationStepProps {
  clientDataManagement: ClientDataManagement
}

export const LegitimationStep = ({ clientDataManagement }: LegitimationStepProps) => {
  const t = useTranslations()
  const { handleFormSubmission, isSaving, isFirstStep, goToPreviousStep, isLastStep } =
    useDataManagement(clientDataManagement)

  // Get existing legitimation data if available
  const existingLegitimation = clientDataManagement?.steps?.legitimation

  const form = useForm<LegitimationInputs>({
    mode: 'all',
    shouldFocusError: true,
    resolver: zodResolver(legitimationSchema),
    defaultValues: {
      idCardNumber: existingLegitimation?.idCardNumber || '',
      idCardDate: existingLegitimation?.idCardDate ? new Date(existingLegitimation.idCardDate) : undefined,
      idCardAuthority: existingLegitimation?.idCardAuthority || '',
    },
  })

  const onSubmit = async (data: LegitimationInputs) => {
    // Transform data to match the expected format for data management
    const transformedData = {
      ...data,
      lastUpdate: new Date().toISOString(),
    }

    await handleFormSubmission(transformedData)
  }

  // Navigation handlers
  const handleFormValidationAndSubmit = async () => {
    // Trigger form validation and submission (used for both Next and Submit)
    await form.handleSubmit(onSubmit)()
  }

  const handlePrevious = async () => {
    await goToPreviousStep()
  }

  return (
    <div className=" mx-auto space-y-8">
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
          <Card>
            <CardHeader>
              <CardTitle>{t('DATA.MANAGEMENT.STEP.LEGITIMATION.TITLE')}</CardTitle>
            </CardHeader>
            <CardContent>
              <CardForm>
                <FormInput<LegitimationInputs>
                  name="idCardNumber"
                  label={t('PROFILE.EDIT_FORM.MY_DATA_FIELDSET.ID_CARD_NUMBER_FIELD.LABEL')}
                />

                <FormCalendar<LegitimationInputs>
                  name="idCardDate"
                  label={t('PROFILE.EDIT_FORM.MY_DATA_FIELDSET.ID_CARD_DATE_FIELD.LABEL')}
                />

                <FormInput<LegitimationInputs>
                  name="idCardAuthority"
                  label={t('PROFILE.EDIT_FORM.MY_DATA_FIELDSET.ID_CARD_AUTHORITY_FIELD.LABEL')}
                />
              </CardForm>
            </CardContent>
          </Card>
        </form>

        <DataManagementNavigation
          submitButtonDisabled={isSaving}
          nextStepDisabled={isSaving}
          previousStepDisabled={isFirstStep}
          clientDataManagement={clientDataManagement}
          isSubmitting={isSaving}
          showSubmitButton={isLastStep}
          onNext={handleFormValidationAndSubmit}
          onPrevious={handlePrevious}
          onSubmit={handleFormValidationAndSubmit}
        />
      </Form>
    </div>
  )
}
