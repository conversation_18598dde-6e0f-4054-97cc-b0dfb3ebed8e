'use client'

import { contactDataSchema, type ContactDataInputs } from '@/modules/profile/types/profile-schema'
import { zodResolver } from '@hookform/resolvers/zod'
import { useTranslations } from 'next-intl'
import { useForm } from 'react-hook-form'

import { Card, CardContent, CardForm, CardHeader, CardTitle } from '@/components/ui/card'
import { Form } from '@/components/ui/form'
import { FormInput } from '@/components/form-inputs'
import { FormPhoneInput } from '@/components/form-inputs/form-phone-input'

import { useDataManagement } from '../../../hooks/use-data-management'
import { ClientDataManagement } from '../../../types/data-management-types'
import { DataManagementNavigation } from '../../data-management-navigation'

interface ContactDataStepProps {
  clientDataManagement: ClientDataManagement
}

export const ContactDataStep = ({ clientDataManagement }: ContactDataStepProps) => {
  const t = useTranslations()
  const { handleFormSubmission, isSaving, isFirstStep, goToPreviousStep, isLastStep } =
    useDataManagement(clientDataManagement)

  // Get existing contact data if available
  const existingContactData = clientDataManagement?.steps?.contactData

  const form = useForm<ContactDataInputs>({
    mode: 'all',
    shouldFocusError: true,
    resolver: zodResolver(contactDataSchema),
    defaultValues: {
      email: existingContactData?.email || '',
      preferredContactType: existingContactData?.preferredContactType || '',
      preferredAppeal: existingContactData?.preferredAppeal || '',
      phone: existingContactData?.phone || '',
      mobile: existingContactData?.mobile || '',
      businessPhone: existingContactData?.businessPhone || '',
    },
  })

  const onSubmit = async (data: ContactDataInputs) => {
    // Transform data to match the expected format for data management
    const transformedData = {
      ...data,
      lastUpdate: new Date().toISOString(),
    }

    await handleFormSubmission(transformedData)
  }

  // Navigation handlers
  const handleFormValidationAndSubmit = async () => {
    // Trigger form validation and submission (used for both Next and Submit)
    await form.handleSubmit(onSubmit)()
  }

  const handlePrevious = async () => {
    await goToPreviousStep()
  }

  return (
    <div className=" mx-auto space-y-6">
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
          <Card>
            <CardHeader>
              <CardTitle>{t('DATA.MANAGEMENT.STEP.CONTACT_DATA.TITLE')}</CardTitle>
            </CardHeader>
            <CardContent>
              <CardForm>
                <FormInput<ContactDataInputs>
                  name="email"
                  label={t('PROFILE.EDIT_FORM.ADDRESS_FIELDSET.EMAIL_FIELD.LABEL')}
                />

                <FormInput<ContactDataInputs>
                  name="preferredContactType"
                  label={t('PROFILE.EDIT_FORM.ADDRESS_FIELDSET.PREFERRED_CONTACT_TYPE_FIELD.LABEL')}
                />

                <FormInput<ContactDataInputs>
                  name="preferredAppeal"
                  label={t('PROFILE.EDIT_FORM.CONTACT_FIELDSET.PREFERRED_APPEAL_FIELD.LABEL')}
                />

                <FormPhoneInput<ContactDataInputs>
                  name="phone"
                  label={t('PROFILE.EDIT_FORM.ADDRESS_FIELDSET.PHONE_FIELD.LABEL')}
                />

                <FormPhoneInput<ContactDataInputs>
                  name="mobile"
                  label={t('PROFILE.EDIT_FORM.ADDRESS_FIELDSET.MOBILE_FIELD.LABEL')}
                />

                <FormPhoneInput<ContactDataInputs>
                  name="businessPhone"
                  label={t('PROFILE.EDIT_FORM.ADDRESS_FIELDSET.BUSINESS_PHONE_FIELD.LABEL')}
                />
              </CardForm>
            </CardContent>
          </Card>
        </form>

        <DataManagementNavigation
          submitButtonDisabled={isSaving}
          nextStepDisabled={isSaving}
          previousStepDisabled={isFirstStep}
          clientDataManagement={clientDataManagement}
          isSubmitting={isSaving}
          showSubmitButton={isLastStep}
          onNext={handleFormValidationAndSubmit}
          onPrevious={handlePrevious}
          onSubmit={handleFormValidationAndSubmit}
        />
      </Form>
    </div>
  )
}
