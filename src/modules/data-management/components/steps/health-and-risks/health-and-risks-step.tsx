'use client'

import { useCallback, useMemo } from 'react'

import { RiskForm } from '@/modules/risk/components/risk-form'
import { RiskFormInputs } from '@/modules/risk/types/risk-schema'

import { useDataManagement } from '../../../hooks/use-data-management'
import { ClientDataManagement } from '../../../types/data-management-types'
import { DataManagementNavigation } from '../../data-management-navigation'

interface HealthAndRisksStepProps {
  clientDataManagement: ClientDataManagement
}

export const HealthAndRisksStep = ({ clientDataManagement }: HealthAndRisksStepProps) => {
  const { handleFormSubmission, goToPreviousStep, isFirstStep, isSaving, isLastStep, goToNextStep } =
    useDataManagement(clientDataManagement)

  // Get existing health and risks data from data management
  const existingHealthAndRisks = clientDataManagement?.steps?.healthAndRisks

  // Use existing data if available, otherwise null
  const initialDataForRiskForm = existingHealthAndRisks || null

  // Check if step has been updated (similar to contracts step)
  const isStepUpdated = useMemo(() => {
    return !!(existingHealthAndRisks && existingHealthAndRisks.lastUpdate)
  }, [existingHealthAndRisks])

  // Determine if next button should be disabled
  const canGoNext = isStepUpdated

  console.log('🔍 [HealthAndRisksStep] Navigation state:', {
    existingHealthAndRisks,
    isStepUpdated,
    canGoNext,
    hasLastUpdate: !!(existingHealthAndRisks && existingHealthAndRisks.lastUpdate),
  })

  // Handle risk form completion - this is the single save point
  const handleRiskFormComplete = useCallback(
    async (riskData: RiskFormInputs) => {
      // Add lastUpdate and save directly
      const healthAndRisksData = {
        ...riskData,
        lastUpdate: new Date().toISOString(),
      }

      // Save the data using the data management system
      await handleFormSubmission(healthAndRisksData)

      // After successful save, navigate to next step
      await goToNextStep()
    },
    [handleFormSubmission, goToNextStep]
  )

  return (
    <div className="space-y-6">
      {/* Risk Form with Custom Navigation */}
      <RiskForm
        initialData={initialDataForRiskForm}
        formWrapperClassName="max-w-none"
        isLoading={isSaving}
        customNavigation={({ onSubmit }) => (
          <DataManagementNavigation
            onSubmit={onSubmit}
            onNext={goToNextStep}
            onPrevious={goToPreviousStep}
            isSubmitting={isSaving}
            submitButtonDisabled={!canGoNext}
            nextStepDisabled={!canGoNext}
            previousStepDisabled={isFirstStep}
            clientDataManagement={clientDataManagement}
            showSubmitButton={isLastStep}
          />
        )}
      />
    </div>
  )
}
