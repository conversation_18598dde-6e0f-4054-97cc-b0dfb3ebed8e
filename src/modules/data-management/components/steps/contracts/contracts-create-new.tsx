import { PlusIcon } from 'lucide-react'

import { Button } from '@/components/ui/button'

export const ContractsCreateNew = () => {
  const handleNewContractTab = () => {
    console.log('handleNewContractTab')
  }
  const isCreatingNew = false
  return (
    <div className="flex flex-row items-center justify-between pb-2">
      <div className="font-semibold text-base">Contracts</div>
      <Button
        variant="outline"
        size="sm"
        onClick={handleNewContractTab}
        className="flex items-center gap-1"
        aria-pressed={isCreatingNew}
      >
        <PlusIcon className="w-4 h-4" />
        Create Contract
      </Button>
    </div>
  )
}
