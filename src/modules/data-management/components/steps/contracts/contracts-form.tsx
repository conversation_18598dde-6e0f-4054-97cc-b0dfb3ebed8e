'use client'

import { useState } from 'react'

import { createOrUpdateContract } from '@/modules/contracts/api/contracts-actions'
import { ContractForm } from '@/modules/contracts/components/contract-form'
import { Product, Provider } from '@/modules/contracts/libs/contract-types'
import { ContractFormInputs } from '@/modules/contracts/types/contract-schema'
import { ContractsData } from '@/modules/data-management/types/data-management-types'
import { useTranslations } from 'next-intl'
import { useAction } from 'next-safe-action/hooks'
import { toast } from 'sonner'

import { fileToDocument } from '@/lib/actions/documents/file-to-document'
import { SelectOption } from '@/components/form-inputs'

import { MiniContractCard } from './mini-contract-card'

interface ContractSubFormProps {
  contractsData: ContractsData
  categoryOptions: SelectOption[]
  providers: Provider[]
  products: Product[]
}

export const ContractsStepForm = ({ contractsData, categoryOptions, providers, products }: ContractSubFormProps) => {
  const t = useTranslations()

  // Contract management hooks
  const { contractsData: contracts, canCreateNew } = contractsData
  // Direct useAction hook for contract updates with custom handlers
  const { executeAsync: updateContract } = useAction(createOrUpdateContract, {
    onSuccess: (data) => {
      console.log('Contract update successful:', data.data)

      // Show success message without redirecting
      toast.success(t('CONTRACTS.EDIT_FORM.EDIT_MODE.TOAST.SUCCESS.MESSAGE'))
    },
    onError: () => {
      // Show error message
      toast.error(t('CONTRACTS.EDIT_FORM.EDIT_MODE.TOAST.ERROR.MESSAGE'))
    },
  })

  // Local state for navigation
  const [currentContractIndex, setCurrentContractIndex] = useState(0)
  const [isCreatingNew, setIsCreatingNew] = useState(false)

  // Use contracts from store instead of props for simplified architecture
  const existingContracts = contracts

  // Current contract data
  const currentContract = isCreatingNew ? null : existingContracts[currentContractIndex]
  const currentContractId = isCreatingNew ? 'new' : currentContract?.id?.toString() || ''

  // Tab/box navigation handlers
  const handleTabClick = (idx: number) => {
    setIsCreatingNew(false)
    setCurrentContractIndex(idx)
  }

  // Handle contract update
  const handleContractUpdate = async (contractData: ContractFormInputs) => {
    console.log('Contract update called with data:', contractData)
    if (!currentContract?.id) return

    try {
      // Process files if any
      let files: any[] = []
      if (contractData.files) {
        files = await Promise.all(contractData.files.map(async (file) => await fileToDocument(file)))
      }

      // Call the update action using the useAction hook
      const result = await updateContract({
        ...contractData,
        files: files,
        editForm: true,
        contract_id: currentContract.id.toString(),
      })

      console.log('Contract update result:', result)

      if (result?.data) {
        console.log('Contract update successful, updating store...')
      }
    } catch (error) {
      console.error('Contract update error:', error)
      // Error handling is done by the useAction hook
    }
  }

  return (
    <div className="space-y-6">
      {/* Contract Tabs/Boxes Navigation */}
      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 xl:grid-cols-4 gap-2 pb-2">
        {existingContracts.map((contract, idx) => (
          <MiniContractCard
            key={contract.id}
            isUpdated={contract.isUpdated}
            contract={contract}
            active={!isCreatingNew && currentContractIndex === idx}
            onClick={() => handleTabClick(idx)}
          />
        ))}
      </div>

      {/* Contract Form */}
      {currentContract && (
        <ContractForm
          onComplete={handleContractUpdate}
          editForm={true}
          key={currentContractId} // Force re-render when switching contracts
          contract={currentContract}
          categoryOptions={categoryOptions}
          providers={providers}
          products={products}
        />
      )}
    </div>
  )
}
