'use client'

import { ChevronRight } from 'lucide-react'
import { useTranslations } from 'next-intl'

import { Button } from '@/components/ui/button'
import { NavigationProps } from '@/components/dynamic-form'

import { ClientDataManagement } from '../types/data-management-types'

interface DataManagementNavigationProps
  extends Omit<NavigationProps, 'totalSteps' | 'currentStep' | 'isFirstStep' | 'isLastStep'> {
  clientDataManagement: ClientDataManagement
}

export function DataManagementNavigation({
  showSubmitButton,
  nextStepDisabled,
  submitButtonDisabled,
  previousStepDisabled,
  isSubmitting,
  onNext,
  onPrevious,
  onSubmit,
}: DataManagementNavigationProps) {
  const t = useTranslations()

  return (
    <div className="flex items-center justify-between ">
      <Button
        type="button"
        variant="outline"
        onClick={onPrevious}
        disabled={previousStepDisabled || isSubmitting}
        className="min-w-24"
      >
        {isSubmitting ? 'Loading...' : t('Previous')}
      </Button>

      {showSubmitButton ? (
        <Button type="button" onClick={onSubmit} disabled={isSubmitting || submitButtonDisabled} className="min-w-24">
          {isSubmitting ? 'Loading...' : 'Complete'}
        </Button>
      ) : (
        <Button type="button" onClick={onNext} disabled={isSubmitting || nextStepDisabled} className="min-w-24">
          {isSubmitting ? 'Loading...' : t('Next')}
          <ChevronRight className="w-4 h-4 ml-1" />
        </Button>
      )}
    </div>
  )
}
