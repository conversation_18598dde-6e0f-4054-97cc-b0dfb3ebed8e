'use client'

import React from 'react'

import { Check } from 'lucide-react'
import { useAction } from 'next-safe-action/hooks'

import { cn } from '@/lib/utils'
import { Button } from '@/components/ui/button'

import { resetDataManagementAction } from '../api/data-management-actions'
import { NavigationState } from '../types/data-management-types'

interface DataManagementStepperProps {
  navigationState: NavigationState
}
export function DataManagementStepper({ navigationState }: DataManagementStepperProps) {
  const { execute: resetDataManagement } = useAction(resetDataManagementAction)

  const steps = navigationState?.navigationSteps

  return (
    <div className="w-full">
      <div className="flex items-center justify-between mb-6">
        {steps?.map((step, index) => {
          return (
            <React.Fragment key={step.title}>
              <div className="flex flex-col items-center">
                <Button
                  variant="ghost"
                  size="sm"
                  className={cn(
                    'w-10 h-10 rounded-full border-2 p-0 transition-all duration-200 dark:border-muted-foreground disabled:opacity-100',
                    {
                      'border-primary bg-primary text-primary-foreground': step.isActive,
                      'border-muted-foreground bg-background text-muted-foreground':
                        !step.isActive && !step.isCompleted,
                      'hover:border-primary hover:bg-primary/10': step.isActive && !step.isCompleted,
                      'cursor-pointer': step.isActive,
                      'cursor-default': !step.isActive,
                    }
                  )}
                  disabled={!step.isActive}
                >
                  {step.isCompleted ? (
                    <Check className="w-4 h-4" />
                  ) : (
                    <span className="text-sm font-medium">{index + 1}</span>
                  )}
                </Button>
              </div>

              {index < steps.length - 1 && (
                <div className="flex-1 mx-2">
                  <div
                    className={cn('h-0.5 transition-all duration-300', {
                      'bg-primary': step.isCompleted,
                      'bg-muted': !step.isCompleted,
                    })}
                  />
                </div>
              )}
            </React.Fragment>
          )
        })}
      </div>
      {process.env.NODE_ENV === 'development' && (
        <Button variant="ghost" size="sm" onClick={() => resetDataManagement()}>
          Reset
        </Button>
      )}
    </div>
  )
}
