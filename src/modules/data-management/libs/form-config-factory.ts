import { FormConfig, StepConfig } from '@/components/dynamic-form'

/**
 * Configuration options for creating a data management form
 */
export interface DataManagementFormOptions {
  /** The step configuration to use */
  step: StepConfig
  /** Custom submit button text (optional) */
  submitButtonText?: string
  /** Custom next button text (optional) */
  nextButtonText?: string
  /** Custom previous button text (optional) */
  previousButtonText?: string
  /** Whether to show the stepper (default: false) */
  showStepper?: boolean
  /** Whether to show navigation buttons (default: false) */
  showNavigationButtons?: boolean
}

/**
 * Factory function to create standardized form configurations for data management forms
 * This eliminates duplication across all step form components
 */
export function createDataManagementFormConfig(options: DataManagementFormOptions): FormConfig {
  const {
    step,
    submitButtonText = `Save ${step.title}`,
    nextButtonText = 'Next',
    previousButtonText = 'Previous',
    showStepper = false,
    showNavigationButtons = false,
  } = options

  return {
    title: step.title,
    description: step.description,
    submitButtonText,
    nextButtonText,
    previousButtonText,
    showStepNumbers: false,
    showProgressBar: false,
    allowStepNavigation: false,
    validateOnStepChange: true,
    showStepper,
    showNavigationButtons,
    steps: [step],
  }
}

/**
 * Helper function to prepare initial data for a form step
 * This standardizes the initial data preparation pattern
 */
export function prepareStepInitialData<T>(stepId: string, existingData: T | undefined): Record<string, T | unknown> {
  return {
    [stepId]: existingData || {},
  }
}

/**
 * Helper function to create debug logging data for form components
 * This standardizes the debug logging pattern across all forms
 */
export function createFormDebugData(
  componentName: string,
  clientDataManagement: any,
  existingData: any,
  dataKey: string
) {
  return {
    hasClientDataManagement: !!clientDataManagement,
    hasSteps: !!clientDataManagement?.steps,
    [`has${dataKey.charAt(0).toUpperCase() + dataKey.slice(1)}`]: !!existingData,
    [`${dataKey}Data`]: existingData,
    currentStep: clientDataManagement?.navigationState?.currentStep,
  }
}
