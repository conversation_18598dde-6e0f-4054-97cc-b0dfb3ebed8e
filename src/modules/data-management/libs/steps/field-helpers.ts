import { validationRuleHelpers } from '@/components/dynamic-form'
import { FieldConfig } from '@/components/dynamic-form/types'

/**
 * Helper function to create field configuration with proper defaults
 * This ensures all required properties are set according to the FieldConfig schema
 */
export function createField(config: Partial<FieldConfig> & Pick<FieldConfig, 'name' | 'type' | 'label'>): FieldConfig {
  const baseField: FieldConfig = {
    disabled: false,
    autoComplete: true,
    isRequired: true,
    validation: [validationRuleHelpers.required()],
    colSpan: 1,
    ...config,
  }

  return baseField
}
