'use server'

import { revalidateTag } from 'next/cache'

import { createDetailedError, fetchApi } from '@/lib/fetch-api'
import { authenticatedAction } from '@/lib/safe-actions'

import { PROFILE_TAGS } from '../../profile/api/profile-tags'
import { professionSchema } from '../types/profession-schema'

export const updateUserProfession = authenticatedAction
  .schema(professionSchema)
  .action(async ({ parsedInput, ctx: { session } }) => {
    if (!session?.accessToken) {
      throw new Error('No session found')
    }

    // Calculate office percentage based on physical percentage
    const workPercentageOffice = parsedInput.workPercentagePhysical
      ? 100 - parsedInput.workPercentagePhysical
      : undefined

    // Convert string values to numbers where API expects integers (based on UserProfile type)
    const incomeYearlyBrutto = parsedInput.incomeYearlyBrutto ? parseInt(parsedInput.incomeYearlyBrutto, 10) : undefined

    const incomeYearlyNetto = parsedInput.incomeYearlyNetto ? parseInt(parsedInput.incomeYearlyNetto, 10) : undefined

    const incomeYearlySalaries = parsedInput.incomeYearlySalaries
      ? parseInt(parsedInput.incomeYearlySalaries, 10)
      : undefined

    const capitalFormingPayments = parsedInput.capitalFormingPayments
      ? parseInt(parsedInput.capitalFormingPayments, 10)
      : undefined

    // Prepare profession data structure matching the API expectations
    const professionData = {
      profession: parsedInput.profession,
      jobType: parsedInput.jobType,
      workPercentageOffice,
      workPercentagePhysical: parsedInput.workPercentagePhysical,
      incomeYearlyBrutto,
      incomeYearlyNetto,
      incomeYearlySalaries,
      retirementSavingSince: parsedInput.retirementSavingSince?.toISOString(),
      capitalFormingPayments,
      taxOfficeName: parsedInput.taxOfficeName,
      taxNumber: parsedInput.taxNumber,
      taxId: parsedInput.taxId,
      taxClass: parsedInput.taxClass,
      churchTaxPercentage: parsedInput.churchTaxPercentage,
      healthInsurance: parsedInput.healthInsurance,
      socialInsuranceNumber: parsedInput.socialInsuranceNumber,
    }

    // Update profession via API (following ExtJS pattern using setProfession endpoint)
    const response = await fetchApi(
      '/profile',
      {
        method: 'PUT',
        body: JSON.stringify({ profile: professionData }),
      },
      session
    )

    if (response.error) {
      throw createDetailedError(response.error, response.details)
    }

    revalidateTag(PROFILE_TAGS.PROFILE)

    return response.data
  })

export const revalidateProfession = async () => {
  return revalidateTag(PROFILE_TAGS.PROFILE)
}
