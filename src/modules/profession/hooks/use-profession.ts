import { useAction } from 'next-safe-action/hooks'
import { toast } from 'sonner'
import { useTranslations } from 'next-intl'

import { updateUserProfession } from '../api/profession-actions'

export function useProfessionForm() {
  const t = useTranslations()

  return useAction(updateUserProfession, {
    onSuccess: () => {
      toast.success(t('TOAST_NOTIFICATION.BODY.RECORD_SAVED_SUCCESSFULLY'))
    },
    onError: ({ error }) => {
      console.error('Profession update error:', error)
      toast.error(t('TOAST_NOTIFICATION.BODY.SAVING_RECORD_FAILED'))
    },
  })
}
