'use client'

import type { UserProfile } from '@/modules/auth/types/auth-types'
import { zodResolver } from '@hookform/resolvers/zod'
import { useTranslations } from 'next-intl'
import { useForm } from 'react-hook-form'

import { safeParseDate } from '@/lib/utils'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { Form } from '@/components/ui/form'
import { FormCalendar, FormInput, FormSelect } from '@/components/form-inputs'

import { useProfessionForm } from '../hooks/use-profession'
import {
  churchTaxOptions,
  jobTypeOptions,
  professionSchema,
  taxClassOptions,
  type ProfessionFormInputs,
} from '../types/profession-schema'

interface ProfessionFormProps {
  initialData?: UserProfile | null
}

export function ProfessionForm({ initialData }: ProfessionFormProps) {
  const t = useTranslations()

  const { executeAsync: updateUserProfession, isPending: isSubmitting } = useProfessionForm()

  // Note: Options now use German values directly (like ExtJS) until translations are added to database
  // No need to translate since labels are already in German

  const form = useForm<ProfessionFormInputs>({
    resolver: zodResolver(professionSchema),
    defaultValues: {
      profession: initialData?.profession || '',
      jobType: (initialData?.jobType as any) || undefined,
      workPercentagePhysical: initialData?.workPercentagePhysical || undefined,
      incomeYearlyBrutto: initialData?.incomeYearlyBrutto?.toString() || '',
      incomeYearlyNetto: initialData?.incomeYearlyNetto?.toString() || '',
      incomeYearlySalaries: initialData?.incomeYearlySalaries?.toString() || '',
      retirementSavingSince: safeParseDate(initialData?.retirementSavingSince),

      capitalFormingPayments: initialData?.capitalFormingPayments?.toString() || '',
      taxOfficeName: initialData?.taxOfficeName || '',
      taxNumber: initialData?.taxNumber || '',
      taxId: initialData?.taxId || '',
      taxClass: (initialData?.taxClass as any) || undefined,
      churchTaxPercentage: (initialData?.churchTaxPercentage?.toString() as any) || undefined,
      healthInsurance: initialData?.healthInsurance || '',
      socialInsuranceNumber: initialData?.socialInsuranceNumber || '',
    },
  })

  // Watch physical percentage to calculate office percentage
  const workPercentagePhysical = form.watch('workPercentagePhysical')
  const workPercentageOffice = workPercentagePhysical ? 100 - workPercentagePhysical : undefined

  const onSubmit = async (data: ProfessionFormInputs) => {
    await updateUserProfession(data)
  }

  return (
    <div className="max-w-4xl mx-auto space-y-8">
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
          <Card>
            <CardContent className="pt-6">
              <div className="grid grid-cols-1  gap-6">
                {/* Job Information */}
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold text-gray-900">
                    {t('PROFESSION.EDIT.JOB_INFO_FIELDSET.TITLE')}
                  </h3>
                  <div className="space-y-4">
                    <FormInput<ProfessionFormInputs> name="profession" label="PROFESSION.EDIT.PROFESSION_FIELD.LABEL" />

                    <FormSelect<ProfessionFormInputs>
                      name="jobType"
                      label="PROFESSION.EDIT.JOB_TYPE_FIELD.LABEL"
                      options={jobTypeOptions}
                    />

                    {/* Office Percentage (Read-only, calculated) */}
                    <div className="space-y-2">
                      <label className="text-sm font-medium text-gray-700">
                        {t('PROFESSION.EDIT.OFFICE_FIELD.LABEL')}
                      </label>
                      <div className="relative">
                        <input
                          type="number"
                          value={workPercentageOffice || ''}
                          readOnly
                          className="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50 text-gray-500 cursor-not-allowed"
                        />
                        <span className="absolute right-3 top-2 text-gray-500">%</span>
                      </div>
                    </div>

                    <FormInput<ProfessionFormInputs>
                      name="workPercentagePhysical"
                      label="PROFESSION.EDIT.PHYSICAL_FIELD.LABEL"
                      type="number"
                      min={0}
                      max={100}
                      suffix="%"
                    />
                  </div>
                </div>

                {/* Income Information */}
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold text-gray-900">{t('PROFESSION.EDIT.INCOME_FIELDSET.TITLE')}</h3>
                  <div className="space-y-4">
                    <FormInput<ProfessionFormInputs>
                      name="incomeYearlyBrutto"
                      label="PROFESSION.EDIT.YEARLY_BRUTTO_FIELD.LABEL"
                      placeholder="0"
                      suffix="&euro;"
                    />

                    <FormInput<ProfessionFormInputs>
                      name="incomeYearlyNetto"
                      label="PROFESSION.EDIT.YEARLY_NETTO_FIELD.LABEL"
                      placeholder="0"
                      suffix="&euro;"
                    />

                    <FormInput<ProfessionFormInputs>
                      name="incomeYearlySalaries"
                      label="PROFESSION.EDIT.YEARLY_SALARIES_FIELD.LABEL"
                    />

                    <FormCalendar<ProfessionFormInputs>
                      name="retirementSavingSince"
                      label="PROFESSION.EDIT.SAVING_SINCE_FIELD.LABEL"
                    />

                    <FormInput<ProfessionFormInputs>
                      name="capitalFormingPayments"
                      label="PROFESSION.EDIT.CAP_FORM_PAYMENTS_FIELD.LABEL"
                      placeholder="0"
                      suffix="&euro;"
                    />
                  </div>
                </div>

                {/* Tax Information */}
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold text-gray-900">
                    {t('PROFESSION.EDIT.TAX_INFO_FIELDSET.TITLE')}
                  </h3>
                  <div className="space-y-4">
                    <FormInput<ProfessionFormInputs>
                      name="taxOfficeName"
                      label="PROFESSION.EDIT.TAX_OFFICE_FIELD.LABEL"
                    />

                    <FormInput<ProfessionFormInputs> name="taxNumber" label="PROFESSION.EDIT.TAX_NUMBER_FIELD.LABEL" />

                    <FormInput<ProfessionFormInputs> name="taxId" label="PROFESSION.EDIT.TAX_ID_FIELD.LABEL" />

                    <FormSelect<ProfessionFormInputs>
                      name="taxClass"
                      label="PROFESSION.EDIT.TAX_CLASS_FIELD.LABEL"
                      options={taxClassOptions}
                    />

                    <FormSelect<ProfessionFormInputs>
                      name="churchTaxPercentage"
                      label="PROFESSION.EDIT.CHURCH_TAX_FIELD.LABEL"
                      options={churchTaxOptions}
                    />
                  </div>
                </div>

                {/* Additional Information */}
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold text-gray-900">
                    {t('PROFESSION.EDIT.ADDITIONAL_INFO_FIELDSET.TITLE')}
                  </h3>
                  <div className="space-y-4">
                    <FormInput<ProfessionFormInputs>
                      name="healthInsurance"
                      label="PROFESSION.EDIT.HEALTH_INSURANCE_FIELD.LABEL"
                    />

                    <FormInput<ProfessionFormInputs>
                      name="socialInsuranceNumber"
                      label="PROFESSION.EDIT.SOCIAL_INSURANCE_NUMBER_FIELD.LABEL"
                    />
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Submit Button */}
          <div className="flex justify-end">
            <Button type="submit" disabled={isSubmitting} className="min-w-[120px]">
              {isSubmitting ? t('Loading..') : t('PROFESSION.EDIT.SAVE_BUTTON.TEXT')}
            </Button>
          </div>
        </form>
      </Form>
    </div>
  )
}
