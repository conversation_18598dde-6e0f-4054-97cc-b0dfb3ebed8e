import { Card, CardContent } from '@/components/ui/card'
import { Skeleton } from '@/components/ui/skeleton'

export function ProfessionLoading() {
  return (
    <div className="max-w-4xl w-full mx-auto space-y-8 ">
      <Card>
        <CardContent className="pt-6">
          <div className="grid grid-cols-1 gap-6">
            {/* Job Information Section */}
            <div className="space-y-4">
              <Skeleton className="h-6 w-32" /> {/* Section title */}
              <div className="space-y-4">
                <Skeleton className="h-10 w-full" /> {/* Profession field */}
                <Skeleton className="h-10 w-full" /> {/* Job type field */}
                <Skeleton className="h-10 w-full" /> {/* Office percentage field */}
                <Skeleton className="h-10 w-full" /> {/* Physical percentage field */}
              </div>
            </div>

            {/* Income Information Section */}
            <div className="space-y-4">
              <Skeleton className="h-6 w-32" /> {/* Section title */}
              <div className="space-y-4">
                <Skeleton className="h-10 w-full" /> {/* Yearly brutto field */}
                <Skeleton className="h-10 w-full" /> {/* Yearly netto field */}
                <Skeleton className="h-10 w-full" /> {/* Yearly salaries field */}
                <Skeleton className="h-10 w-full" /> {/* Retirement saving since field */}
                <Skeleton className="h-10 w-full" /> {/* Capital forming payments field */}
              </div>
            </div>

            {/* Tax Information Section */}
            <div className="space-y-4">
              <Skeleton className="h-6 w-32" /> {/* Section title */}
              <div className="space-y-4">
                <Skeleton className="h-10 w-full" /> {/* Tax office field */}
                <Skeleton className="h-10 w-full" /> {/* Tax number field */}
                <Skeleton className="h-10 w-full" /> {/* Tax ID field */}
                <Skeleton className="h-10 w-full" /> {/* Tax class field */}
                <Skeleton className="h-10 w-full" /> {/* Church tax field */}
              </div>
            </div>

            {/* Additional Information Section */}
            <div className="space-y-4">
              <Skeleton className="h-6 w-32" /> {/* Section title */}
              <div className="space-y-4">
                <Skeleton className="h-10 w-full" /> {/* Health insurance field */}
                <Skeleton className="h-10 w-full" /> {/* Social insurance number field */}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Submit Button */}
      <div className="flex justify-end">
        <Skeleton className="h-10 w-32" />
      </div>
    </div>
  )
}
