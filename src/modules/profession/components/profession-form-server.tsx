import { getUserProfile } from '@/modules/profile/api/profile-api'

import { ErrorBoundary } from '@/components/error-boundary'

import { ProfessionForm } from './profession-form'

export async function ProfessionFormServer() {
  const profileResponse = await getUserProfile()

  if (profileResponse?.serverError || !profileResponse?.data) {
    return (
      <ErrorBoundary
        error={profileResponse?.serverError?.message || 'Unknown error'}
        title="Failed to load profession data"
        description="There was an error loading your profession information. Please try again."
      />
    )
  }

  return (
    <div className="flex-1 overflow-y-auto p-4">
      <ProfessionForm initialData={profileResponse?.data} />
    </div>
  )
}
