import { z } from 'zod'

// Job Type enum based on ExtJS options
export const JobTypeEnum = z.enum([
  '<PERSON><PERSON><PERSON><PERSON>',
  '<PERSON><PERSON><PERSON><PERSON>',
  '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>',
  '<PERSON><PERSON><PERSON><PERSON><PERSON>',
  '<PERSON><PERSON><PERSON> auf Lebenszeit',
  '<PERSON><PERSON><PERSON> im höheren Dienst',
  '<PERSON><PERSON><PERSON> auf Widerruf',
  '<PERSON><PERSON><PERSON> auf Probe',
  'Arbeiter öffentl. Dienst',
  'Angestellter öffentl. Dienst',
  'Geschäftsf. Gesellschafter',
  'Vorstand',
  '<PERSON><PERSON><PERSON><PERSON>',
  '<PERSON><PERSON><PERSON>',
  'Student',
  'Wehr-.Zivildienst.FSJ',
  'Hausfrau.Hausmann',
  'In Elternzeit',
  'Rentner.Ruheständler',
  'Arbeitssuchend',
] as const)

export type JobTypeType = z.infer<typeof JobTypeEnum>

export const jobTypeOptions: Array<{ value: JobTypeType; label: string }> = [
  { value: 'Arbeiter', label: '<PERSON>rbeiter' },
  { value: '<PERSON><PERSON><PERSON><PERSON>', label: '<PERSON>estellter' },
  { value: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>', label: '<PERSON><PERSON><PERSON><PERSON><PERSON>ndi<PERSON>' },
  { value: '<PERSON><PERSON><PERSON><PERSON><PERSON>', label: '<PERSON><PERSON><PERSON><PERSON><PERSON>' },
  { value: 'Beamter auf Lebenszeit', label: 'Beamter auf Lebenszeit' },
  { value: 'Beamter im höheren Dienst', label: 'Beamter im höheren Dienst' },
  { value: 'Beamter auf Widerruf', label: 'Beamter auf Widerruf' },
  { value: 'Beamter auf Probe', label: 'Beamter auf Probe' },
  { value: 'Arbeiter öffentl. Dienst', label: 'Arbeiter öffentl. Dienst' },
  { value: 'Angestellter öffentl. Dienst', label: 'Angestellter öffentl. Dienst' },
  { value: 'Geschäftsf. Gesellschafter', label: 'Geschäftsf. Gesellschafter' },
  { value: 'Vorstand', label: 'Vorstand' },
  { value: 'Schüler', label: 'Schüler' },
  { value: 'Azubi', label: 'Azubi' },
  { value: 'Student', label: 'Student' },
  { value: 'Wehr-.Zivildienst.FSJ', label: 'Wehr-.Zivildienst.FSJ' },
  { value: 'Hausfrau.Hausmann', label: 'Hausfrau.Hausmann' },
  { value: 'In Elternzeit', label: 'In Elternzeit' },
  { value: 'Rentner.Ruheständler', label: 'Rentner.Ruheständler' },
  { value: 'Arbeitssuchend', label: 'Arbeitssuchend' },
]

// Tax Class enum based on ExtJS options
export const TaxClassEnum = z.enum(['_', 'I', 'II', 'III', 'IV', 'V', 'VI'] as const)

export type TaxClassType = z.infer<typeof TaxClassEnum>

// TODO: Add these translation keys to the translation database:
// PROFESSION.EDIT.TAX_CLASS_FIELD.OPTION.NONE = "Keine"
// PROFESSION.EDIT.TAX_CLASS_FIELD.OPTION.I = "I"
// PROFESSION.EDIT.TAX_CLASS_FIELD.OPTION.II = "II"
// PROFESSION.EDIT.TAX_CLASS_FIELD.OPTION.III = "III"
// PROFESSION.EDIT.TAX_CLASS_FIELD.OPTION.IV = "IV"
// PROFESSION.EDIT.TAX_CLASS_FIELD.OPTION.V = "V"
// PROFESSION.EDIT.TAX_CLASS_FIELD.OPTION.VI = "VI"

// Temporary: Use German values directly (like ExtJS) until translations are added
export const taxClassOptions: Array<{ value: TaxClassType; label: string }> = [
  { value: '_', label: 'Keine' },
  { value: 'I', label: 'I' },
  { value: 'II', label: 'II' },
  { value: 'III', label: 'III' },
  { value: 'IV', label: 'IV' },
  { value: 'V', label: 'V' },
  { value: 'VI', label: 'VI' },
]

// Church Tax enum based on ExtJS options
export const ChurchTaxEnum = z.enum(['0', '4', '5', '6', '7', '8', '9'] as const)

export type ChurchTaxType = z.infer<typeof ChurchTaxEnum>

// TODO: Add these translation keys to the translation database:
// PROFESSION.EDIT.CHURCH_TAX_FIELD.OPTION.0 = "0%"
// PROFESSION.EDIT.CHURCH_TAX_FIELD.OPTION.4 = "4%"
// PROFESSION.EDIT.CHURCH_TAX_FIELD.OPTION.5 = "5%"
// PROFESSION.EDIT.CHURCH_TAX_FIELD.OPTION.6 = "6%"
// PROFESSION.EDIT.CHURCH_TAX_FIELD.OPTION.7 = "7%"
// PROFESSION.EDIT.CHURCH_TAX_FIELD.OPTION.8 = "8%"
// PROFESSION.EDIT.CHURCH_TAX_FIELD.OPTION.9 = "9%"

// Temporary: Use German values directly (like ExtJS) until translations are added
export const churchTaxOptions: Array<{ value: ChurchTaxType; label: string }> = [
  { value: '0', label: '0%' },
  { value: '4', label: '4%' },
  { value: '5', label: '5%' },
  { value: '6', label: '6%' },
  { value: '7', label: '7%' },
  { value: '8', label: '8%' },
  { value: '9', label: '9%' },
]

// Profession form schema based on ExtJS fields
export const professionSchema = z.object({
  // Job Information
  profession: z.string().min(1, 'this field is required'),
  jobType: JobTypeEnum.nullable(),
  workPercentagePhysical: z.coerce
    .number({
      invalid_type_error: 'PROFESSION.VALIDATION.WORK_PERCENTAGE_INVALID',
    })
    .min(0, 'PROFESSION.VALIDATION.WORK_PERCENTAGE_MIN')
    .max(100, 'PROFESSION.VALIDATION.WORK_PERCENTAGE_MAX')
    .optional(),
  workPercentageOffice: z.number().optional(),

  // Income Information (all input as strings, converted to integers in action for API)
  incomeYearlyBrutto: z.string().min(1, 'this field is required'),
  incomeYearlyNetto: z.string().min(1, 'this field is required'),
  incomeYearlySalaries: z.string().min(1, 'this field is required'),
  retirementSavingSince: z.date().nullable(),
  capitalFormingPayments: z.string().min(1, 'this field is required'),

  // Tax Information
  taxOfficeName: z.string().min(1, 'this field is required'),
  taxNumber: z.string().min(1, 'this field is required'),
  taxId: z.string().min(1, 'this field is required'),
  taxClass: TaxClassEnum.nullable(),
  churchTaxPercentage: ChurchTaxEnum.nullable(),

  // Additional Information
  healthInsurance: z.string().optional(),
  socialInsuranceNumber: z.string().optional(),
})

export type ProfessionFormInputs = z.infer<typeof professionSchema>
