@import 'variables';

@font-face {
  font-family: '#{$icomoon-font-family}';
  src: url('#{$icomoon-font-path}/#{$icomoon-font-family}.eot?rlkq0i');
  src: url('#{$icomoon-font-path}/#{$icomoon-font-family}.eot?rlkq0i#iefix') format('embedded-opentype'),
    url('#{$icomoon-font-path}/#{$icomoon-font-family}.woff2?rlkq0i') format('woff2'),
    url('#{$icomoon-font-path}/#{$icomoon-font-family}.ttf?rlkq0i') format('truetype'),
    url('#{$icomoon-font-path}/#{$icomoon-font-family}.woff?rlkq0i') format('woff'),
    url('#{$icomoon-font-path}/#{$icomoon-font-family}.svg?rlkq0i##{$icomoon-font-family}') format('svg');
  font-weight: normal;
  font-style: normal;
  font-display: block;
}

.icon {
  /* use !important to prevent issues with browser extensions that change fonts */
  font-family: '#{$icomoon-font-family}' !important;
  speak: never;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;

  /* Better Font Rendering =========== */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-add-circle1 .path1 {
  &:before {
    content: $icon-add-circle1-path1;
    color: rgb(20, 42, 58);
  }
}
.icon-add-circle1 .path2 {
  &:before {
    content: $icon-add-circle1-path2;
    margin-left: -1em;
    color: rgb(255, 255, 255);
  }
}
.icon-happyemoji .path1 {
  &:before {
    content: $icon-happyemoji-path1;
    color: rgb(20, 42, 58);
  }
}
.icon-happyemoji .path2 {
  &:before {
    content: $icon-happyemoji-path2;
    margin-left: -1em;
    color: rgb(255, 255, 255);
  }
}
.icon-minus-cirlce .path1 {
  &:before {
    content: $icon-minus-cirlce-path1;
    color: rgb(20, 42, 58);
  }
}
.icon-minus-cirlce .path2 {
  &:before {
    content: $icon-minus-cirlce-path2;
    margin-left: -1em;
    color: rgb(255, 255, 255);
  }
}
.icon-nav-arrow-left {
  &:before {
    content: $icon-nav-arrow-left;
  }
}
.icon-navarrow-right {
  &:before {
    content: $icon-navarrow-right;
    color: #fff;
  }
}
.icon-europe-1 .path1 {
  &:before {
    content: $icon-europe-1-path1;
    color: rgb(20, 42, 58);
  }
}
.icon-europe-1 .path2 {
  &:before {
    content: $icon-europe-1-path2;
    margin-left: -1.375em;
    color: rgb(20, 42, 58);
  }
}
.icon-europe-1 .path3 {
  &:before {
    content: $icon-europe-1-path3;
    margin-left: -1.375em;
    color: rgb(255, 255, 255);
  }
}
.icon-europe .path1 {
  &:before {
    content: $icon-europe-path1;
    color: rgb(20, 42, 58);
  }
}
.icon-europe .path2 {
  &:before {
    content: $icon-europe-path2;
    margin-left: -1.375em;
    color: rgb(20, 42, 58);
  }
}
.icon-europe .path3 {
  &:before {
    content: $icon-europe-path3;
    margin-left: -1.375em;
    color: rgb(255, 255, 255);
  }
}
.icon-nav-arrow-left1 {
  &:before {
    content: $icon-nav-arrow-left1;
  }
}
.icon-navarrow-right1 {
  &:before {
    content: $icon-navarrow-right1;
    color: #fff;
  }
}
.icon-eye {
  &:before {
    content: $icon-eye;
  }
}
.icon-eye-slash {
  &:before {
    content: $icon-eye-slash;
  }
}
.icon-camera {
  &:before {
    content: $icon-camera;
  }
}
.icon-card {
  &:before {
    content: $icon-card;
  }
}
.icon-close {
  &:before {
    content: $icon-close;
  }
}
.icon-crown {
  &:before {
    content: $icon-crown;
  }
}
.icon-danger {
  &:before {
    content: $icon-danger;
  }
}
.icon-directbox-notif {
  &:before {
    content: $icon-directbox-notif;
  }
}
.icon-document-download {
  &:before {
    content: $icon-document-download;
  }
}
.icon-edit {
  &:before {
    content: $icon-edit;
  }
}
.icon-element {
  &:before {
    content: $icon-element;
  }
}
.icon-folder {
  &:before {
    content: $icon-folder;
  }
}
.icon-folder-add {
  &:before {
    content: $icon-folder-add;
  }
}
.icon-gallery {
  &:before {
    content: $icon-gallery;
  }
}
.icon-gift {
  &:before {
    content: $icon-gift;
  }
}
.icon-global {
  &:before {
    content: $icon-global;
  }
}
.icon-home {
  &:before {
    content: $icon-home;
  }
}
.icon-house {
  &:before {
    content: $icon-house;
  }
}
.icon-info-circle {
  &:before {
    content: $icon-info-circle;
  }
}
.icon-information {
  &:before {
    content: $icon-information;
  }
}
.icon-location {
  &:before {
    content: $icon-location;
  }
}
.icon-lock {
  &:before {
    content: $icon-lock;
  }
}
.icon-logout {
  &:before {
    content: $icon-logout;
  }
}
.icon-medal-star {
  &:before {
    content: $icon-medal-star;
  }
}
.icon-menu {
  &:before {
    content: $icon-menu;
  }
}
.icon-mobile {
  &:before {
    content: $icon-mobile;
  }
}
.icon-profile-circle {
  &:before {
    content: $icon-profile-circle;
  }
}
.icon-search-normal {
  &:before {
    content: $icon-search-normal;
  }
}
.icon-security {
  &:before {
    content: $icon-security;
  }
}
.icon-security-safe {
  &:before {
    content: $icon-security-safe;
  }
}
.icon-star {
  &:before {
    content: $icon-star;
  }
}
.icon-tick-circle {
  &:before {
    content: $icon-tick-circle;
  }
}
.icon-tick-square {
  &:before {
    content: $icon-tick-square;
  }
}
.icon-trash {
  &:before {
    content: $icon-trash;
  }
}
.icon-add-circle {
  &:before {
    content: $icon-add-circle;
  }
}
.icon-arrow-down {
  &:before {
    content: $icon-arrow-down;
  }
}
.icon-arrow-left {
  &:before {
    content: $icon-arrow-left;
  }
}
.icon-arrow-right {
  &:before {
    content: $icon-arrow-right;
  }
}
.icon-document {
  &:before {
    content: $icon-document;
  }
}
.icon-receipt {
  &:before {
    content: $icon-receipt;
  }
}
.icon-arrow-down-14 {
  &:before {
    content: $icon-arrow-down-14;
  }
}
.icon-calendar {
  &:before {
    content: $icon-calendar;
  }
}
.icon-call-calling {
  &:before {
    content: $icon-call-calling;
  }
}
.icon-setting {
  &:before {
    content: $icon-setting;
  }
}
.icon-health {
  &:before {
    content: $icon-health;
  }
}
.icon-arrow-up {
  &:before {
    content: $icon-arrow-up;
  }
}
.icon-briefcase {
  &:before {
    content: $icon-briefcase;
  }
}
