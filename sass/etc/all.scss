$icomoon-font-family: "icomoon";
$icomoon-font-path: "fonts";


@font-face {
	font-family: '#{$icomoon-font-family}';
	src:  url('#{$icomoon-font-path}/#{$icomoon-font-family}.eot?8ookz0');
	src:  url('#{$icomoon-font-path}/#{$icomoon-font-family}.eot?8ookz0#iefix') format('embedded-opentype'),
	  url('#{$icomoon-font-path}/#{$icomoon-font-family}.woff2?8ookz0') format('woff2'),
	  url('#{$icomoon-font-path}/#{$icomoon-font-family}.ttf?8ookz0') format('truetype'),
	  url('#{$icomoon-font-path}/#{$icomoon-font-family}.woff?8ookz0') format('woff'),
	  url('#{$icomoon-font-path}/#{$icomoon-font-family}.svg?8ookz0##{$icomoon-font-family}') format('svg');
	font-weight: normal;
	font-style: normal;
	font-display: block;
  }

[class^="icon-"], [class*=" icon-"] {
	font-family: '#{$icomoon-font-family}' !important;
	speak: never;
	font-style: normal;
	font-weight: normal;
	font-variant: normal;
	text-transform: none;

	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
}

.x-body {
	@import 'icomoon/style';
}